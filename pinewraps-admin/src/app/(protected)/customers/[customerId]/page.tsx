'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { toast } from '@/lib/toast';
import { customerService, type Customer, type RewardHistory } from '@/services/customer.service';
import { Loader2, Save, ArrowLeft, Edit, X, Check, ShoppingBag } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Heading } from '@/components/ui/heading';
import { auth } from '@/lib/firebase';

export default function CustomerDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [pointsToAdd, setPointsToAdd] = useState<number>(0);
  const [savingPoints, setSavingPoints] = useState(false);
  const [savingVip, setSavingVip] = useState(false);
  const [rewardPoints, setRewardPoints] = useState(0);
  const [totalPoints, setTotalPoints] = useState(0);
  const [rewardTier, setRewardTier] = useState('BRONZE');
  const [rewardHistory, setRewardHistory] = useState<RewardHistory[]>([]);

  // Name editing states
  const [isEditingName, setIsEditingName] = useState(false);
  const [editFirstName, setEditFirstName] = useState('');
  const [editLastName, setEditLastName] = useState('');
  const [savingName, setSavingName] = useState(false);
  const [nameJustUpdated, setNameJustUpdated] = useState(false);

  useEffect(() => {
    if (params.customerId) {
      loadData();
    }
  }, [params.customerId]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Load customer details
      const customerResponse = await customerService.getCustomer(params.customerId as string);
      setCustomer(customerResponse.data);

      // Initialize edit fields with current customer data
      setEditFirstName(customerResponse.data.firstName || '');
      setEditLastName(customerResponse.data.lastName || '');

      // Load reward details
      const rewardResponse = await customerService.getCustomerReward(params.customerId as string);
      setRewardPoints(rewardResponse.data.points);
      setTotalPoints(rewardResponse.data.totalPoints);
      setRewardTier(rewardResponse.data.tier);

      // Load reward history
      const historyResponse = await customerService.getCustomerRewardHistory(params.customerId as string);
      setRewardHistory(historyResponse.data.history);
    } catch (error) {
      console.error('Error loading data:', error);
      setError('Failed to load customer details');
    } finally {
      setLoading(false);
    }
  };

  const handleAddPoints = async () => {
    if (!params.customerId) return;

    try {
      setSavingPoints(true);
      const token = await auth.currentUser?.getIdToken();
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/rewards/${params.customerId as string}/add`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ 
          points: pointsToAdd,
          description: 'Points added by admin'
        })
      });

      if (!response.ok) {
        throw new Error('Failed to add points');
      }

      // Reload reward data after adding points
      const rewardResponse = await customerService.getCustomerReward(params.customerId as string);
      setRewardPoints(rewardResponse.data.points);
      setTotalPoints(rewardResponse.data.totalPoints);
      setRewardTier(rewardResponse.data.tier);

      // Reload history
      const historyResponse = await customerService.getCustomerRewardHistory(params.customerId as string);
      setRewardHistory(historyResponse.data.history);

      setPointsToAdd(0);
      toast.success('Points added successfully');
    } catch (error) {
      console.error('Error adding points:', error);
      toast.error('Failed to add points');
    } finally {
      setSavingPoints(false);
    }
  };

  const handleVipToggle = async (isVip: boolean) => {
    if (!customer) return;

    try {
      setSavingVip(true);
      const token = await auth.currentUser?.getIdToken();
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/customers/${customer.id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          isVip
        })
      });

      if (!response.ok) {
        throw new Error('Failed to update VIP status');
      }

      const data = await response.json();
      setCustomer(data.data);
      toast.success(isVip ? 'Customer marked as VIP' : 'Customer removed from VIP');
    } catch (error) {
      console.error('Error updating VIP status:', error);
      toast.error('Failed to update VIP status');
    } finally {
      setSavingVip(false);
    }
  };

  const handleSaveName = async () => {
    if (!customer) return;

    // Validate that names are not empty and contain valid characters
    const trimmedFirstName = editFirstName.trim();
    const trimmedLastName = editLastName.trim();

    if (!trimmedFirstName || !trimmedLastName) {
      toast.error('First name and last name are required');
      return;
    }

    if (trimmedFirstName.length < 2 || trimmedLastName.length < 2) {
      toast.error('First name and last name must be at least 2 characters long');
      return;
    }

    // Check if the names have actually changed
    if (trimmedFirstName === customer.firstName && trimmedLastName === customer.lastName) {
      setIsEditingName(false);
      return;
    }

    try {
      setSavingName(true);

      // Update customer using the service
      const response = await customerService.updateCustomer(customer.id, {
        firstName: trimmedFirstName,
        lastName: trimmedLastName
      });

      // Update local state
      setCustomer(response.data);
      setIsEditingName(false);
      setNameJustUpdated(true);
      toast.success('Customer name updated successfully');

      // Hide the "just updated" indicator after 3 seconds
      setTimeout(() => {
        setNameJustUpdated(false);
      }, 3000);
    } catch (error) {
      console.error('Error updating customer name:', error);
      toast.error('Failed to update customer name');
    } finally {
      setSavingName(false);
    }
  };

  const handleCancelEdit = () => {
    // Reset edit fields to current customer data
    setEditFirstName(customer?.firstName || '');
    setEditLastName(customer?.lastName || '');
    setIsEditingName(false);
  };

  return (
    <div className="flex-1 space-y-4">
      <div className="flex items-center justify-between">
        <Heading title="Customer Details" description="View and manage customer information" />
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => router.push(`/customers/${params.customerId}/orders`)}
          >
            <ShoppingBag className="mr-2 h-4 w-4" />
            View Orders
          </Button>
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
        </div>
      </div>
      <Separator />
      
      {loading ? (
        <div className="flex h-[400px] items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      ) : error ? (
        <div className="flex h-[400px] flex-col items-center justify-center gap-4">
          <p className="text-muted-foreground">{error}</p>
          <Button onClick={loadData}>Try Again</Button>
        </div>
      ) : customer && (
        <div className="grid gap-6 md:grid-cols-2">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Basic Information</CardTitle>
                  <CardDescription>Customer's personal details</CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <Label htmlFor="vip-status" className={customer?.isVip ? 'text-primary' : ''}>
                    VIP Status
                  </Label>
                  <Switch
                    id="vip-status"
                    checked={customer?.isVip || false}
                    onCheckedChange={handleVipToggle}
                    disabled={loading || savingVip}
                  />
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Name Section */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-sm font-medium">Customer Name</Label>
                    {!isEditingName ? (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setIsEditingName(true)}
                        className="h-8 px-3"
                      >
                        <Edit className="h-3 w-3 mr-1" />
                        Edit Name
                      </Button>
                    ) : (
                      <div className="flex gap-2">
                        <Button
                          variant="default"
                          size="sm"
                          onClick={handleSaveName}
                          disabled={savingName}
                          className="h-8 px-3"
                        >
                          {savingName ? (
                            <>
                              <Loader2 className="h-3 w-3 animate-spin mr-1" />
                              Saving...
                            </>
                          ) : (
                            <>
                              <Check className="h-3 w-3 mr-1" />
                              Save
                            </>
                          )}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleCancelEdit}
                          disabled={savingName}
                          className="h-8 px-3"
                        >
                          <X className="h-3 w-3 mr-1" />
                          Cancel
                        </Button>
                      </div>
                    )}
                  </div>

                  {!isEditingName ? (
                    <div className="flex items-center gap-2">
                      <div className="text-lg font-medium">
                        {customer?.firstName} {customer?.lastName}
                      </div>
                      {customer?.isVip && (
                        <Badge variant="default">
                          VIP
                        </Badge>
                      )}
                      {nameJustUpdated && (
                        <Badge variant="secondary" className="animate-pulse">
                          Updated
                        </Badge>
                      )}
                    </div>
                  ) : (
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <Label className="text-xs text-muted-foreground">First Name</Label>
                        <Input
                          value={editFirstName}
                          onChange={(e) => setEditFirstName(e.target.value)}
                          placeholder="Enter first name"
                          disabled={savingName}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              handleSaveName();
                            } else if (e.key === 'Escape') {
                              handleCancelEdit();
                            }
                          }}
                          autoFocus
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label className="text-xs text-muted-foreground">Last Name</Label>
                        <Input
                          value={editLastName}
                          onChange={(e) => setEditLastName(e.target.value)}
                          placeholder="Enter last name"
                          disabled={savingName}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              handleSaveName();
                            } else if (e.key === 'Escape') {
                              handleCancelEdit();
                            }
                          }}
                          className="mt-1"
                        />
                      </div>
                    </div>
                  )}
                </div>

                {/* Email Section */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Email Address</Label>
                  <Input value={customer.email} readOnly className="bg-muted" />
                </div>

                {/* Phone Section */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Phone Number</Label>
                  <Input
                    value={customer.phone || 'Not provided'}
                    readOnly
                    className="bg-muted"
                    placeholder="No phone number provided"
                  />
                </div>

                {/* Birth Date Section */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Date of Birth</Label>
                  <Input
                    type="date"
                    value={customer.birthDate ? new Date(customer.birthDate).toISOString().split('T')[0] : ''}
                    readOnly
                    className="bg-muted"
                    placeholder="No birth date provided"
                  />
                </div>

                <Separator className="my-4" />

                {/* Rewards Section */}
                <div className="space-y-4">
                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label>Current Points</Label>
                      <div className="text-2xl font-bold">
                        {rewardPoints}
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label>Total Points</Label>
                      <div className="text-2xl font-bold text-muted-foreground">
                        {totalPoints}
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label>Tier</Label>
                      <div className="space-y-2">
                        <Badge variant="outline" className="text-lg px-3 py-1">
                          {rewardTier}
                        </Badge>
                        <p className="text-sm text-muted-foreground">
                          {rewardTier === 'BRONZE' && 'Earn 7% points on all orders'}
                          {rewardTier === 'SILVER' && 'Earn 12% points on all orders + Free shipping on orders over 50 AED'}
                          {rewardTier === 'GOLD' && 'Earn 15% points on all orders + Free shipping on all orders'}
                          {rewardTier === 'PLATINUM' && 'Earn 20% points on all orders + Free shipping + Priority support'}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Add Points Form */}
                  <div className="space-y-4">
                    <Label>Add Points</Label>
                    <div className="flex gap-4">
                      <div className="flex-1">
                        <Input
                          type="number"
                          min="0"
                          value={pointsToAdd}
                          onChange={(e) => setPointsToAdd(parseInt(e.target.value) || 0)}
                          placeholder="Enter points to add"
                        />
                      </div>
                      <Button 
                        onClick={handleAddPoints} 
                        disabled={pointsToAdd <= 0 || savingPoints}
                      >
                        {savingPoints ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Adding...
                          </>
                        ) : (
                          <>
                            <Save className="mr-2 h-4 w-4" />
                            Add Points
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Notifications */}
          <Card>
            <CardHeader>
              <CardTitle>Notification Preferences</CardTitle>
              <CardDescription>Customer's notification settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Order Updates</Label>
                    <div className="text-sm text-muted-foreground">
                      Receive updates about order status
                    </div>
                  </div>
                  <Switch checked={customer.notifications?.orderUpdates} disabled />
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Promotions</Label>
                    <div className="text-sm text-muted-foreground">
                      Receive promotional offers and deals
                    </div>
                  </div>
                  <Switch checked={customer.notifications?.promotions} disabled />
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Newsletter</Label>
                    <div className="text-sm text-muted-foreground">
                      Subscribe to our newsletter
                    </div>
                  </div>
                  <Switch checked={customer.notifications?.newsletter} disabled />
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>SMS Updates</Label>
                    <div className="text-sm text-muted-foreground">
                      Receive updates via SMS
                    </div>
                  </div>
                  <Switch checked={customer.notifications?.sms} disabled />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Points History */}
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle>Points History</CardTitle>
              <CardDescription>History of points earned and redeemed</CardDescription>
            </CardHeader>
            <CardContent>
              {rewardHistory.length > 0 ? (
                <div className="space-y-6">
                  {/* Points Summary */}
                  <div className="grid grid-cols-2 gap-4 p-4 bg-muted/50 rounded-lg">
                    <div>
                      <div className="text-sm text-muted-foreground mb-1">Total Points Earned</div>
                      <div className="text-2xl font-bold text-green-600">
                        +{rewardHistory.reduce((sum, record) => sum + (record.pointsEarned || 0), 0)}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground mb-1">Total Points Redeemed</div>
                      <div className="text-2xl font-bold text-red-600">
                        -{rewardHistory.reduce((sum, record) => sum + (record.pointsRedeemed || 0), 0)}
                      </div>
                    </div>
                  </div>

                  {/* History List */}
                  <div className="space-y-4">
                    {rewardHistory.map((record) => (
                      <div
                        key={record.id}
                        className="flex items-center justify-between rounded-lg border p-4"
                      >
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            {record.pointsEarned > 0 && (
                              <span className="text-green-600 font-medium">
                                +{record.pointsEarned} earned
                              </span>
                            )}
                            {record.pointsRedeemed > 0 && (
                              <span className="text-red-600 font-medium">
                                -{record.pointsRedeemed} redeemed
                              </span>
                            )}
                            {record.orderTotal > 0 && (
                              <span className="text-sm text-muted-foreground">
                                (Order: AED {record.orderTotal})
                              </span>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {record.description}
                          </p>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {new Date(record.createdAt).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center py-6 text-muted-foreground">
                  No points history available
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
