{"git.enableSmartCommit": true, "git.confirmSync": false, "git.autofetch": true, "git.autoStash": true, "git.fetchOnPull": true, "git.branchPrefix": "feature/", "git.enableCommitSigning": false, "git.untrackedChanges": "mixed", "git.showActionButton": {"commit": true, "publish": true, "sync": true}, "javascript.experimental.updateImportsOnPaste": true, "javascript.format.insertSpaceAfterConstructor": true, "git.ignoreLimitWarning": true, "kiroAgent.configureMCP": "Disabled"}