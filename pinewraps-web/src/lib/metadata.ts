import { Metadata } from 'next';

// Base URL configuration
export const getBaseUrl = (): string => {
  return process.env.NEXT_PUBLIC_SITE_URL || 'https://pinewraps.com';
};

// Default metadata configuration
export const defaultMetadata = {
  siteName: 'Pinewraps Dubai',
  title: 'Pinewraps - Premium Cakes & Flower Gifts in Dubai',
  description: 'Discover exquisite cakes and beautiful flower arrangements for your special occasions. We deliver premium gifting experiences across Dubai. Order online for same-day delivery.',
  keywords: 'cakes dubai, flower gifts dubai, cake delivery, flower delivery, birthday cakes, anniversary gifts, premium cakes, flower bouquets, same day delivery dubai',
  ogImage: '/og-image.jpg',
  twitterHandle: '@pinewraps',
};

// Generate canonical URL
export const generateCanonicalUrl = (path: string): string => {
  const baseUrl = getBaseUrl();
  const cleanPath = path.startsWith('/') ? path : `/${path}`;
  return `${baseUrl}${cleanPath}`;
};

// Create base metadata with canonical URL
export const createMetadata = (options: {
  title?: string;
  description?: string;
  keywords?: string;
  path: string;
  ogImage?: string;
  noIndex?: boolean;
}): Metadata => {
  const canonicalUrl = generateCanonicalUrl(options.path);
  
  return {
    title: options.title || defaultMetadata.title,
    description: options.description || defaultMetadata.description,
    keywords: options.keywords || defaultMetadata.keywords,
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      title: options.title || defaultMetadata.title,
      description: options.description || defaultMetadata.description,
      url: canonicalUrl,
      siteName: defaultMetadata.siteName,
      images: [
        {
          url: options.ogImage || defaultMetadata.ogImage,
          width: 1200,
          height: 630,
          alt: options.title || defaultMetadata.title,
        },
      ],
      type: 'website',
      locale: 'en_AE',
    },
    twitter: {
      card: 'summary_large_image',
      title: options.title || defaultMetadata.title,
      description: options.description || defaultMetadata.description,
      site: defaultMetadata.twitterHandle,
      creator: defaultMetadata.twitterHandle,
      images: [options.ogImage || defaultMetadata.ogImage],
    },
    robots: options.noIndex ? {
      index: false,
      follow: false,
    } : {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };
};

// Product metadata generator
export const createProductMetadata = (product: {
  name: string;
  description?: string;
  metaTitle?: string;
  metaDescription?: string;
  metaKeywords?: string;
  slug: string;
  images?: Array<{ url: string; alt?: string }>;
}): Metadata => {
  const title = product.metaTitle || `${product.name} | Pinewraps Dubai`;
  const description = product.metaDescription || 
    (product.description ? product.description.replace(/<[^>]*>/g, '').substring(0, 160) : defaultMetadata.description);
  
  return createMetadata({
    title,
    description,
    keywords: product.metaKeywords,
    path: `/shop/${product.slug}`,
    ogImage: product.images?.[0]?.url,
  });
};

// Collection metadata generator
export const createCollectionMetadata = (collection: {
  name: string;
  description?: string;
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
  slug: string;
  image?: string;
}): Metadata => {
  const title = collection.seoTitle || `${collection.name} | Pinewraps Dubai`;
  const description = collection.seoDescription || collection.description || defaultMetadata.description;
  
  return createMetadata({
    title,
    description,
    keywords: collection.seoKeywords?.join(', '),
    path: `/collections/${collection.slug}`,
    ogImage: collection.image,
  });
};

// Blog post metadata generator
export const createBlogPostMetadata = (post: {
  title: string;
  excerpt?: string;
  metaTitle?: string;
  metaDescription?: string;
  slug: string;
  featuredImage?: string;
}): Metadata => {
  const title = post.metaTitle || `${post.title} | Pinewraps Blog`;
  const description = post.metaDescription || post.excerpt || defaultMetadata.description;
  
  return createMetadata({
    title,
    description,
    path: `/blog/${post.slug}`,
    ogImage: post.featuredImage,
  });
};
