'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Heading } from '@/components/ui/heading';
import { Separator } from '@/components/ui/separator';
import { FileText, Printer, Send } from 'lucide-react';
import api from '@/lib/api';
import { formatDate } from '@/lib/utils';
import { getFirebaseToken } from '@/lib/firebase'; // Import getFirebaseToken function
import toast from 'react-hot-toast'; // Import toast

export default function PrintPurchaseOrderPage() {
  const params = useParams();
  const [purchaseOrder, setPurchaseOrder] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadPurchaseOrder();
  }, [params.id]);

  const loadPurchaseOrder = async () => {
    try {
      setLoading(true);
      const response = await api.get(`/api/purchase-orders/${params.id}`);
      setPurchaseOrder(response.data.data);
    } catch (error) {
      console.error('Error loading purchase order:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePrint = () => {
    window.print();
  };

  const handleDownloadPDF = async () => {
    try {
      const token = await getFirebaseToken();
      window.open(`/api/purchase-orders/${params.id}/pdf?token=${token}`, '_blank');
    } catch (error) {
      console.error('Error downloading PDF:', error);
      toast.error('Failed to download PDF. Please try again.');
    }
  };

  if (loading || !purchaseOrder) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      <div className="flex-1 space-y-4 p-8 pt-6 print:p-0">
        {/* Header - Hide in print */}
        <div className="print:hidden">
          <div className="flex items-center justify-between">
            <Heading title="Purchase Order" description="View and print purchase order" />
            <div className="flex items-center gap-4">
              <Button onClick={handleDownloadPDF}>
                <FileText className="mr-2 h-4 w-4" />
                Download PDF
              </Button>
              <Button onClick={handlePrint}>
                <Printer className="mr-2 h-4 w-4" />
                Print
              </Button>
            </div>
          </div>
          <Separator className="my-4" />
        </div>

        {/* Purchase Order Content */}
        <div className="space-y-6 print:space-y-4">
          {/* Company Header */}
          <div className="flex justify-between">
            <div>
              <h1 className="text-2xl font-bold">Pinewraps</h1>
              <p>Your Address Line 1</p>
              <p>Your Address Line 2</p>
              <p>Phone: Your Phone</p>
              <p>Email: Your Email</p>
            </div>
            <div className="text-right">
              <h2 className="text-xl font-semibold">Purchase Order</h2>
              <p>PO Number: {purchaseOrder.orderNumber}</p>
              <p>Date: {formatDate(purchaseOrder.date)}</p>
              <p>Expected Delivery: {formatDate(purchaseOrder.expectedDeliveryDate)}</p>
            </div>
          </div>

          {/* Supplier Info */}
          <div className="rounded-lg border p-4">
            <h3 className="font-semibold">Supplier</h3>
            <p>{purchaseOrder.supplier.name}</p>
            <p>{purchaseOrder.supplier.address}</p>
            <p>Phone: {purchaseOrder.supplier.phone}</p>
            <p>Email: {purchaseOrder.supplier.email}</p>
          </div>

          {/* Items Table */}
          <div className="rounded-lg border">
            <table className="w-full">
              <thead className="border-b bg-gray-50">
                <tr>
                  <th className="p-2 text-left">Item</th>
                  <th className="p-2 text-right">Quantity</th>
                  <th className="p-2 text-right">Unit Price</th>
                  <th className="p-2 text-right">Total</th>
                </tr>
              </thead>
              <tbody>
                {purchaseOrder.items.map((item: any) => (
                  <tr key={item.id} className="border-b">
                    <td className="p-2">{item.item.name}</td>
                    <td className="p-2 text-right">{item.quantity}</td>
                    <td className="p-2 text-right">
                      {new Intl.NumberFormat('en-US', {
                        style: 'currency',
                        currency: 'AED',
                      }).format(item.unitPrice)}
                    </td>
                    <td className="p-2 text-right">
                      {new Intl.NumberFormat('en-US', {
                        style: 'currency',
                        currency: 'AED',
                      }).format(item.total)}
                    </td>
                  </tr>
                ))}
              </tbody>
              <tfoot className="border-t bg-gray-50 font-semibold">
                <tr>
                  <td colSpan={3} className="p-2 text-right">
                    Subtotal:
                  </td>
                  <td className="p-2 text-right">
                    {new Intl.NumberFormat('en-US', {
                      style: 'currency',
                      currency: 'AED',
                    }).format(purchaseOrder.subtotal)}
                  </td>
                </tr>
                <tr>
                  <td colSpan={3} className="p-2 text-right">
                    Tax:
                  </td>
                  <td className="p-2 text-right">
                    {new Intl.NumberFormat('en-US', {
                      style: 'currency',
                      currency: 'AED',
                    }).format(purchaseOrder.tax)}
                  </td>
                </tr>
                <tr>
                  <td colSpan={3} className="p-2 text-right">
                    Additional Charge:
                  </td>
                  <td className="p-2 text-right">
                    {new Intl.NumberFormat('en-US', {
                      style: 'currency',
                      currency: 'AED',
                    }).format(purchaseOrder.additionalCharge)}
                  </td>
                </tr>
                <tr>
                  <td colSpan={3} className="p-2 text-right">
                    Total:
                  </td>
                  <td className="p-2 text-right">
                    {new Intl.NumberFormat('en-US', {
                      style: 'currency',
                      currency: 'AED',
                    }).format(purchaseOrder.total)}
                  </td>
                </tr>
              </tfoot>
            </table>
          </div>

          {/* Additional Info */}
          <div className="space-y-4">
            <div>
              <h3 className="font-semibold">Payment Terms</h3>
              <p>{purchaseOrder.paymentTerms.replace('_', ' ')}</p>
            </div>
            {purchaseOrder.notes && (
              <div>
                <h3 className="font-semibold">Notes</h3>
                <p>{purchaseOrder.notes}</p>
              </div>
            )}
          </div>

          {/* Signatures */}
          <div className="mt-8 grid grid-cols-2 gap-8">
            <div>
              <p className="font-semibold">Authorized By:</p>
              <div className="mt-4 border-t border-dashed pt-2">
                <p>Name & Signature</p>
                <p>Date: </p>
              </div>
            </div>
            <div>
              <p className="font-semibold">Received By:</p>
              <div className="mt-4 border-t border-dashed pt-2">
                <p>Name & Signature</p>
                <p>Date: </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
