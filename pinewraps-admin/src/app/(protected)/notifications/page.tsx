'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Bell, Send, Users, User, Award, Crown, Search } from 'lucide-react';
import { toast } from '@/lib/toast';
import { cn } from '@/lib/utils';
import { 
  sendPushNotification, 
  SendPushNotificationParams,
  getPushNotifications
} from '@/lib/utils/notifications';
import { getCustomers } from '@/lib/utils/customers';
import debounce from 'lodash/debounce';

type NotificationType = 'ALL' | 'SPECIFIC_CUSTOMERS' | 'REWARD_TIER' | 'VIP';

interface CustomerType {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
}

export default function NotificationsPage() {
  const [notificationType, setNotificationType] = useState<NotificationType>('ALL');
  const [title, setTitle] = useState('');
  const [message, setMessage] = useState('');
  const [selectedCustomers, setSelectedCustomers] = useState<string[]>([]);
  const [selectedRewardTier, setSelectedRewardTier] = useState<string>('GREEN');
  const [isSending, setIsSending] = useState(false);
  const [customers, setCustomers] = useState<CustomerType[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [totalCustomers, setTotalCustomers] = useState(0);
  const [page, setPage] = useState(1);
  const [limit] = useState(20);

  // Fetch customers when search query changes
  useEffect(() => {
    const fetchCustomers = async () => {
      if (notificationType !== 'SPECIFIC_CUSTOMERS') return;
      
      setIsLoading(true);
      try {
        const response = await getCustomers(page, limit, searchQuery);
        if (response.success) {
          const customerData = searchQuery 
            ? response.data 
            : response.data.customers;
            
          setCustomers(customerData);
          
          if (!searchQuery && response.data.pagination) {
            setTotalCustomers(response.data.pagination.total);
          }
        } else {
          toast.error('Failed to load customers');
        }
      } catch (error) {
        console.error('Error fetching customers:', error);
        toast.error('Failed to load customers');
      } finally {
        setIsLoading(false);
      }
    };

    // Use debounce for search to avoid too many requests
    const debouncedFetch = debounce(fetchCustomers, 300);
    debouncedFetch();
    
    // Cleanup
    return () => {
      debouncedFetch.cancel();
    };
  }, [notificationType, searchQuery, page, limit]);

  const handleSendNotification = async () => {
    if (!title || !message || !notificationType) {
      toast.error('Please fill in all required fields');
      return;
    }

    setIsSending(true);
    try {
      toast.loading(
        <div>
          <strong>Sending notification...</strong>
          <p>This may take a moment</p>
        </div>,
        { id: 'sending-notification' }
      );

      // Prepare target data based on notification type
      let targetData = {};
      
      if (notificationType === 'SPECIFIC_CUSTOMERS') {
        targetData = { customerIds: selectedCustomers };
      } else if (notificationType === 'REWARD_TIER') {
        targetData = { tier: selectedRewardTier };
      }
      
      const result = await sendPushNotification({
        title,
        message,
        targetType: notificationType as 'ALL' | 'VIP' | 'REWARD_TIER' | 'SPECIFIC_CUSTOMERS',
        targetData
      });
      
      toast.dismiss('sending-notification');
      
      if (result.success) {
        toast.success(
          <div>
            <strong>Notification sent successfully!</strong>
            {result.data?.recipientCount > 0 ? (
              <p>Sent to {result.data.recipientCount} recipients</p>
            ) : (
              <p>No recipients with FCM tokens were found. Make sure customers have registered for push notifications.</p>
            )}
          </div>
        );
        
        // Reset form
        setTitle('');
        setMessage('');
        setNotificationType('ALL');
        setSelectedCustomers([]);
        setSelectedRewardTier(null);
      } else {
        toast.error(
          <div>
            <strong>Failed to send notification</strong>
            <p>{result.error || 'An unexpected error occurred'}</p>
          </div>
        );
      }
    } catch (error) {
      console.error('Error sending notification:', error);
      
      toast.dismiss('sending-notification');
      toast.error(
        <div>
          <strong>Failed to send notification</strong>
          <p>{error instanceof Error ? error.message : 'An unexpected error occurred'}</p>
        </div>
      );
    } finally {
      setIsSending(false);
    }
  };

  const toggleCustomerSelection = (customerId: string) => {
    setSelectedCustomers(prev => 
      prev.includes(customerId)
        ? prev.filter(id => id !== customerId)
        : [...prev, customerId]
    );
  };

  return (
    <div className="p-6 max-w-[1400px] mx-auto">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-gray-900">Send Push Notifications</h1>
        <p className="text-sm text-gray-500 mt-1">
          Send notifications to all customers, VIP customers, or specific reward tiers
        </p>
      </div>

      {/* Notification Form */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2 space-y-6">
          <Card className="p-6">
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-700">
                  Recipients
                </label>
                <Select 
                  value={notificationType} 
                  onValueChange={(value: NotificationType) => {
                    setNotificationType(value);
                    setSelectedCustomers([]);
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select recipients" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ALL">All Customers</SelectItem>
                    <SelectItem value="VIP">VIP Customers</SelectItem>
                    <SelectItem value="REWARD_TIER">Specific Reward Tier</SelectItem>
                    <SelectItem value="SPECIFIC_CUSTOMERS">Specific Customers</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {notificationType === 'REWARD_TIER' && (
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Reward Tier
                  </label>
                  <Select 
                    value={selectedRewardTier} 
                    onValueChange={setSelectedRewardTier}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select reward tier" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="GREEN">Green Tier</SelectItem>
                      <SelectItem value="SILVER">Silver Tier</SelectItem>
                      <SelectItem value="GOLD">Gold Tier</SelectItem>
                      <SelectItem value="PLATINUM">Platinum Tier</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}

              <div>
                <label className="text-sm font-medium text-gray-700">
                  Notification Title
                </label>
                <Input
                  placeholder="Enter notification title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                />
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700">
                  Message
                </label>
                <Textarea
                  placeholder="Enter your notification message..."
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  className="min-h-[120px]"
                />
              </div>

              <Button
                className="w-full"
                onClick={handleSendNotification}
                disabled={isSending}
              >
                {isSending ? (
                  <>
                    <svg className="animate-spin h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Sending...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Send Notification
                  </>
                )}
              </Button>
            </div>
          </Card>

          {/* Preview Card */}
          <Card className="p-6">
            <h2 className="text-sm font-medium text-gray-700 mb-4">Preview</h2>
            <div className="border rounded-lg p-4 bg-gray-50">
              <div className="flex items-start space-x-3">
                <div className="p-2 rounded-full bg-blue-100">
                  <Bell className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">
                    {title || 'Notification Title'}
                  </p>
                  <p className="text-sm text-gray-500 mt-1">
                    {message || 'Notification message will appear here'}
                  </p>
                </div>
              </div>
            </div>
          </Card>
        </div>

        {/* User Selection or Reward Tier Info */}
        {notificationType === 'SPECIFIC_CUSTOMERS' ? (
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-sm font-medium text-gray-700">Select Customers</h2>
              <span className="text-xs text-gray-500">
                {selectedCustomers.length} selected
              </span>
            </div>
            <div className="space-y-2">
              <Input
                type="search"
                placeholder="Search customers"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full"
              />
              {isLoading ? (
                <div className="flex justify-center py-8">
                  <svg className="animate-spin h-6 w-6 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                </div>
              ) : customers.length === 0 ? (
                <div className="text-center py-8 text-gray-500">No customers found</div>
              ) : (
                <>
                  <div className="space-y-2 max-h-[400px] overflow-y-auto">
                    {customers.map((customer) => (
                      <div
                        key={customer.id}
                        className={cn(
                          'flex items-center justify-between p-3 rounded-lg cursor-pointer transition-colors',
                          selectedCustomers.includes(customer.id)
                            ? 'bg-blue-50 border border-blue-200'
                            : 'bg-white border border-gray-200 hover:bg-gray-50'
                        )}
                        onClick={() => toggleCustomerSelection(customer.id)}
                      >
                        <div className="flex items-center space-x-3">
                          <div className="p-2 rounded-full bg-gray-100">
                            <User className="h-4 w-4 text-gray-600" />
                          </div>
                          <div>
                            <p className="font-medium text-gray-900">
                              {customer.firstName} {customer.lastName}
                            </p>
                            <p className="text-xs text-gray-500">{customer.email}</p>
                          </div>
                        </div>
                        <div>
                          {selectedCustomers.includes(customer.id) ? (
                            <div className="h-5 w-5 rounded-full bg-blue-500 flex items-center justify-center">
                              <svg className="h-3 w-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                              </svg>
                            </div>
                          ) : (
                            <div className="h-5 w-5 rounded-full border-2 border-gray-300"></div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  {/* Pagination */}
                  {!searchQuery && totalCustomers > limit && (
                    <div className="flex items-center justify-between mt-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setPage(p => Math.max(1, p - 1))}
                        disabled={page === 1}
                      >
                        Previous
                      </Button>
                      <span className="text-sm text-gray-500">
                        Page {page} of {Math.ceil(totalCustomers / limit)}
                      </span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setPage(p => p + 1)}
                        disabled={page >= Math.ceil(totalCustomers / limit)}
                      >
                        Next
                      </Button>
                    </div>
                  )}
                </>
              )}
            </div>
          </Card>
        ) : notificationType === 'REWARD_TIER' ? (
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-sm font-medium text-gray-700">Reward Tier Information</h2>
            </div>
            <div className="space-y-4">
              <div className="flex items-center space-x-3 p-3 rounded-lg bg-gray-50 border border-gray-200">
                <div className="p-2 rounded-full bg-green-100">
                  <Award className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">Green Tier</p>
                  <p className="text-xs text-gray-500">New customers</p>
                </div>
              </div>
              <div className="flex items-center space-x-3 p-3 rounded-lg bg-gray-50 border border-gray-200">
                <div className="p-2 rounded-full bg-gray-300">
                  <Award className="h-5 w-5 text-gray-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">Silver Tier</p>
                  <p className="text-xs text-gray-500">Regular customers</p>
                </div>
              </div>
              <div className="flex items-center space-x-3 p-3 rounded-lg bg-gray-50 border border-gray-200">
                <div className="p-2 rounded-full bg-yellow-100">
                  <Award className="h-5 w-5 text-yellow-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">Gold Tier</p>
                  <p className="text-xs text-gray-500">Loyal customers</p>
                </div>
              </div>
              <div className="flex items-center space-x-3 p-3 rounded-lg bg-gray-50 border border-gray-200">
                <div className="p-2 rounded-full bg-purple-100">
                  <Crown className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">Platinum Tier</p>
                  <p className="text-xs text-gray-500">Premium customers</p>
                </div>
              </div>
            </div>
          </Card>
        ) : notificationType === 'VIP' ? (
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-sm font-medium text-gray-700">VIP Customers</h2>
            </div>
            <div className="space-y-4">
              <div className="flex items-center space-x-3 p-3 rounded-lg bg-gray-50 border border-gray-200">
                <div className="p-2 rounded-full bg-purple-100">
                  <Crown className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">VIP Customers</p>
                  <p className="text-xs text-gray-500">Send to all customers marked as VIP</p>
                </div>
              </div>
            </div>
          </Card>
        ) : (
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-sm font-medium text-gray-700">All Customers</h2>
            </div>
            <div className="space-y-4">
              <div className="flex items-center space-x-3 p-3 rounded-lg bg-gray-50 border border-gray-200">
                <div className="p-2 rounded-full bg-blue-100">
                  <Users className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">All Customers</p>
                  <p className="text-xs text-gray-500">Send to all customers with registered devices</p>
                </div>
              </div>
            </div>
          </Card>
        )}
      </div>
    </div>
  );
}
