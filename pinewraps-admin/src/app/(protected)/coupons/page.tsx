"use client";

import { useEffect, useState } from "react";
import { Plus } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { DataTable } from "@/components/ui/data-table";
import { Heading } from "@/components/ui/heading";
import { Separator } from "@/components/ui/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { columns } from "@/components/coupons/columns";
import { CouponForm } from "@/components/coupons/coupon-form";
import { useCoupons, useUpdateCoupon, useDeleteCoupon, useCreateCoupon } from "@/hooks/use-coupons";
import { Coupon, CouponStatus } from "@/lib/api/coupons";

const CouponsPage = () => {
  const [loading, setLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedCoupon, setSelectedCoupon] = useState<Coupon | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  
  const { data: coupons, isLoading } = useCoupons();
  const { mutate: updateCoupon } = useUpdateCoupon();
  const { mutate: deleteCoupon } = useDeleteCoupon();
  const { mutate: createCoupon } = useCreateCoupon();

  useEffect(() => {
    if (!isLoading) {
      setLoading(false);
    }
  }, [isLoading]);

  const handleSubmit = async (data: any) => {
    try {
      if (selectedCoupon) {
        await updateCoupon({ id: selectedCoupon.id, data });
      } else {
        await createCoupon(data);
      }
      setIsModalOpen(false);
      setSelectedCoupon(null);
    } catch (error) {
      console.error("Error submitting coupon:", error);
    }
  };

  // Filter coupons based on selected filters
  const filteredCoupons = coupons?.filter((coupon: Coupon) => {
    const statusMatch = statusFilter === 'all' || coupon.status === statusFilter;
    const typeMatch = typeFilter === 'all' || coupon.type === typeFilter;

    return statusMatch && typeMatch;
  }) || [];

  const couponsWithActions = filteredCoupons.map((coupon: Coupon) => ({
    ...coupon,
    onEdit: (coupon: Coupon) => {
      setSelectedCoupon(coupon);
      setIsModalOpen(true);
    },
    onStatusChange: (id: string, status: string) => {
      try {
        updateCoupon({
          id,
          data: {
            status: status as CouponStatus
          }
        });
      } catch (error) {
        console.error("Error updating coupon status:", error);
      }
    },
    onDelete: (id: string) => {
      if (window.confirm("Are you sure you want to delete this coupon?")) {
        deleteCoupon(id);
      }
    },
  }));

  return (
    <>
      <div className="flex-1 space-y-4">
        <div className="flex items-center justify-between">
          <Heading title="Coupons" description="Manage your discount coupons" />
          <Button onClick={() => {
            setSelectedCoupon(null);
            setIsModalOpen(true);
          }}>
            <Plus className="mr-2 h-4 w-4" /> Add New
          </Button>
        </div>
        <Separator />
        <DataTable
          columns={columns}
          data={couponsWithActions}
          loading={loading}
          searchKey="code"
          searchPlaceholder="Search by coupon code..."
          filterComponent={
            <div className="flex items-center space-x-2 ml-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="ACTIVE">Active</SelectItem>
                  <SelectItem value="INACTIVE">Inactive</SelectItem>
                  <SelectItem value="EXPIRED">Expired</SelectItem>
                </SelectContent>
              </Select>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="PERCENTAGE">Percentage</SelectItem>
                  <SelectItem value="FIXED_AMOUNT">Fixed Amount</SelectItem>
                </SelectContent>
              </Select>
            </div>
          }
        />
      </div>

      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {selectedCoupon ? "Edit Coupon" : "Create New Coupon"}
            </DialogTitle>
          </DialogHeader>
          <CouponForm 
            initialData={selectedCoupon}
            loading={loading}
            onSubmit={handleSubmit}
          />
        </DialogContent>
      </Dialog>
    </>
  );
};

export default CouponsPage;
