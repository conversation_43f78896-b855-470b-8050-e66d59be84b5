'use client';

import { useState, useEffect } from 'react';
import { Users, DollarSign, Wallet, ShoppingBag } from 'lucide-react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Heading } from "@/components/ui/heading";
import { Separator } from "@/components/ui/separator";
import { useToast } from '@/components/ui/use-toast';
import api from '@/lib/api';

// Value formatter for currency
const formatCurrency = (number: number) => 
  new Intl.NumberFormat("en-AE", {
    style: 'currency',
    currency: 'AED',
    minimumFractionDigits: 0,
  }).format(number);

interface FinanceStats {
  totalEmployees: number;
  totalSalaries: number;
  paidSalaries: number;
  totalOrderRevenue: number;
}

export default function FinancePage() {
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<FinanceStats>({
    totalEmployees: 0,
    totalSalaries: 0,
    paidSalaries: 0,
    totalOrderRevenue: 0,
  });

  // Fetch analytics data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Fetch active employees data
        const employeesResponse = await api.get('/api/employees?active=true');
        const employees = employeesResponse.data.data;
        
        // Fetch all salaries
        const salariesResponse = await api.get('/api/salaries');
        const salaries = salariesResponse.data.data;
        
        // Calculate salary stats
        let totalSalaries = 0;
        let paidSalaries = 0;
        salaries.forEach(salary => {
          totalSalaries += salary.amount;
          if (salary.status === 'PAID') {
            paidSalaries += salary.amount;
          }
        });

        // Fetch orders revenue - get all time data
        const ordersResponse = await api.get('/api/orders/analytics?timeRange=all');
        const ordersData = ordersResponse.data.data;

        setStats({
          totalEmployees: employees.length,
          totalSalaries,
          paidSalaries,
          totalOrderRevenue: ordersData.totalRevenue,
        });
      } catch (error) {
        console.error('Error fetching finance data:', error);
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to load finance data. Please refresh the page to try again.",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const StatCard = ({ title, value, description, icon: Icon }) => (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">
          {title}
        </CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        <p className="text-xs text-muted-foreground">{description}</p>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="flex-1 space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <Heading title="Finance Overview" description="Financial statistics and metrics" />
        </div>
      </div>
      <Separator />
      
      <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Total Employees"
          value={stats.totalEmployees}
          description="Current active employees"
          icon={Users}
        />
        <StatCard
          title="Total Salaries"
          value={formatCurrency(stats.totalSalaries)}
          description="Total salary amount"
          icon={DollarSign}
        />
        <StatCard
          title="Paid Salaries"
          value={formatCurrency(stats.paidSalaries)}
          description="Total paid salaries"
          icon={Wallet}
        />
        <StatCard
          title="Total Order Revenue"
          value={formatCurrency(stats.totalOrderRevenue)}
          description="Total revenue from all orders"
          icon={ShoppingBag}
        />
      </div>
    </div>
  );
}