import { apiClient } from "./client";

export interface ExpenseCategory {
  id: string;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

export interface OperatingExpense {
  id: string;
  amount: number;
  description: string;
  date: string;
  categoryId: string;
  companyId?: string;
  isPaid: boolean;
  paidAt?: string;
  createdAt: string;
  updatedAt: string;
  category?: ExpenseCategory;
  company?: {
    id: string;
    name: string;
  };
}

export interface CreateExpenseCategoryDTO {
  name: string;
  description?: string;
}

export interface CreateOperatingExpenseDTO {
  amount: number;
  description: string;
  date: string;
  categoryId: string;
  companyId?: string;
}

export interface APIResponse<T> {
  success: boolean;
  data: T;
  error?: string;
}

// Expense Categories API
export const getExpenseCategories = async () => {
  const response = await apiClient.get<APIResponse<ExpenseCategory[]>>("/operating-expenses/categories");
  return response.data.data;
};

export const getExpenseCategory = async (id: string) => {
  const response = await apiClient.get<APIResponse<ExpenseCategory>>(`/operating-expenses/categories/${id}`);
  return response.data.data;
};

export const createExpenseCategory = async (data: CreateExpenseCategoryDTO) => {
  const response = await apiClient.post<APIResponse<ExpenseCategory>>("/operating-expenses/categories", data);
  return response.data.data;
};

export const updateExpenseCategory = async ({ id, data }: { id: string; data: Partial<CreateExpenseCategoryDTO> }) => {
  const response = await apiClient.put<APIResponse<ExpenseCategory>>(`/operating-expenses/categories/${id}`, data);
  return response.data.data;
};

export const deleteExpenseCategory = async (id: string) => {
  const response = await apiClient.delete<APIResponse<void>>(`/operating-expenses/categories/${id}`);
  return response.data;
};

// Operating Expenses API
export const getOperatingExpenses = async () => {
  const response = await apiClient.get<APIResponse<OperatingExpense[]>>("/operating-expenses");
  return response.data.data;
};

export const getOperatingExpense = async (id: string) => {
  const response = await apiClient.get<APIResponse<OperatingExpense>>(`/operating-expenses/${id}`);
  return response.data.data;
};

export const createOperatingExpense = async (data: CreateOperatingExpenseDTO) => {
  const response = await apiClient.post<APIResponse<OperatingExpense>>("/operating-expenses", data);
  return response.data.data;
};

export const updateOperatingExpense = async ({ id, data }: { id: string; data: Partial<CreateOperatingExpenseDTO> }) => {
  const response = await apiClient.put<APIResponse<OperatingExpense>>(`/operating-expenses/${id}`, data);
  return response.data.data;
};

export const deleteOperatingExpense = async (id: string) => {
  const response = await apiClient.delete<APIResponse<void>>(`/operating-expenses/${id}`);
  return response.data;
};

export const markExpenseAsPaid = async (id: string) => {
  const response = await apiClient.patch<APIResponse<OperatingExpense>>(`/operating-expenses/${id}/mark-paid`, {});
  return response.data.data;
};
