import { prisma } from '../lib/prisma';

const defaultExpenseCategories = [
  {
    name: 'Rent',
    description: 'Office and warehouse rent payments',
    color: '#FF6B6B',
  },
  {
    name: 'Utilities',
    description: 'Electricity, water, gas, and internet bills',
    color: '#4ECDC4',
  },
  {
    name: 'Maintenance',
    description: 'Equipment and facility maintenance costs',
    color: '#45B7D1',
  },
  {
    name: 'Insurance',
    description: 'Business and equipment insurance premiums',
    color: '#96CEB4',
  },
  {
    name: 'Marketing',
    description: 'Advertising and promotional expenses',
    color: '#FFEAA7',
  },
  {
    name: 'Office Supplies',
    description: 'Stationery, printing, and office equipment',
    color: '#DDA0DD',
  },
  {
    name: 'Professional Services',
    description: 'Legal, accounting, and consulting fees',
    color: '#98D8C8',
  },
  {
    name: 'Transportation',
    description: 'Vehicle maintenance, fuel, and delivery costs',
    color: '#F7DC6F',
  },
  {
    name: 'Software & Subscriptions',
    description: 'Software licenses and subscription services',
    color: '#BB8FCE',
  },
  {
    name: 'Miscellaneous',
    description: 'Other operating expenses',
    color: '#85C1E9',
  },
];

async function seedExpenseCategories() {
  console.log('🌱 Seeding expense categories...');

  try {
    for (const category of defaultExpenseCategories) {
      const existingCategory = await prisma.expenseCategory.findUnique({
        where: { name: category.name },
      });

      if (!existingCategory) {
        await prisma.expenseCategory.create({
          data: category,
        });
        console.log(`✅ Created category: ${category.name}`);
      } else {
        console.log(`⏭️  Category already exists: ${category.name}`);
      }
    }

    console.log('🎉 Expense categories seeding completed!');
  } catch (error) {
    console.error('❌ Error seeding expense categories:', error);
    throw error;
  }
}

// Run the seed function if this file is executed directly
if (require.main === module) {
  seedExpenseCategories()
    .then(() => {
      console.log('✅ Seeding completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Seeding failed:', error);
      process.exit(1);
    });
}

export { seedExpenseCategories };
