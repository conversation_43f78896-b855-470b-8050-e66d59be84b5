'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DataTable } from '@/components/ui/data-table';
import { Heading } from '@/components/ui/heading';
import { Separator } from '@/components/ui/separator';
import { Plus, Search, Filter, Download, DollarSign, TrendingUp, AlertCircle, Calendar } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { format } from 'date-fns';
import api from '@/lib/api';
import { getFirebaseToken } from '@/lib/firebase';

interface ExpenseCategory {
  id: string;
  name: string;
  color?: string;
  description?: string;
}

interface OperatingExpense {
  id: string;
  invoiceNumber: string;
  companyName: string;
  amount: number;
  currency: string;
  invoiceDate: string;
  dueDate?: string;
  paidDate?: string;
  status: 'DUE' | 'PAID' | 'OVERDUE' | 'PARTIAL' | 'CANCELLED' | 'DISPUTED';
  bankReference?: string;
  paymentMethod?: string;
  description?: string;
  category: ExpenseCategory;
  createdBy: {
    firstName: string;
    lastName: string;
  };
  createdAt: string;
}

interface ExpenseStats {
  totalExpenses: number;
  totalAmount: number;
  dueAmount: number;
  paidAmount: number;
  overdueAmount: number;
  overdueCount: number;
}

const statusColors = {
  DUE: 'bg-yellow-100 text-yellow-800',
  PAID: 'bg-green-100 text-green-800',
  OVERDUE: 'bg-red-100 text-red-800',
  PARTIAL: 'bg-blue-100 text-blue-800',
  CANCELLED: 'bg-gray-100 text-gray-800',
  DISPUTED: 'bg-purple-100 text-purple-800',
};

export default function OperatingExpensesPage() {
  const router = useRouter();
  const [expenses, setExpenses] = useState<OperatingExpense[]>([]);
  const [categories, setCategories] = useState<ExpenseCategory[]>([]);
  const [stats, setStats] = useState<ExpenseStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [categoryFilter, setCategoryFilter] = useState<string>('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const fetchExpenses = async () => {
    try {
      const token = await getFirebaseToken();
      const params = {
        page: currentPage.toString(),
        limit: '20',
        ...(searchTerm && { search: searchTerm }),
        ...(statusFilter && { status: statusFilter }),
        ...(categoryFilter && { categoryId: categoryFilter }),
      };

      const response = await api.get('/api/operating-expenses', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        params,
      });

      setExpenses(response.data.data.expenses);
      setTotalPages(response.data.data.totalPages);
    } catch (error) {
      console.error('Error fetching expenses:', error);
      toast.error('Failed to load expenses');
    }
  };

  const fetchCategories = async () => {
    try {
      const token = await getFirebaseToken();
      const response = await api.get('/api/operating-expenses/categories', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      setCategories(response.data.data);
    } catch (error) {
      console.error('Error fetching categories:', error);
      toast.error('Failed to load categories');
    }
  };

  const fetchStats = async () => {
    try {
      const token = await getFirebaseToken();
      const response = await api.get('/api/operating-expenses/stats', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      setStats(response.data.data);
    } catch (error) {
      console.error('Error fetching stats:', error);
      toast.error('Failed to load statistics');
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([fetchExpenses(), fetchCategories(), fetchStats()]);
      setLoading(false);
    };
    loadData();
  }, [currentPage, searchTerm, statusFilter, categoryFilter]);

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  const handleStatusFilter = (value: string) => {
    setStatusFilter(value === 'all' ? '' : value);
    setCurrentPage(1);
  };

  const handleCategoryFilter = (value: string) => {
    setCategoryFilter(value === 'all' ? '' : value);
    setCurrentPage(1);
  };

  const columns = [
    {
      accessorKey: 'invoiceNumber',
      header: 'Invoice #',
      cell: ({ row }: any) => (
        <div className="font-medium">{row.original.invoiceNumber}</div>
      ),
    },
    {
      accessorKey: 'companyName',
      header: 'Company',
      cell: ({ row }: any) => {
        // Handle all possible company name structures
        const expense = row.original;
        let companyName = 'N/A';
        
        // Check all possible locations for company name
        if (expense.companyName) {
          companyName = expense.companyName;
        } else if (expense.company) {
          // If company is an object with name property
          if (typeof expense.company === 'object' && expense.company.name) {
            companyName = expense.company.name;
          }
          // If company is a string (direct company name)
          else if (typeof expense.company === 'string') {
            companyName = expense.company;
          }
        }
        
        return (
          <div className="max-w-[200px] truncate">{companyName}</div>
        );
      },
    },
    {
      accessorKey: 'category',
      header: 'Category',
      cell: ({ row }: any) => (
        <Badge 
          variant="outline" 
          style={{ backgroundColor: row.original.category.color + '20', borderColor: row.original.category.color }}
        >
          {row.original.category.name}
        </Badge>
      ),
    },
    {
      accessorKey: 'amount',
      header: 'Amount',
      cell: ({ row }: any) => (
        <div className="font-medium">
          {row.original.currency} {row.original.amount.toLocaleString()}
        </div>
      ),
    },
    {
      accessorKey: 'invoiceDate',
      header: 'Invoice Date',
      cell: ({ row }: any) => (
        <div>{format(new Date(row.original.invoiceDate), 'MMM dd, yyyy')}</div>
      ),
    },
    {
      accessorKey: 'dueDate',
      header: 'Due Date',
      cell: ({ row }: any) => (
        <div>
          {row.original.dueDate ? format(new Date(row.original.dueDate), 'MMM dd, yyyy') : '-'}
        </div>
      ),
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }: any) => (
        <Badge className={statusColors[row.original.status as keyof typeof statusColors]}>
          {row.original.status}
        </Badge>
      ),
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }: any) => (
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push(`/operating-expenses/${row.original.id}`)}
          >
            View
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push(`/operating-expenses/${row.original.id}/edit`)}
          >
            Edit
          </Button>
        </div>
      ),
    },
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div className="flex-1 space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <Heading 
            title="Operating Expenses" 
            description="Manage business operating costs, invoices, and payments" 
          />
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => router.push('/operating-expenses/categories')}
          >
            <Filter className="h-4 w-4 mr-2" />
            Categories
          </Button>
          <Button
            variant="outline"
            onClick={() => router.push('/operating-expenses/companies')}
          >
            Companies
          </Button>
          <Button onClick={() => router.push('/operating-expenses/new')}>
            <Plus className="h-4 w-4 mr-2" />
            Add Expense
          </Button>
        </div>
      </div>
      <Separator />

      {/* Stats Cards */}
      {stats && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Expenses</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">AED {stats.totalAmount.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                {stats.totalExpenses} total expenses
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Amount Due</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">AED {stats.dueAmount.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                Pending payments
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Paid Amount</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">AED {stats.paidAmount.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                Completed payments
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Overdue</CardTitle>
              <AlertCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">AED {stats.overdueAmount.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                {stats.overdueCount} overdue expenses
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 flex-wrap">
            <div className="flex-1 min-w-[200px]">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by invoice number, company name..."
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            <Select value={statusFilter || 'all'} onValueChange={handleStatusFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="DUE">Due</SelectItem>
                <SelectItem value="PAID">Paid</SelectItem>
                <SelectItem value="OVERDUE">Overdue</SelectItem>
                <SelectItem value="PARTIAL">Partial</SelectItem>
                <SelectItem value="CANCELLED">Cancelled</SelectItem>
                <SelectItem value="DISPUTED">Disputed</SelectItem>
              </SelectContent>
            </Select>
            <Select value={categoryFilter || 'all'} onValueChange={handleCategoryFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Data Table */}
      <Card>
        <CardHeader>
          <CardTitle>Expenses</CardTitle>
          <CardDescription>
            A list of all operating expenses and their details
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={expenses}
            searchKey="companyName"
          />
          
          {/* Pagination */}
          <div className="flex items-center justify-between space-x-2 py-4">
            <div className="text-sm text-muted-foreground">
              Page {currentPage} of {totalPages}
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
