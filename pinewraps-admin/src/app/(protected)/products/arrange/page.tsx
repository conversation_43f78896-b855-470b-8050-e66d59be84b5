'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  rectSortingStrategy,
} from '@dnd-kit/sortable';
import { toast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Loader2, Save, ArrowLeft } from 'lucide-react';
import { Heading } from '@/components/ui/heading';
import { Separator } from '@/components/ui/separator';
import { productsApi } from '@/lib/api';
import { SortableProductCard } from '@/components/products/SortableProductCard';

interface Product {
  id: string;
  name: string;
  images: Array<{
    url: string;
    isPrimary: boolean;
  }>;
  category: {
    name: string;
  };
  position: number;
}

export default function ArrangeProductsPage() {
  const router = useRouter();
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await productsApi.getAll({
        limit: 1000, // Get all products
        page: 1,
      });
      
      if (response?.data?.data?.products) {
        // Sort products by position before setting state
        const sortedProducts = [...response.data.data.products].sort((a, b) => 
          (a.position || 0) - (b.position || 0)
        );
        setProducts(sortedProducts);
      } else {
        console.error('Invalid response format:', response);
        toast({
          title: 'Error',
          description: 'Failed to fetch products. Invalid response format.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error fetching products:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch products. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      setProducts((items) => {
        const oldIndex = items.findIndex((item) => item.id === active.id);
        const newIndex = items.findIndex((item) => item.id === over.id);

        const newItems = arrayMove(items, oldIndex, newIndex);
        setHasChanges(true);
        return newItems;
      });
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      // Extract just the product IDs from the products array
      const productIds = products.map((p) => p.id);
      await productsApi.reorderProducts(productIds);
      toast({
        title: 'Success',
        description: 'Product arrangement saved successfully.',
      });
      setHasChanges(false);
    } catch (error) {
      console.error('Error saving product arrangement:', error);
      toast({
        title: 'Error',
        description: 'Failed to save product arrangement. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex h-[50vh] items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    );
  }

  return (
    <div className="flex-1 space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <Heading
            title="Arrange Products"
            description="Drag and drop products to arrange their display order"
          />
        </div>
        <Button
          onClick={handleSave}
          disabled={!hasChanges || saving}
          className="min-w-[120px]"
        >
          {saving ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              Save Order
            </>
          )}
        </Button>
      </div>
      <Separator />

      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
      >
        <SortableContext items={products} strategy={rectSortingStrategy}>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
            {products.map((product) => (
              <SortableProductCard key={product.id} product={product} />
            ))}
          </div>
        </SortableContext>
      </DndContext>
    </div>
  );
}
