# 🛡️ Database Protection System

## Overview

This system prevents accidental database resets and destructive operations on production databases.

## Protection Layers

### 1. Environment Protection
- `PRODUCTION_PROTECTION=true` - Enables protection mode
- `ENVIRONMENT=production` - Marks environment as production
- `ALLOW_DATABASE_RESET=false` - Disables database resets

### 2. URL Detection
Automatically detects production databases by URL patterns:
- `supabase.co`
- `amazonaws.com` 
- `planetscale.com`
- `railway.app`
- `heroku.com`
- `vercel.com`

### 3. Command Interception
Dangerous commands are intercepted and require confirmation:
- `db:reset`
- `migrate:reset`
- `db:push --force-reset`
- `migrate:dev --create-only`

## Safe Commands

### Database Push
```bash
# Safe push (recommended)
npm run db:push

# Force push (requires confirmation)
npm run db:push:force
```

### Migrations
```bash
# Safe migration
npm run db:migrate dev

# Deploy migrations
npm run db:migrate:deploy

# Reset (requires confirmation)
npm run db:migrate:reset
```

### Status Check
```bash
# Check protection status
npm run db:status
```

## Blocked Commands

These commands are completely blocked:
```bash
npm run db:reset          # ❌ BLOCKED
npm run prisma:reset       # ❌ BLOCKED
npx prisma db push --force-reset  # ❌ Use safe scripts instead
```

## Emergency Override

If you absolutely need to perform a destructive operation:

1. Set `ALLOW_DATABASE_RESET=true` in .env
2. Run the command
3. **IMMEDIATELY** set `ALLOW_DATABASE_RESET=false` back

## Protection Status

Current protection settings:
- Production Protection: **ENABLED**
- Environment: **production**
- Database Reset: **DISABLED**
- URL Protection: **ACTIVE**

## For Developers

### Adding New Protection
Edit `scripts/db-protection.js` to add new dangerous command patterns.

### Testing Protection
```bash
# Test protection status
node scripts/db-protection.js

# Test specific command
node scripts/db-protection.js "db push --force-reset"
```

## ⚠️ IMPORTANT WARNINGS

1. **NEVER** run `npx prisma db push --force-reset` directly
2. **ALWAYS** use the safe npm scripts
3. **VERIFY** protection status before any schema changes
4. **BACKUP** before any destructive operations

## Recovery

If data is accidentally lost:
1. Check Supabase dashboard for backups
2. Contact Supabase support with project ID: `ovnafvbnjsqrzcquwmdz`
3. Use point-in-time recovery if available

---

**Remember: It's better to be safe than sorry. This protection system exists to prevent data loss.**
