"use client";

import { ColumnDef } from "@tanstack/react-table";
import { CellAction } from "@/components/companies/cell-action";

export type CompanyColumn = {
  id: string;
  name: string;
  email: string | null;
  phone: string | null;
  address: string | null;
};

export const columns: ColumnDef<CompanyColumn>[] = [
  {
    accessorKey: "name",
    header: "Name",
  },
  {
    accessorKey: "email",
    header: "Email",
  },
  {
    accessorKey: "phone",
    header: "Phone",
  },
  {
    accessorKey: "address",
    header: "Address",
  },
  {
    id: "actions",
    cell: ({ row }) => <CellAction data={row.original} />,
  },
];
