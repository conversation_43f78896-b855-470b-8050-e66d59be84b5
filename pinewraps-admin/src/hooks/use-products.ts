import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { productsApi, categoriesApi } from "@/lib/api";
import { toast } from "@/components/ui/use-toast";

export interface ProductFilters {
  page?: number;
  limit?: number;
  status?: string;
  categoryId?: string;
  search?: string;
}

export interface ProductAnalytics {
  totalProducts: number;
  activeProducts: number;
  draftProducts: number;
  totalCategories: number;
}

// Hook for fetching products with filters
export const useProducts = (filters: ProductFilters = {}) => {
  return useQuery({
    queryKey: ['products', filters],
    queryFn: async () => {
      const params = {
        page: filters.page || 1,
        limit: filters.limit || 10,
        status: filters.status !== 'all' ? filters.status?.toUpperCase() : undefined,
        categoryId: filters.categoryId !== 'all' ? filters.categoryId : undefined,
        search: filters.search && filters.search.trim() !== '' ? filters.search.trim() : undefined
      };

      const response = await productsApi.getAll(params);
      console.log('Products API Response:', response); // Debug log
      console.log('Products data:', response.data); // Debug log
      
      // Ensure we're returning the full data structure with pagination
      if (response.data && response.data.data) {
        return response.data.data; // Return the complete data object including pagination
      }
      
      return { products: [], pagination: { total: 0, pages: 1, page: 1, limit: 10 } }; // Default fallback
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes (formerly cacheTime)
    placeholderData: (previousData) => previousData, // Keep previous data while fetching new data
  });
};

// Hook for fetching product analytics
export const useProductAnalytics = () => {
  return useQuery({
    queryKey: ['product-analytics'],
    queryFn: async () => {
      const response = await productsApi.getAnalytics();
      console.log('Analytics API Response:', response); // Debug log
      console.log('Analytics data:', response.data); // Debug log
      return response.data.data || response.data || {}; // Try both structures
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Hook for fetching categories
export const useProductCategories = () => {
  return useQuery({
    queryKey: ['product-categories'],
    queryFn: async () => {
      const response = await categoriesApi.getAll();
      return response.data.data || []; // Handle nested data structure
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
};

// Hook for deleting a product
export const useDeleteProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (productId: string) => {
      const response = await productsApi.delete(productId);
      return response.data.data || {}; // Handle nested data structure
    },
    onSuccess: () => {
      // Invalidate and refetch products and analytics
      queryClient.invalidateQueries({ queryKey: ['products'] });
      queryClient.invalidateQueries({ queryKey: ['product-analytics'] });
      
      toast({
        title: "Success",
        description: "Product deleted successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error?.response?.data?.message || "Failed to delete product",
        variant: "destructive",
      });
    },
  });
};

// Hook for bulk deleting products (Note: API doesn't have bulkDelete, so we'll delete individually)
export const useBulkDeleteProducts = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (productIds: string[]) => {
      // Delete products individually since API doesn't have bulk delete
      const results = await Promise.allSettled(
        productIds.map(id => productsApi.delete(id))
      );
      
      const successful = results.filter(result => result.status === 'fulfilled').length;
      const failed = results.filter(result => result.status === 'rejected').length;
      
      return { successful, failed, total: productIds.length };
    },
    onSuccess: (data, productIds) => {
      // Invalidate and refetch products and analytics
      queryClient.invalidateQueries({ queryKey: ['products'] });
      queryClient.invalidateQueries({ queryKey: ['product-analytics'] });
      
      toast({
        title: "Success",
        description: `${data.successful} products deleted successfully${data.failed > 0 ? `, ${data.failed} failed` : ''}`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error?.response?.data?.message || "Failed to delete products",
        variant: "destructive",
      });
    },
  });
};

// Hook for exporting products
export const useExportProducts = () => {
  return useMutation({
    mutationFn: async (filters: ProductFilters = {}) => {
      const response = await productsApi.exportProducts();
      return response; // Return raw response for file download
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Products exported successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error?.response?.data?.message || "Failed to export products",
        variant: "destructive",
      });
    },
  });
};

// Hook for importing products
export const useImportProducts = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (file: File) => {
      const response = await productsApi.importProducts(file);
      return response.data.data || {}; // Handle nested data structure
    },
    onSuccess: () => {
      // Invalidate and refetch products and analytics
      queryClient.invalidateQueries({ queryKey: ['products'] });
      queryClient.invalidateQueries({ queryKey: ['product-analytics'] });
      
      toast({
        title: "Success",
        description: "Products imported successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error?.response?.data?.message || "Failed to import products",
        variant: "destructive",
      });
    },
  });
};
