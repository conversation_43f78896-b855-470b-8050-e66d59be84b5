import { Router } from 'express';
import { StaffReportsController } from '../controllers/staff-reports.controller';
import { requireAuth, requireAccess } from '../middleware/auth';
import { UserRole } from '@prisma/client';

const router = Router();

// All staff reports routes require authentication and admin access
router.use(requireAuth);
router.use(requireAccess([UserRole.ADMIN, UserRole.SUPER_ADMIN]));

/**
 * @route GET /api/staff-reports/debug/users
 * @desc Debug endpoint to check users in database
 * @access Admin
 */
router.get('/debug/users', StaffReportsController.debugUsers);

/**
 * @route GET /api/staff-reports/debug/staff/:staffId
 * @desc Debug endpoint to check staff assignments in orders
 * @access Admin
 */
router.get('/debug/staff/:staffId', StaffReportsController.debugStaffAssignments);

/**
 * @route GET /api/staff-reports/debug/final-check
 * @desc Debug endpoint to check final check data
 * @access Admin
 */
router.get('/debug/final-check', StaffReportsController.debugFinalCheckData);

/**
 * @route GET /api/staff-reports/staff/:staffId
 * @desc Get detailed individual staff analytics
 * @access Admin
 * @param {string} staffId - Staff member ID
 * @query {string} [timeRange] - Time range (7d, 14d, 30d, 3m, all, custom)
 * @query {string} [startDate] - Custom start date (ISO string)
 * @query {string} [endDate] - Custom end date (ISO string)
 */
router.get('/staff/:staffId', StaffReportsController.getIndividualStaffDetails);

/**
 * @route GET /api/staff-reports/performance
 * @desc Get individual staff performance metrics
 * @access Admin
 * @query {string} [staffId] - Specific staff member ID
 * @query {string} [department] - Filter by department (kitchen, design, finalCheck, cashier)
 * @query {string} [timeRange] - Time range (7d, 14d, 30d, 3m, all, custom)
 * @query {string} [startDate] - Custom start date (ISO string)
 * @query {string} [endDate] - Custom end date (ISO string)
 */
router.get('/performance', StaffReportsController.getStaffPerformance);

/**
 * @route GET /api/staff-reports/departments
 * @desc Get department performance metrics
 * @access Admin
 * @query {string} [timeRange] - Time range (7d, 14d, 30d, 3m, all, custom)
 * @query {string} [startDate] - Custom start date (ISO string)
 * @query {string} [endDate] - Custom end date (ISO string)
 */
router.get('/departments', StaffReportsController.getDepartmentMetrics);

/**
 * @route GET /api/staff-reports/quality
 * @desc Get quality control metrics
 * @access Admin
 * @query {string} [timeRange] - Time range (7d, 14d, 30d, 3m, all, custom)
 * @query {string} [startDate] - Custom start date (ISO string)
 * @query {string} [endDate] - Custom end date (ISO string)
 */
router.get('/quality', StaffReportsController.getQualityMetrics);

/**
 * @route GET /api/staff-reports/dashboard
 * @desc Get comprehensive staff dashboard data
 * @access Admin
 * @query {string} [timeRange] - Time range (7d, 14d, 30d, 3m, all, custom)
 * @query {string} [startDate] - Custom start date (ISO string)
 * @query {string} [endDate] - Custom end date (ISO string)
 */
router.get('/dashboard', StaffReportsController.getStaffDashboard);

/**
 * @route GET /api/staff-reports/trends
 * @desc Get productivity trends over time
 * @access Admin
 * @query {string} [staffId] - Specific staff member ID
 * @query {string} [department] - Filter by department (kitchen, design, finalCheck, cashier)
 * @query {string} [timeRange] - Time range (7d, 14d, 30d, 3m, all, custom)
 */
router.get('/trends', StaffReportsController.getProductivityTrends);

export default router;
