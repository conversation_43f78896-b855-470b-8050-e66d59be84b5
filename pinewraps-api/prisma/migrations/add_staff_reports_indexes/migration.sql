-- Add composite indexes for staff reports performance optimization
-- These indexes will significantly improve query performance for staff analytics

-- Composite indexes for staff + date + status queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS "POSOrder_createdById_createdAt_status_idx" 
ON "POSOrder"("createdById", "createdAt", "status");

CREATE INDEX CONCURRENTLY IF NOT EXISTS "POSOrder_kitchenById_createdAt_status_idx" 
ON "POSOrder"("kitchenById", "createdAt", "status");

CREATE INDEX CONCURRENTLY IF NOT EXISTS "POSOrder_designById_createdAt_status_idx" 
ON "POSOrder"("designById", "createdAt", "status");

CREATE INDEX CONCURRENTLY IF NOT EXISTS "POSOrder_finalCheckById_createdAt_status_idx" 
ON "POSOrder"("finalCheckById", "createdAt", "status");

-- Additional indexes for date range queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS "POSOrder_createdAt_status_idx" 
ON "POSOrder"("createdAt", "status");

CREATE INDEX CONCURRENTLY IF NOT EXISTS "POSOrder_status_createdAt_idx" 
ON "POSOrder"("status", "createdAt");

-- Index for staff filtering queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS "User_staff_roles_idx" 
ON "User"("isKitchenStaff", "isDesignStaff", "isFinalCheckStaff", "isCashierStaff", "isActive", "isDeleted");
