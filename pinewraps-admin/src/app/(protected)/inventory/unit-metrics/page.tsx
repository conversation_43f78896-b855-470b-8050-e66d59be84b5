'use client';

import { useState, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import { useRouter } from 'next/navigation';
import {
  Plus,
  Search,
  Edit,
  Trash2,
  MoreVertical,
  CheckCircle2,
  XCircle,
  Star,
} from 'lucide-react';
import { Heading } from '@/components/ui/heading';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { toast } from '@/lib/toast';
import api from '@/lib/api';
import { UnitMetricForm } from '../../../../components/inventory/UnitMetricForm';

interface UnitMetric {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  isActive: boolean;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function UnitMetricsPage() {
  const router = useRouter();
  const pageRef = useRef<HTMLDivElement>(null);
  const [unitMetrics, setUnitMetrics] = useState<UnitMetric[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedUnitMetric, setSelectedUnitMetric] = useState<UnitMetric | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const fetchUnitMetrics = async () => {
    try {
      setLoading(true);
      const response = await api.get('/api/unit-metrics', {
        params: {
          search: searchQuery || undefined,
        }
      });
      setUnitMetrics(response.data.data.items || []);
    } catch (error: any) {
      console.error('Error fetching unit metrics:', error);
      toast.error(error.response?.data?.message || 'Failed to fetch unit metrics');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUnitMetrics();
  }, [searchQuery]);

  const forceCloseDialogs = () => {
    setIsFormOpen(false);
    setIsDeleteDialogOpen(false);
    setSelectedUnitMetric(null);
    setSubmitting(false);
    setDeleting(false);

    // Force remove any modal overlays
    setTimeout(() => {
      const overlays = document.querySelectorAll('[data-radix-dialog-overlay]');
      overlays.forEach(overlay => overlay.remove());

      const contents = document.querySelectorAll('[data-radix-dialog-content]');
      contents.forEach(content => content.remove());

      // Remove any remaining modal backdrops
      const backdrops = document.querySelectorAll('.fixed.inset-0.z-50');
      backdrops.forEach(backdrop => backdrop.remove());
    }, 50);
  };

  // Cleanup effect to ensure proper state management
  useEffect(() => {
    return () => {
      // Reset all dialog states on unmount
      forceCloseDialogs();
    };
  }, []);

  // Emergency cleanup effect - force close dialogs if they get stuck
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        forceCloseDialogs();
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  // Body scroll lock when modals are open
  useEffect(() => {
    if (isFormOpen || isDeleteDialogOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isFormOpen, isDeleteDialogOpen]);

  const handleSubmit = async (data: any) => {
    if (submitting) return; // Prevent double submission

    setSubmitting(true);

    try {
      if (selectedUnitMetric) {
        await api.patch(`/api/unit-metrics/${selectedUnitMetric.id}`, data);
        toast.success('Unit metric updated successfully');
      } else {
        await api.post('/api/unit-metrics', data);
        toast.success('Unit metric created successfully');
      }

      // Refresh data first
      await fetchUnitMetrics();

      // Then close modal and reset state
      setIsFormOpen(false);
      setSelectedUnitMetric(null);

    } catch (error: any) {
      console.error('Error saving unit metric:', error);
      toast.error(error.response?.data?.message || 'Failed to save unit metric');
    }

    // Always reset submitting state
    setSubmitting(false);
  };

  const handleDeleteUnitMetric = async (id: string) => {
    if (deleting) return; // Prevent double deletion

    setDeleting(true);

    try {
      await api.delete(`/api/unit-metrics/${id}`);
      toast.success('Unit metric deleted successfully');

      // Refresh data first
      await fetchUnitMetrics();

      // Then close modal and reset state
      setIsDeleteDialogOpen(false);
      setSelectedUnitMetric(null);

    } catch (error: any) {
      console.error('Error deleting unit metric:', error);
      toast.error(error.response?.data?.message || 'Failed to delete unit metric');
    }

    // Always reset deleting state
    setDeleting(false);
  };

  const onDelete = (unitMetric: UnitMetric) => {
    setSelectedUnitMetric(unitMetric);
    setIsDeleteDialogOpen(true);
  };



  const filteredUnitMetrics = unitMetrics.filter((unitMetric) => {
    const searchLower = searchQuery.toLowerCase();
    return (
      unitMetric.name.toLowerCase().includes(searchLower) ||
      unitMetric.displayName.toLowerCase().includes(searchLower) ||
      (unitMetric.description && unitMetric.description.toLowerCase().includes(searchLower))
    );
  });

  return (
    <div ref={pageRef} className="flex-1 space-y-4" tabIndex={-1}>
      <div className="flex items-center justify-between">
        <div>
          <Heading 
            title="Unit Metrics" 
            description="Manage your unit of measurement metrics" 
          />
        </div>
        <Button
          onClick={() => {
            setSelectedUnitMetric(null);
            setIsFormOpen(true);
          }}
        >
          <Plus className="mr-2 h-4 w-4" />
          Add Unit Metric
        </Button>
      </div>
      <Separator />

      <Card>
        <CardHeader>
          <CardTitle>Unit Metrics</CardTitle>
          <CardDescription>
            Manage unit metrics for inventory items
          </CardDescription>
          <div className="flex items-center space-x-2">
            <Search className="h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search unit metrics..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="h-9"
            />
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Display Name</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Default</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={6} className="h-24 text-center">
                    Loading...
                  </TableCell>
                </TableRow>
              ) : filteredUnitMetrics.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="h-24 text-center">
                    No unit metrics found.
                  </TableCell>
                </TableRow>
              ) : (
                filteredUnitMetrics.map((unitMetric) => (
                  <TableRow key={unitMetric.id}>
                    <TableCell className="font-medium">{unitMetric.name}</TableCell>
                    <TableCell>{unitMetric.displayName}</TableCell>
                    <TableCell>{unitMetric.description || '-'}</TableCell>
                    <TableCell>
                      {unitMetric.isDefault ? (
                        <Badge variant="default" className="bg-yellow-500">
                          <Star className="h-3 w-3 mr-1" />
                          Default
                        </Badge>
                      ) : '-'}
                    </TableCell>
                    <TableCell>
                      {unitMetric.isActive ? (
                        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                          <CheckCircle2 className="h-3 w-3 mr-1" />
                          Active
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                          <XCircle className="h-3 w-3 mr-1" />
                          Inactive
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => {
                              setSelectedUnitMetric(unitMetric);
                              setIsFormOpen(true);
                            }}
                          >
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="text-destructive"
                            onClick={() => onDelete(unitMetric)}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Add/Edit Modal */}
      {isFormOpen && createPortal(
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black/50"
            onClick={() => {
              if (!submitting) {
                setIsFormOpen(false);
                setSelectedUnitMetric(null);
              }
            }}
          />

          {/* Modal Content */}
          <div className="relative bg-white rounded-lg shadow-lg max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold">
                  {selectedUnitMetric ? 'Edit Unit Metric' : 'Add Unit Metric'}
                </h2>
                <button
                  onClick={() => {
                    if (!submitting) {
                      setIsFormOpen(false);
                      setSelectedUnitMetric(null);
                    }
                  }}
                  className="text-gray-400 hover:text-gray-600"
                  disabled={submitting}
                >
                  ✕
                </button>
              </div>

              <UnitMetricForm
                key={selectedUnitMetric?.id || 'new'}
                initialData={selectedUnitMetric}
                onSubmit={handleSubmit}
                onCancel={() => {
                  if (!submitting) {
                    setIsFormOpen(false);
                    setSelectedUnitMetric(null);
                  }
                }}
                loading={submitting}
              />
            </div>
          </div>
        </div>,
        document.body
      )}

      {/* Delete Confirmation Modal */}
      {isDeleteDialogOpen && createPortal(
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black/50"
            onClick={() => {
              if (!deleting) {
                setIsDeleteDialogOpen(false);
                setSelectedUnitMetric(null);
              }
            }}
          />

          {/* Modal Content */}
          <div className="relative bg-white rounded-lg shadow-lg max-w-md w-full mx-4">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold">Delete Unit Metric</h2>
                <button
                  onClick={() => {
                    if (!deleting) {
                      setIsDeleteDialogOpen(false);
                      setSelectedUnitMetric(null);
                    }
                  }}
                  className="text-gray-400 hover:text-gray-600"
                  disabled={deleting}
                >
                  ✕
                </button>
              </div>

              <p className="text-gray-600 mb-6">
                Are you sure you want to delete "{selectedUnitMetric?.displayName}"? This action cannot be undone.
              </p>

              <div className="flex justify-end space-x-4">
                <Button
                  variant="outline"
                  onClick={() => {
                    if (!deleting) {
                      setIsDeleteDialogOpen(false);
                      setSelectedUnitMetric(null);
                    }
                  }}
                  disabled={deleting}
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => selectedUnitMetric && handleDeleteUnitMetric(selectedUnitMetric.id)}
                  disabled={deleting}
                >
                  {deleting ? 'Deleting...' : 'Delete'}
                </Button>
              </div>
            </div>
          </div>
        </div>,
        document.body
      )}
    </div>
  );
}
