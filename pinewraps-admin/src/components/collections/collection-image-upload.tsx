'use client';

import { useCallback, useState } from 'react';
import { Upload, X } from 'lucide-react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { auth } from '@/lib/firebase';

interface CollectionImageUploadProps {
  value: string;
  onChange: (value: string) => void;
  onRemove: () => void;
  disabled?: boolean;
}

export function CollectionImageUpload({
  value,
  onChange,
  onRemove,
  disabled
}: CollectionImageUploadProps) {
  const [loading, setLoading] = useState(false);

  const [error, setError] = useState<string>('');

  const validateFile = (file: File) => {
    // Check file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      throw new Error('File size must be less than 5MB');
    }

    // Check file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      throw new Error('Only JPEG, PNG and WebP images are allowed');
    }
  };

  const onUpload = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    try {
      setLoading(true);
      setError('');
      
      const file = e.target.files?.[0];
      if (!file) return;

      // Validate file
      validateFile(file);

      const formData = new FormData();
      formData.append('file', file);
      formData.append('context', 'collections');

      // Get Firebase token
      const token = await auth.currentUser?.getIdToken();
      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch('/api/collection/upload', {
        method: 'POST',
        body: formData,
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Upload failed');
      }

      onChange(data.url);
    } catch (error) {
      console.error('Error uploading image:', error);
      setError(error instanceof Error ? error.message : 'Failed to upload image');
    } finally {
      setLoading(false);
    }
  }, [onChange]);

  const handleRemove = useCallback(async () => {
    try {
      setLoading(true);
      setError('');

      // Delete from Firebase
      // Get Firebase token
      const token = await auth.currentUser?.getIdToken();
      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch('/api/collection/upload', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify({ url: value })
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to delete image');
      }

      onRemove();
    } catch (error) {
      console.error('Error removing image:', error);
      setError(error instanceof Error ? error.message : 'Failed to remove image');
    } finally {
      setLoading(false);
    }
  }, [value, onRemove]);

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Image Preview Section */}
        <div className="space-y-4">
          <div className="relative aspect-[16/9] rounded-lg border-2 border-dashed border-muted-foreground/25 overflow-hidden group hover:border-muted-foreground/50 transition-colors">
            {value ? (
              <div className="relative w-full h-full group">
                <Image
                  fill
                  className="object-cover"
                  alt="Collection image"
                  src={value}
                  sizes="(min-width: 1024px) 50vw, 100vw"
                  priority
                />
                {/* Overlay with actions */}
                <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-4">
                  <Button
                    type="button"
                    onClick={handleRemove}
                    variant="destructive"
                    size="sm"
                    disabled={disabled || loading}
                    className="hover:scale-105 transition-transform"
                  >
                    <X className="h-4 w-4 mr-2" />
                    Remove
                  </Button>
                  <label
                    htmlFor="image"
                    className={`cursor-pointer inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-all hover:scale-105 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-9 px-4 py-2 ${
                      disabled || loading ? 'opacity-50 cursor-not-allowed' : ''
                    }`}
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Change
                  </label>
                </div>
                {/* Loading overlay */}
                {loading && (
                  <div className="absolute inset-0 bg-black/60 flex flex-col items-center justify-center gap-3">
                    <div className="animate-spin rounded-full h-8 w-8 border-3 border-primary border-t-transparent" />
                    <span className="text-white text-sm font-medium">Uploading...</span>
                  </div>
                )}
              </div>
            ) : (
              <label
                htmlFor="image"
                className="absolute inset-0 flex flex-col items-center justify-center gap-3 cursor-pointer hover:bg-muted/50 transition-colors"
              >
                <div className="rounded-full bg-muted-foreground/10 p-4">
                  <Upload className="h-6 w-6 text-muted-foreground" />
                </div>
                <div className="text-center">
                  <span className="font-medium text-muted-foreground hover:text-primary transition-colors">
                    Click to upload
                  </span>
                  <p className="text-xs text-muted-foreground/75 mt-1">
                    Recommended: 1200x800px. Max 5MB
                  </p>
                  <p className="text-xs text-muted-foreground/75">
                    Formats: JPEG, PNG, WebP
                  </p>
                </div>
              </label>
            )}
          </div>
          {error && (
            <div className="flex items-center gap-2 text-sm text-destructive bg-destructive/10 px-3 py-2 rounded">
              <X className="h-4 w-4" />
              {error}
            </div>
          )}
        </div>

        {/* Image Details Section */}
        <div className="space-y-4">
          <div className="bg-muted/50 rounded-lg p-4 space-y-3">
            <h4 className="font-medium">Image Guidelines</h4>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li className="flex items-center gap-2">
                <div className="h-1.5 w-1.5 rounded-full bg-primary" />
                Recommended size: 1200x800 pixels
              </li>
              <li className="flex items-center gap-2">
                <div className="h-1.5 w-1.5 rounded-full bg-primary" />
                Maximum file size: 5MB
              </li>
              <li className="flex items-center gap-2">
                <div className="h-1.5 w-1.5 rounded-full bg-primary" />
                Supported formats: JPEG, PNG, WebP
              </li>
              <li className="flex items-center gap-2">
                <div className="h-1.5 w-1.5 rounded-full bg-primary" />
                Use high-quality images for best results
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Hidden file input */}
      <input
        type="file"
        accept="image/jpeg,image/png,image/webp"
        onChange={onUpload}
        disabled={disabled || loading}
        className="hidden"
        id="image"
      />
    </div>
  );
}
