'use client';

import { useRouter } from 'next/navigation';
import { CollectionService } from '@/services/collection.service';
import { CollectionForm } from '@/components/collections/collection-form';
import { toast } from 'react-hot-toast';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

export default function NewCollectionPage() {
  const router = useRouter();

  const handleSubmit = async (data: any) => {
    try {
      await CollectionService.createCollection(data);
      toast.success('Collection created successfully');
      router.push('/collections');
    } catch (error) {
      console.error('Error creating collection:', error);
      toast.error('Failed to create collection');
    }
  };

  return (
    <div className="p-6">
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="ghost"
          onClick={() => router.push('/collections')}
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <h1 className="text-2xl font-bold">New Collection</h1>
      </div>

      <CollectionForm onSubmit={handleSubmit} />
    </div>
  );
}
