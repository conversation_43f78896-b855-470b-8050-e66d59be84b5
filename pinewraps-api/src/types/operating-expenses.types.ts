import { ExpenseStatus } from '@prisma/client';

export interface CreateExpenseCategoryDto {
  name: string;
  description?: string;
  color?: string;
}

export interface UpdateExpenseCategoryDto {
  name?: string;
  description?: string;
  color?: string;
  isActive?: boolean;
}

export interface CreateOperatingExpenseDto {
  invoiceNumber: string;
  companyId: string;
  amount: number;
  currency?: string;
  invoiceDate: string; // ISO date string
  dueDate?: string; // ISO date string
  categoryId: string;
  description?: string;
  bankReference?: string;
  attachments?: string[];
}

export interface UpdateOperatingExpenseDto {
  invoiceNumber?: string;
  companyId?: string;
  amount?: number;
  currency?: string;
  invoiceDate?: string;
  dueDate?: string;
  paidDate?: string;
  status?: ExpenseStatus;
  bankReference?: string;
  paymentMethod?: string;
  categoryId?: string;
  description?: string;
  attachments?: string[];
}

export interface ExpenseFilters {
  status?: ExpenseStatus;
  categoryId?: string;
  companyId?: string;
  startDate?: string;
  endDate?: string;
  minAmount?: number;
  maxAmount?: number;
}

export interface ExpenseQueryParams extends ExpenseFilters {
  page?: number;
  limit?: number;
  sortBy?: 'invoiceDate' | 'dueDate' | 'amount' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
  search?: string;
}

export interface ExpenseListResponse {
  expenses: OperatingExpenseWithCategory[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface OperatingExpenseWithCategory {
  id: string;
  invoiceNumber: string;
  amount: number;
  currency: string;
  invoiceDate: Date;
  dueDate?: Date;
  paidDate?: Date;
  status: ExpenseStatus;
  bankReference?: string;
  paymentMethod?: string;
  description?: string;
  attachments: string[];
  createdAt: Date;
  updatedAt: Date;
  category: {
    id: string;
    name: string;
    color?: string;
  };
  company: {
    id: string;
    name: string;
    email?: string;
    phone?: string;
    address?: string;
  };
  createdBy: {
    id: string;
    firstName: string;
    lastName: string;
  };
}

export interface ExpenseCategoryWithStats {
  id: string;
  name: string;
  description?: string;
  color?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  _count: {
    expenses: number;
  };
  totalAmount?: number;
}

export interface ExpenseStats {
  totalExpenses: number;
  totalAmount: number;
  dueAmount: number;
  paidAmount: number;
  overdueAmount: number;
  overdueCount: number;
  categoryBreakdown: {
    categoryId: string;
    categoryName: string;
    totalAmount: number;
    count: number;
  }[];
  monthlyTrend: {
    month: string;
    totalAmount: number;
    count: number;
  }[];
}
