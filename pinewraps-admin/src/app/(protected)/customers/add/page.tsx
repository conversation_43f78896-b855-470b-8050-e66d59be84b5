'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { CustomerForm, type CustomerFormData } from '@/components/customers/customer-form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { customerService } from '@/services/customer.service';
import { toast } from 'sonner';

export default function AddCustomerPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (data: CustomerFormData) => {
    try {
      setIsSubmitting(true);
      
      // Format the data for the API
      const customerData = {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        phone: data.phone || null,
        birthDate: data.birthDate ? data.birthDate.toISOString() : null,
        isVip: data.isVip,
        password: data.password || undefined,
        notifications: {
          orderUpdates: true,
          promotions: true,
          newsletter: true,
          sms: true
        }
      };

      // Make the API call
      const response = await customerService.createCustomer(customerData);
      
      // Show success message and redirect
      toast.success('Customer created successfully');
      router.push('/customers');
    } catch (error) {
      console.error('Error creating customer:', error);
      toast.error('Failed to create customer. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto py-10">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold tracking-tight">Add New Customer</h1>
        <Button
          variant="outline"
          onClick={() => router.back()}
          disabled={isSubmitting}
        >
          Cancel
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Customer Information</CardTitle>
        </CardHeader>
        <CardContent>
          <CustomerForm
            onSubmit={handleSubmit}
            isSubmitting={isSubmitting}
            submitLabel="Create Customer"
          />
        </CardContent>
      </Card>
    </div>
  );
}
