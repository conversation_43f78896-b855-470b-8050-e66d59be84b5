import { AxiosError } from 'axios';
import axios from 'axios';
import { auth } from '@/lib/firebase';

// Company interface
export interface Company {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
}

// Create axios instance with auth token
const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to add auth token
apiClient.interceptors.request.use(async (config) => {
  const user = auth.currentUser;
  if (user) {
    const token = await user.getIdToken();
    config.headers.Authorization = `Bearer ${token}`;
    console.log('Setting auth token for companies API request');
  } else {
    console.warn('No current user for auth token');
  }
  return config;
});

const COMPANIES_ENDPOINT = '/api/companies';

class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public data?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

export const companyService = {
  async getCompanies(): Promise<Company[]> {
    try {
      console.log('Fetching companies from service...');
      const response = await apiClient.get(COMPANIES_ENDPOINT);
      console.log('Companies API response:', response);
      // Handle the API response structure {success: true, data: Array<Company>}
      if (response.data && response.data.success && Array.isArray(response.data.data)) {
        return response.data.data;
      }
      // If the response is directly an array
      if (Array.isArray(response.data)) {
        return response.data;
      }
      console.error('Unexpected API response format:', response.data);
      return [];
    } catch (error) {
      console.error('Error fetching companies:', error);
      if (error instanceof AxiosError) {
        throw new ApiError(
          error.response?.data?.message || 'Failed to fetch companies',
          error.response?.status,
          error.response?.data
        );
      }
      throw error;
    }
  },

  async getCompanyById(id: string): Promise<Company> {
    try {
      const response = await apiClient.get<Company>(`${COMPANIES_ENDPOINT}/${id}`);
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        throw new ApiError(
          error.response?.data?.message || 'Failed to fetch company',
          error.response?.status,
          error.response?.data
        );
      }
      throw error;
    }
  },

  async createCompany(data: Omit<Company, 'id'>): Promise<Company> {
    try {
      const response = await apiClient.post<Company>(COMPANIES_ENDPOINT, data);
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        throw new ApiError(
          error.response?.data?.message || 'Failed to create company',
          error.response?.status,
          error.response?.data
        );
      }
      throw error;
    }
  },

  async updateCompany(id: string, data: Partial<Company>): Promise<Company> {
    try {
      const response = await apiClient.patch<Company>(`${COMPANIES_ENDPOINT}/${id}`, data);
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        throw new ApiError(
          error.response?.data?.message || 'Failed to update company',
          error.response?.status,
          error.response?.data
        );
      }
      throw error;
    }
  },

  async deleteCompany(id: string): Promise<void> {
    try {
      await apiClient.delete(`${COMPANIES_ENDPOINT}/${id}`);
    } catch (error) {
      if (error instanceof AxiosError) {
        throw new ApiError(
          error.response?.data?.message || 'Failed to delete company',
          error.response?.status,
          error.response?.data
        );
      }
      throw error;
    }
  }
};
