#!/usr/bin/env node

/**
 * Safe Database Push Script
 * Prevents accidental database resets during schema pushes
 */

const { spawn } = require('child_process');
const DatabaseProtection = require('./db-protection');

async function safePush() {
  const protection = new DatabaseProtection();
  
  console.log('🛡️  Safe Database Push - Protection Enabled');
  console.log('═══════════════════════════════════════════');
  
  // Check if this is a dangerous operation
  const args = process.argv.slice(2);
  const hasForceReset = args.includes('--force-reset') || args.includes('--reset');
  
  if (hasForceReset) {
    console.log('🚨 FORCE RESET DETECTED - Checking permissions...');
    const allowed = await protection.confirmDestructiveOperation('prisma db push --force-reset');
    
    if (!allowed) {
      console.log('❌ Force reset blocked by database protection');
      process.exit(1);
    }
  }

  // Create backup before push (if in production)
  if (protection.isProductionDatabase() || protection.isProductionEnvironment()) {
    protection.createBackup();
  }

  // Remove dangerous flags and run safe push
  const safeArgs = args.filter(arg => 
    !arg.includes('--force-reset') && 
    !arg.includes('--reset')
  );

  console.log('✅ Running safe database push...');
  
  const prisma = spawn('npx', ['prisma', 'db', 'push', ...safeArgs], {
    stdio: 'inherit',
    shell: true
  });

  prisma.on('close', (code) => {
    if (code === 0) {
      console.log('✅ Database push completed successfully');
    } else {
      console.log(`❌ Database push failed with code ${code}`);
    }
    process.exit(code);
  });

  prisma.on('error', (error) => {
    console.error('❌ Error running database push:', error);
    process.exit(1);
  });
}

safePush().catch(error => {
  console.error('❌ Safe push script error:', error);
  process.exit(1);
});
