'use client';

import { useEffect, useState, use } from 'react';
import { useRouter } from 'next/navigation';
import { format } from 'date-fns';
import { ArrowLeft } from 'lucide-react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useAuth } from '@/providers/auth-provider';
import api from '@/lib/api';

interface Adjustment {
  id: string;
  type: string;
  reason: string;
  quantity: number;
  item: {
    name: string;
    sku: string;
    currentStock: number;
  };
  createdBy: {
    firstName: string;
    lastName: string;
  };
  createdAt: string;
}

export default function AdjustmentDetailsPage({
  params,
}: {
  params: Promise<{ date: string }>;
}) {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [adjustments, setAdjustments] = useState<Adjustment[]>([]);
  const [loading, setLoading] = useState(true);
  const resolvedParams = use(params);

  // Check authentication
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/login');
      return;
    }

    if (user && !user.adminAccess?.includes('inventory') && user.role !== 'ADMIN' && user.role !== 'SUPER_ADMIN') {
      toast.error('You do not have access to this page');
      router.push('/dashboard');
      return;
    }
  }, [user, authLoading, router]);

  useEffect(() => {
    const fetchAdjustments = async () => {
      try {
        setLoading(true);
        const date = decodeURIComponent(resolvedParams.date);
        const response = await api.get('/api/inventory-adjustments', {
          params: {
            date
          }
        });
        
        if (response.data?.data?.items) {
          // Get all adjustments for this date
          const dayAdjustments = response.data.data.items.find((item: any) => item.date === date);
          setAdjustments(dayAdjustments?.adjustments || []);
        }
      } catch (error) {
        console.error('Error fetching adjustments:', error);
        toast.error('Failed to load adjustments');
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      fetchAdjustments();
    }
  }, [resolvedParams.date, user]);

  if (loading || authLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  const formattedDate = format(new Date(decodeURIComponent(resolvedParams.date)), 'MMMM d, yyyy');

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center gap-4 mb-8">
        <Button
          variant="ghost"
          onClick={() => router.push('/inventory/adjustments')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Adjustments
        </Button>
        <h1 className="text-3xl font-bold tracking-tight">
          Adjustments for {formattedDate}
        </h1>
      </div>

      <Card>
        <CardContent className="p-6">
          <div className="rounded-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Time</TableHead>
                  <TableHead>Item</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>Current Stock</TableHead>
                  <TableHead>Reason</TableHead>
                  <TableHead>Adjusted By</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {adjustments.map((adjustment) => (
                  <TableRow key={adjustment.id}>
                    <TableCell>
                      {format(new Date(adjustment.createdAt), 'hh:mm a')}
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{adjustment.item.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {adjustment.item.sku}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {adjustment.type.toLowerCase()}
                      </Badge>
                    </TableCell>
                    <TableCell>{adjustment.quantity}</TableCell>
                    <TableCell>{adjustment.item.currentStock}</TableCell>
                    <TableCell>{adjustment.reason}</TableCell>
                    <TableCell>
                      {adjustment.createdBy.firstName} {adjustment.createdBy.lastName}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
