import { apiClient } from "./client";

export interface Company {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateCompanyDTO {
  name: string;
  email?: string;
  phone?: string;
  address?: string;
}

export interface APIResponse<T> {
  success: boolean;
  data: T;
  error?: string;
}

export const getCompanies = async () => {
  const response = await apiClient.get<APIResponse<Company[]>>("/companies");
  return response.data.data;
};

export const getCompany = async (id: string) => {
  const response = await apiClient.get<APIResponse<Company>>(`/companies/${id}`);
  return response.data.data;
};

export const createCompany = async (data: CreateCompanyDTO) => {
  const response = await apiClient.post<APIResponse<Company>>("/companies", data);
  return response.data.data;
};

export const updateCompany = async (id: string, data: Partial<CreateCompanyDTO>) => {
  const response = await apiClient.patch<APIResponse<Company>>(`/companies/${id}`, data);
  return response.data.data;
};

export const deleteCompany = async (id: string) => {
  const response = await apiClient.delete<APIResponse<void>>(`/companies/${id}`);
  return response.data;
};
