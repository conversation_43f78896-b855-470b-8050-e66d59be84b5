'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ArrowLeft, Edit, Trash2, CheckCircle, Download } from 'lucide-react';
import { format } from 'date-fns';
import { toast } from 'sonner';
import api from '@/lib/api';
import { getFirebaseToken } from '@/lib/firebase';

interface ExpenseCategory {
  id: string;
  name: string;
  color?: string;
}

interface OperatingExpense {
  id: string;
  invoiceNumber: string;
  companyName?: string;
  companyId?: string;
  amount: number;
  currency: string;
  invoiceDate: string;
  dueDate?: string;
  paidDate?: string;
  status: 'DUE' | 'PAID' | 'OVERDUE' | 'PARTIAL' | 'CANCELLED' | 'DISPUTED';
  bankReference?: string;
  paymentMethod?: string;
  description?: string;
  attachments: string[];
  category: ExpenseCategory;
  company?: {
    id: string;
    name: string;
  };
  createdBy: {
    firstName: string;
    lastName: string;
  };
  createdAt: string;
  updatedAt: string;
}

const statusColors = {
  DUE: 'bg-yellow-100 text-yellow-800',
  PAID: 'bg-green-100 text-green-800',
  OVERDUE: 'bg-red-100 text-red-800',
  PARTIAL: 'bg-blue-100 text-blue-800',
  CANCELLED: 'bg-gray-100 text-gray-800',
  DISPUTED: 'bg-purple-100 text-purple-800',
};

// Helper function to get company name from different data structures
function getCompanyName(expense: OperatingExpense | null): string {
  if (!expense) return 'N/A';
  
  // Check all possible locations for company name
  if (expense.companyName) return expense.companyName;
  if (expense.company && expense.company.name) return expense.company.name;
  
  // If we have a raw object with company property that might be a string
  const anyExpense = expense as any;
  if (anyExpense.company && typeof anyExpense.company === 'string') {
    return anyExpense.company;
  }
  
  return 'N/A';
}

export default function ExpenseDetailPage() {
  const router = useRouter();
  const params = useParams();
  const [expense, setExpense] = useState<OperatingExpense | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (params.id) {
      fetchExpense(params.id as string);
    }
  }, [params.id]);

  const fetchExpense = async (id: string) => {
    try {
      const token = await getFirebaseToken();
      const response = await api.get(`/api/operating-expenses/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      console.log('API Response:', response.data.data);
      
      // Extract company information from the response
      const expenseData = response.data.data;
      
      // Handle different API response structures
      let processedExpense = { ...expenseData };
      
      // If we have a companyId but no company name information
      if (expenseData.companyId && !expenseData.companyName && !expenseData.company) {
        // Fetch company details
        try {
          const companyResponse = await api.get(`/api/companies/${expenseData.companyId}`, {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          });
          
          const companyData = companyResponse.data.data || companyResponse.data;
          processedExpense.company = {
            id: companyData.id,
            name: companyData.name
          };
        } catch (companyError) {
          console.error('Error fetching company details:', companyError);
        }
      }
      
      setExpense(processedExpense);
    } catch (error) {
      console.error('Error fetching expense:', error);
      toast.error('Failed to load expense details');
      router.push('/operating-expenses');
    } finally {
      setLoading(false);
    }
  };

  const handleMarkAsPaid = async () => {
    if (!expense) return;

    const paidDate = new Date().toISOString().split('T')[0];

    try {
      const token = await getFirebaseToken();
      await api.patch(`/api/operating-expenses/${expense.id}/mark-paid`, {
        paidDate,
        paymentMethod: 'Manual Update',
      }, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      toast.success('Expense marked as paid');
      fetchExpense(expense.id);
    } catch (error: any) {
      console.error('Error marking expense as paid:', error);
      toast.error(error.message || 'Failed to mark expense as paid');
    }
  };

  const handleDelete = async () => {
    if (!expense) return;

    if (!confirm(`Are you sure you want to delete expense "${expense.invoiceNumber}"?`)) {
      return;
    }

    try {
      const token = await getFirebaseToken();
      await api.delete(`/api/operating-expenses/${expense.id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      toast.success('Expense deleted successfully');
      router.push('/operating-expenses');
    } catch (error: any) {
      console.error('Error deleting expense:', error);
      toast.error(error.message || 'Failed to delete expense');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (!expense) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-gray-900">Expense not found</h2>
        <p className="mt-2 text-gray-600">The expense you're looking for doesn't exist.</p>
        <Button
          className="mt-4"
          onClick={() => router.push('/operating-expenses')}
        >
          Back to Expenses
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Invoice {expense.invoiceNumber}
            </h1>
            <p className="text-muted-foreground">
              {getCompanyName(expense)}
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          {expense.status === 'DUE' && (
            <Button
              variant="outline"
              onClick={handleMarkAsPaid}
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Mark as Paid
            </Button>
          )}
          <Button
            variant="outline"
            onClick={() => router.push(`/operating-expenses/${expense.id}/edit`)}
          >
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          <Button
            variant="outline"
            onClick={handleDelete}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Main Details */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Invoice Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <label className="text-sm font-medium text-gray-500">Invoice Number</label>
                  <p className="text-lg font-semibold">{expense.invoiceNumber}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Company</label>
                  <p className="text-lg font-semibold">{getCompanyName(expense)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Amount</label>
                  <p className="text-lg font-semibold">
                    {expense.currency} {expense.amount.toLocaleString()}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Category</label>
                  <div className="mt-1">
                    <Badge 
                      variant="outline" 
                      style={{ backgroundColor: expense.category.color + '20', borderColor: expense.category.color }}
                    >
                      {expense.category.name}
                    </Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Invoice Date</label>
                  <p className="text-lg">{format(new Date(expense.invoiceDate), 'MMM dd, yyyy')}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Due Date</label>
                  <p className="text-lg">
                    {expense.dueDate ? format(new Date(expense.dueDate), 'MMM dd, yyyy') : 'Not set'}
                  </p>
                </div>
              </div>

              {expense.description && (
                <>
                  <Separator />
                  <div>
                    <label className="text-sm font-medium text-gray-500">Description</label>
                    <p className="mt-1 text-gray-900">{expense.description}</p>
                  </div>
                </>
              )}

              {expense.bankReference && (
                <>
                  <Separator />
                  <div>
                    <label className="text-sm font-medium text-gray-500">Bank Reference</label>
                    <p className="mt-1 text-gray-900">{expense.bankReference}</p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {expense.attachments.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Attachments</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {expense.attachments.map((attachment, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <span className="text-sm font-medium">Attachment {index + 1}</span>
                      <Button variant="outline" size="sm">
                        <Download className="h-4 w-4 mr-2" />
                        Download
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Status</CardTitle>
            </CardHeader>
            <CardContent>
              <Badge className={statusColors[expense.status]}>
                {expense.status}
              </Badge>
            </CardContent>
          </Card>

          {expense.status === 'PAID' && (
            <Card>
              <CardHeader>
                <CardTitle>Payment Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-500">Paid Date</label>
                  <p className="text-sm">
                    {expense.paidDate ? format(new Date(expense.paidDate), 'MMM dd, yyyy') : '-'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Payment Method</label>
                  <p className="text-sm">{expense.paymentMethod || '-'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Bank Reference</label>
                  <p className="text-sm">{expense.bankReference || '-'}</p>
                </div>
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle>Audit Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-500">Created By</label>
                <p className="text-sm">
                  {expense.createdBy.firstName} {expense.createdBy.lastName}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Created At</label>
                <p className="text-sm">
                  {format(new Date(expense.createdAt), 'MMM dd, yyyy HH:mm')}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Last Updated</label>
                <p className="text-sm">
                  {format(new Date(expense.updatedAt), 'MMM dd, yyyy HH:mm')}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
