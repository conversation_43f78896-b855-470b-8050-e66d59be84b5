'use client';

import { useEffect, useState, use } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Loader2 } from 'lucide-react';
import { toast } from 'sonner';

import { PurchaseOrderForm } from '@/components/purchase-orders/PurchaseOrderForm';
import { Button } from '@/components/ui/button';
import api from '@/lib/api';
import { getFirebaseToken } from '@/lib/firebase';

interface Props {
  params: Promise<{
    id: string;
  }>;
}

export default function EditPurchaseOrderPage({ params }: Props) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { id } = use(params);
  const [purchaseOrder, setPurchaseOrder] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Get return URL from search params
  const returnUrl = searchParams.get('returnUrl') || '/inventory/purchase-orders';

  useEffect(() => {
    const fetchPurchaseOrder = async () => {
      try {
        const token = await getFirebaseToken();
        const response = await api.get(`/api/purchase-orders/${id}`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });
        console.log('Purchase Order API Response:', response.data.data);
        console.log('Purchase Order Items:', response.data.data?.items);
        if (response.data.data?.items) {
          response.data.data.items.forEach((item: any, index: number) => {
            console.log(`Item ${index}:`, {
              id: item.id,
              itemId: item.itemId,
              quantity: item.quantity,
              unitPrice: item.unitPrice,
              total: item.total,
              item: item.item
            });
          });
        }
        setPurchaseOrder(response.data.data);
      } catch (error: any) {
        console.error('Error fetching purchase order:', error);
        setError(error.response?.data?.message || 'Failed to load purchase order');
        if (error.response?.status === 404) {
          router.push('/404');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchPurchaseOrder();
  }, [id, router]);

  if (loading) {
    return (
      <div className="flex h-[400px] w-full items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-[400px] w-full items-center justify-center">
        <p className="text-destructive">{error}</p>
      </div>
    );
  }

  return (
    <div className="flex-1 space-y-4">
      <PurchaseOrderForm initialData={purchaseOrder} returnUrl={returnUrl} />

    </div>
  );
}
