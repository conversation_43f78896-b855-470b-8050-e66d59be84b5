'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Legend
} from 'recharts';
import staffReportsService, { DepartmentMetrics } from '@/services/staff-reports.service';

interface DepartmentMetricsChartProps {
  data: DepartmentMetrics[];
  isLoading: boolean;
}

export default function DepartmentMetricsChart({ data, isLoading }: DepartmentMetricsChartProps) {
  const chartData = React.useMemo(() => {
    return data.map(dept => ({
      name: staffReportsService.getDepartmentDisplayName(dept.department),
      orders: dept.totalOrders,
      efficiency: dept.efficiency,
      avgTime: dept.averageProcessingTime,
      returnRate: dept.returnRate,
      staff: dept.totalStaff,
      color: staffReportsService.getDepartmentColor(dept.department),
    }));
  }, [data]);

  const pieData = React.useMemo(() => {
    return data.map(dept => ({
      name: staffReportsService.getDepartmentDisplayName(dept.department),
      value: dept.totalOrders,
      color: staffReportsService.getDepartmentColor(dept.department),
    }));
  }, [data]);

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Department Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <Skeleton className="h-64 w-full" />
          </CardContent>
        </Card>
        
        <div className="grid gap-4 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Order Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <Skeleton className="h-48 w-full" />
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Department Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {[...Array(4)].map((_, i) => (
                  <Skeleton key={i} className="h-16 w-full" />
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Main Performance Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Department Performance Metrics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip 
                  formatter={(value: number, name: string) => {
                    if (name === 'orders') return [value, 'Orders'];
                    if (name === 'efficiency') return [`${value.toFixed(1)}%`, 'Efficiency'];
                    if (name === 'avgTime') return [`${value.toFixed(1)}min`, 'Avg Time'];
                    if (name === 'returnRate') return [`${value.toFixed(1)}%`, 'Return Rate'];
                    return [value, name];
                  }}
                />
                <Bar dataKey="orders" fill="#3b82f6" name="orders" />
                <Bar dataKey="efficiency" fill="#10b981" name="efficiency" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      <div className="grid gap-4 md:grid-cols-2">
        {/* Order Distribution Pie Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Order Distribution by Department</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-48">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={pieData}
                    cx="50%"
                    cy="50%"
                    outerRadius={60}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {pieData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value: number) => [value, 'Orders']} />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Department Summary Cards */}
        <Card>
          <CardHeader>
            <CardTitle>Department Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {data.map((dept) => (
                <div 
                  key={dept.department} 
                  className="p-3 border rounded-lg"
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <div 
                        className="h-3 w-3 rounded-full"
                        style={{ backgroundColor: staffReportsService.getDepartmentColor(dept.department) }}
                      />
                      <span className="font-medium">
                        {staffReportsService.getDepartmentDisplayName(dept.department)}
                      </span>
                    </div>
                    <Badge variant="outline">
                      {dept.totalStaff} staff
                    </Badge>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>
                      <span className="text-muted-foreground">Orders:</span>
                      <span className="ml-1 font-medium">{dept.totalOrders}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Efficiency:</span>
                      <span className="ml-1 font-medium">{dept.efficiency.toFixed(1)}%</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Avg Time:</span>
                      <span className="ml-1 font-medium">
                        {staffReportsService.formatProcessingTime(dept.averageProcessingTime)}
                      </span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Return Rate:</span>
                      <span className={`ml-1 font-medium ${dept.returnRate > 5 ? 'text-red-600' : 'text-green-600'}`}>
                        {dept.returnRate.toFixed(1)}%
                      </span>
                    </div>
                  </div>
                </div>
              ))}
              
              {data.length === 0 && (
                <div className="text-center py-4 text-muted-foreground">
                  No department data available.
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
