'use client';

import { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Eye } from 'lucide-react';
import { useRouter } from 'next/navigation';

import { DataTableColumnHeader } from '@/components/ui/data-table-header';

export type InventoryAdjustment = {
  id: string;
  date: string;
  itemCount: number;
  types: string[];
  notes: string;
  createdBy: Array<{
    id: string;
    firstName: string;
    lastName: string;
  }>;
  adjustments: any[];
};

export const columns: ColumnDef<InventoryAdjustment>[] = [
  {
    accessorKey: 'date',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Date" />
    ),
    cell: ({ row }) => {
      return format(new Date(row.getValue('date')), 'MMM dd, yyyy');
    },
  },
  {
    accessorKey: 'itemCount',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Total Items" />
    ),
  },
  {
    accessorKey: 'types',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Adjustment Types" />
    ),
    cell: ({ row }) => {
      const types = row.getValue('types') as string[];
      return (
        <div className="flex flex-wrap gap-1">
          {types.map((type) => (
            <Badge key={type} variant="outline">
              {type.toLowerCase()}
            </Badge>
          ))}
        </div>
      );
    },
  },
  {
    accessorKey: 'createdBy',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Created By" />
    ),
    cell: ({ row }) => {
      const users = row.getValue('createdBy') as Array<{ firstName: string; lastName: string }>;
      return (
        <div className="flex flex-col gap-1">
          {users.map((user, index) => (
            <span key={index}>{user.firstName} {user.lastName}</span>
          ))}
        </div>
      );
    },
  },
  {
    accessorKey: 'notes',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Notes" />
    ),
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      const router = useRouter();
      const adjustment = row.original;
      const date = encodeURIComponent(adjustment.date);

      return (
        <Button
          variant="ghost"
          size="icon"
          onClick={() => router.push(`/inventory/adjustments/${date}`)}
        >
          <Eye className="h-4 w-4" />
        </Button>
      );
    },
  },
];
