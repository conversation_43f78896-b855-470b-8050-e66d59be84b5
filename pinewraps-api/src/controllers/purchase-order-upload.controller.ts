import { Request, Response } from 'express';
import { prisma } from '../lib/prisma';
import { pdfStorageService } from '../lib/pdf-storage';
import multer from 'multer';
import { ApiError } from '../utils/api-error';

// Configure multer for memory storage
const storage = multer.memoryStorage();
const upload = multer({ 
  storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    // Accept only PDF files
    if (file.mimetype === 'application/pdf') {
      cb(null, true);
    } else {
      cb(new Error('Only PDF files are allowed'));
    }
  }
});

// Middleware to handle file upload
export const uploadInvoicePdf = upload.single('file');

// Controller to handle the upload
export const handleInvoicePdfUpload = async (req: Request, res: Response) => {
  try {
    if (!req.file) {
      throw new ApiError(400, 'No file uploaded');
    }

    const file = req.file;
    const purchaseOrderId = req.body.purchaseOrderId;

    // Upload the file to Firebase Storage
    const fileUrl = await pdfStorageService.uploadPurchaseOrderInvoice(
      purchaseOrderId || 'temp', // Use 'temp' if no purchase order ID yet
      {
        buffer: file.buffer,
        mimetype: file.mimetype,
        originalname: file.originalname
      }
    );

    // If we have a purchase order ID, update the record
    if (purchaseOrderId) {
      try {
        console.log(`Updating purchase order ${purchaseOrderId} with PDF URL: ${fileUrl}`);
        
        // Using a type assertion to handle the new field that TypeScript doesn't recognize yet
        // This is needed because we've added the field to the schema but haven't regenerated Prisma types
        const updatedOrder = await prisma.purchaseOrder.update({
          where: { id: purchaseOrderId },
          data: { 
            // @ts-ignore: Field exists in the database but not in the generated types
            invoicePdfUrl: fileUrl 
          }
        });
        
        console.log('Purchase order updated successfully:', updatedOrder.id);
      } catch (error) {
        console.error('Error updating purchase order with PDF URL:', error);
        throw new ApiError(500, 'Failed to update purchase order with PDF URL');
      }
    }

    // Return the file URL
    return res.json({
      success: true,
      fileUrl
    });
  } catch (error) {
    console.error('Error uploading invoice PDF:', error);
    
    if (error instanceof ApiError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message
      });
    }
    
    return res.status(500).json({
      success: false,
      message: 'Failed to upload invoice PDF'
    });
  }
};
