'use client';

import { Product } from '@/types/product';
import { useEffect, useState } from 'react';
import JsonLd from '@/components/seo/JsonLd';

interface ProductStructuredDataProps {
  product: Product;
  websiteUrl: string;
}

export default function ProductStructuredData({ product, websiteUrl }: ProductStructuredDataProps) {
  const [validUntilDate, setValidUntilDate] = useState<string>('');
  
  useEffect(() => {
    // Set price valid until date to 1 year from now
    const oneYearFromNow = new Date();
    oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);
    setValidUntilDate(oneYearFromNow.toISOString().split('T')[0]);
  }, []);

  // Ensure we have a valid product
  if (!product || !product.id) return null;
  
  // Prepare product data for structured data
  const productData = {
    ...product,
    images: product.images?.map(img => ({
      ...img,
      url: img.url?.startsWith('http') ? img.url : `${websiteUrl}${img.url || '/placeholder.jpg'}`
    })),
    price: product.basePrice || 0,
    priceValidUntil: validUntilDate,
    brand: 'Pinewraps',
    shippingDetails: {
      cost: 30,
      country: 'AE',
      minHandlingDays: 1,
      maxHandlingDays: 2,
      minTransitDays: 1,
      maxTransitDays: 3
    }
  };

  return <JsonLd type="product" data={productData} />;
}
