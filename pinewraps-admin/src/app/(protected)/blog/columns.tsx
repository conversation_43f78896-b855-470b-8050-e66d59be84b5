'use client';

import { ColumnDef } from '@tanstack/react-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { DataTableColumnHeader } from '@/components/ui/data-table-header';
import { Edit, MoreHorizontal, Trash } from 'lucide-react';
import { format } from 'date-fns';

// Define the type for a blog post
export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED';
  publishedAt: string | null;
  author: string;
  viewCount: number;
  createdAt: string;
  updatedAt: string;
  categories: { id: string; name: string }[];
}

export const blogPostColumns: ColumnDef<BlogPost>[] = [
  {
    accessorKey: 'title',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Title" />
    ),
    size: 400,
    cell: ({ row }) => (
      <div className="font-medium max-w-[380px] truncate" title={row.getValue('title') as string}>
        {row.getValue('title')}
      </div>
    ),
  },
  {
    accessorKey: 'status',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    size: 100,
    cell: ({ row }) => {
      const status = row.getValue('status') as string;
      return (
        <Badge
          variant={
            status === 'PUBLISHED'
              ? 'default'
              : status === 'DRAFT'
              ? 'outline'
              : 'secondary'
          }
          className="text-xs"
        >
          {status.charAt(0).toUpperCase() + status.slice(1).toLowerCase()}
        </Badge>
      );
    },
  },
  {
    accessorKey: 'categories',
    header: 'Categories',
    size: 180,
    cell: ({ row }) => {
      const categories = row.original.categories || [];
      return (
        <div className="flex flex-wrap gap-1 max-w-[160px]">
          {categories.slice(0, 2).map((category) => (
            <Badge key={category.id} variant="outline" className="text-xs">
              {category.name}
            </Badge>
          ))}
          {categories.length > 2 && (
            <Badge variant="outline" className="text-xs">
              +{categories.length - 2}
            </Badge>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: 'publishedAt',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Published" />
    ),
    size: 120,
    cell: ({ row }) => {
      const date = row.getValue('publishedAt') as string;
      return (
        <div className="text-sm text-muted-foreground">
          {date ? format(new Date(date), 'MMM dd, yyyy') : 'Not published'}
        </div>
      );
    },
  },
  {
    accessorKey: 'viewCount',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Views" />
    ),
    size: 80,
    cell: ({ row }) => (
      <div className="text-center font-medium">
        {row.getValue('viewCount')}
      </div>
    ),
  },
  {
    id: 'actions',
    header: 'Actions',
    size: 80,
    cell: ({ row }) => {
      const post = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => window.open(`/blog/posts/edit/${post.id}`, '_self')}
            >
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => {
                // This will be handled by the parent component
                const event = new CustomEvent('deleteBlogPost', { detail: post.id });
                window.dispatchEvent(event);
              }}
              className="text-red-600"
            >
              <Trash className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
