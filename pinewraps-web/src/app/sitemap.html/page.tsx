import { BlogService } from '@/services/blog.service';
import { collectionService } from '@/services/collection.service';
import { headers } from 'next/headers';
import { BlogPost } from '@/types/blog';
import { generateSlug } from '@/services/api';
import { Collection } from '@/types/collection';



interface Product {
  id: string;
  name: string;
  slug: string;
  status: 'ACTIVE' | 'DRAFT';
  visibility: 'ALL' | 'WEB_ONLY';
}

export const revalidate = 3600; // Revalidate every hour

async function getProducts() {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/products/public?page=1&limit=1000`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    console.log('Raw API response:', result);
    return result;
  } catch (error) {
    console.error('Error fetching products:', error);
    return { success: false, data: [] };
  }
}

interface SitemapData {
  staticPages: { url: string; name: string }[];
  products: any[];
  blogPosts: BlogPost[];
  collections: Collection[];
}

async function generateSitemap(): Promise<SitemapData> {
  // Static pages
  const staticPages = [
    { url: '/', name: 'Home' },
    { url: '/about', name: 'About Us' },
    { url: '/contact', name: 'Contact' },
    { url: '/shop', name: 'Shop' },
    { url: '/blog', name: 'Blog' },
    { url: '/privacy-policy', name: 'Privacy Policy' },
    { url: '/terms', name: 'Terms & Conditions' },
    { url: '/refund-policy', name: 'Refund Policy' },
    { url: '/shipping-policy', name: 'Shipping Policy' },
  ];

  // Fetch all dynamic data
  try {
    const [blogPosts, productsRes, collectionsRes] = await Promise.all([
      BlogService.getAllPosts(1, 1000),  // Get all blog posts
      getProducts(),  // Get all products
      collectionService.getCollections({ limit: 1000 }),  // Get all collections
    ]);

    console.log('Products response:', productsRes); // Debug log
    console.log('Products data type:', typeof productsRes.data);
    console.log('Is array?', Array.isArray(productsRes.data));
    
    if (!productsRes.success || !productsRes.data?.products) {
      console.error('Invalid products response format');
      return {
        staticPages,
        products: [],
        blogPosts: [],
        collections: []
      };
    }

    // Map and filter products
    const products = productsRes.data.products;
    console.log('Products array:', products);
    
    const mappedProducts = products
      .filter((product: any) => {
        console.log('Filtering product:', product);
        return product.status === 'ACTIVE' &&
          (product.visibility === 'ALL' || product.visibility === 'WEB_ONLY' || product.showOnHomepage);
      })
      .map((product: any) => ({
        id: product.id,
        name: product.name,
        slug: product.slug || generateSlug(product.name),
        status: product.status,
        visibility: product.visibility
      }));

    const result = {
      staticPages,
      products: mappedProducts,
      blogPosts: blogPosts.data || [],
      collections: Array.isArray(collectionsRes) ? collectionsRes : [],
    };
  
    return result as {
      staticPages: typeof staticPages;
      products: typeof mappedProducts;
      blogPosts: typeof blogPosts.data;
      collections: Collection[];
    };  
  } catch (error) {
    console.error('Error fetching data:', error);
    return {
      staticPages,
      products: [],
      blogPosts: [],
      collections: []
    } as SitemapData;
  }

}

interface StaticPage {
  url: string;
  name: string;
}

export default async function SitemapPage() {
  const { staticPages, products, blogPosts, collections } = await generateSitemap();
  const headersList = await headers();
  const host = headersList.get('host') || 'pinewraps.com';
  const protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';
  const baseUrl = `${protocol}://${host}`;

  return (
    <>
      {/* Simple HTML structure for crawlers */}
      <div className="max-w-5xl mx-auto py-16 px-4">
        <h1 className="text-3xl font-bold mb-8">Sitemap</h1>
        
        <div className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">Pages</h2>
          <ul className="list-disc pl-5 space-y-2">
            {staticPages.map((page) => (
              <li key={page.url}>
                <a 
                  href={page.url}
                  className="text-blue-600 hover:underline"
                >
                  {page.name}
                </a>
              </li>
            ))}
          </ul>
        </div>

        <div className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">Products</h2>
          <ul className="list-disc pl-5 space-y-2">
            {products.map((product: Product) => (
              <li key={product.id}>
                <a 
                  href={`/shop/${product.slug}`}
                  className="text-blue-600 hover:underline"
                >
                  {product.name}
                </a>
              </li>
            ))}
          </ul>
        </div>

        <div className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">Collections</h2>
          <ul className="list-disc pl-5 space-y-2">
            {collections.map((collection: Collection) => (
              <li key={collection.id}>
                <a 
                  href={`/collections/${collection.slug}`}
                  className="text-blue-600 hover:underline"
                >
                  {collection.name}
                </a>
              </li>
            ))}
          </ul>
        </div>

        <div className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">Blog Posts</h2>
          <ul className="list-disc pl-5 space-y-2">
            {blogPosts.map((post: BlogPost) => (
              <li key={post.id}>
                <a 
                  href={`/blog/${post.slug}`}
                  className="text-blue-600 hover:underline"
                >
                  {post.title}
                </a>
              </li>
            ))}
          </ul>
        </div>
      </div>

      {/* Machine-readable metadata */}
      <script 
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "ItemList",
            "itemListElement": [
              ...staticPages.map((page: StaticPage, index: number) => ({
                "@type": "ListItem",
                "position": index + 1,
                "name": page.name,
                "url": `${baseUrl}${page.url}`
              })),
              ...products.map((product: Product, index: number) => ({
                "@type": "ListItem",
                "position": staticPages.length + index + 1,
                "name": product.name,
                "url": `${baseUrl}/shop/${product.slug}`
              })),
              ...blogPosts.map((post: BlogPost, index: number) => ({
                "@type": "ListItem",
                "position": staticPages.length + products.length + index + 1,
                "name": post.title,
                "url": `${baseUrl}/blog/${post.slug}`
              })),
              ...collections.map((collection: Collection, index: number) => ({
                "@type": "ListItem",
                "position": staticPages.length + products.length + blogPosts.length + index + 1,
                "name": collection.name,
                "url": `${baseUrl}/collections/${collection.slug}`
              }))
            ]
          })
        }}
      />
    </>
  );
}
