"use client";

import { ColumnDef } from "@tanstack/react-table";
import { DataTableColumnHeader } from "@/components/ui/data-table-header";
import { Badge } from "@/components/ui/badge";
import { Package, CreditCard } from "lucide-react";

interface Order {
  id: string;
  orderNumber: string;
  status: string;
  total: number;
  subtotal: number;
  items: Array<{
    id: string;
    name: string;
    variant?: string;
    quantity: number;
    price: number;
  }>;
  createdAt: string;
  updatedAt: string;
  source: 'ONLINE' | 'POS';
  paymentMethod?: string;
  deliveryMethod?: string;
}

const getStatusVariant = (status: string) => {
  switch (status.toLowerCase()) {
    case 'pending':
      return 'secondary';
    case 'processing':
      return 'default';
    case 'completed':
    case 'delivered':
      return 'default';
    case 'cancelled':
      return 'destructive';
    case 'refunded':
      return 'outline';
    default:
      return 'secondary';
  }
};

const getSourceVariant = (source: string) => {
  switch (source) {
    case 'POS':
      return 'default';
    case 'ONLINE':
      return 'secondary';
    default:
      return 'outline';
  }
};

const formatOrderStatus = (status: string) => {
  return status.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
};

export const orderColumns: ColumnDef<Order>[] = [
  {
    accessorKey: "orderNumber",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Order Number" />
    ),
    enableSorting: true,
    cell: ({ row }) => {
      const order = row.original;

      return (
        <div className="font-medium">
          {order.orderNumber || order.id}
          <Badge variant="outline" className="ml-2 text-xs">
            {order.source || 'ONLINE'}
          </Badge>
        </div>
      );
    },
  },
  {
    accessorKey: "source",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Source" />
    ),
    enableSorting: true,
    cell: ({ row }) => (
      <Badge variant={getSourceVariant(row.original.source)}>
        {row.original.source}
      </Badge>
    ),
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    enableSorting: true,
    cell: ({ row }) => (
      <Badge variant={getStatusVariant(row.original.status)}>
        {formatOrderStatus(row.original.status)}
      </Badge>
    ),
  },
  {
    accessorKey: "items",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Items" />
    ),
    enableSorting: false,
    cell: ({ row }) => {
      const items = row.original.items;
      const totalItems = items.reduce((sum, item) => sum + item.quantity, 0);
      
      return (
        <div className="flex items-center gap-2">
          <Package className="h-4 w-4 text-muted-foreground" />
          <span>{totalItems} item{totalItems !== 1 ? 's' : ''}</span>
        </div>
      );
    },
  },
  {
    accessorKey: "total",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Total" />
    ),
    enableSorting: true,
    cell: ({ row }) => (
      <div className="font-medium">
        AED {row.original.total.toFixed(2)}
      </div>
    ),
  },
  {
    accessorKey: "paymentMethod",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Payment" />
    ),
    enableSorting: true,
    cell: ({ row }) => {
      const paymentMethod = row.original.paymentMethod;
      if (!paymentMethod) return <span className="text-muted-foreground">-</span>;
      
      return (
        <div className="flex items-center gap-2">
          <CreditCard className="h-4 w-4 text-muted-foreground" />
          <span className="capitalize">
            {paymentMethod.replace(/_/g, ' ').toLowerCase()}
          </span>
        </div>
      );
    },
  },
  {
    accessorKey: "deliveryMethod",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Delivery" />
    ),
    enableSorting: true,
    cell: ({ row }) => {
      const deliveryMethod = row.original.deliveryMethod;
      if (!deliveryMethod) return <span className="text-muted-foreground">-</span>;
      
      return (
        <Badge variant="outline" className="capitalize">
          {deliveryMethod.toLowerCase()}
        </Badge>
      );
    },
  },
  {
    accessorKey: "createdAt",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Order Date" />
    ),
    enableSorting: true,
    cell: ({ row }) => {
      const date = new Date(row.original.createdAt);
      return (
        <div className="text-muted-foreground">
          {date.toLocaleDateString()} {date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </div>
      );
    }
  }
];
