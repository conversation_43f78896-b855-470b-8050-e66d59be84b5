'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Plus, Package, CheckCircle2, AlertCircle, Search, X, PlusCircle } from 'lucide-react';
import { DataTable } from '@/components/ui/data-table';
import { Input } from '@/components/ui/input';
import { Heading } from '@/components/ui/heading';
import { Separator } from '@/components/ui/separator';
import { AnalyticsCard } from '@/components/ui/analytics-card';
import DeleteDialog from '@/components/shared/DeleteDialog';
import { useToast } from '@/components/ui/use-toast';
import { cn } from '@/lib/utils';
import { useAuth } from '@/hooks/use-auth';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import AddonGroupForm from '@/components/product-addons/AddonGroupForm';
import { Dialog, DialogContent, DialogDescription, DialogHeader, <PERSON>alogTitle, DialogTrigger } from '@/components/ui/dialog';
import api from '@/lib/api';

// Types for our addons and groups
interface ProductAddonSubOption {
  id: string;
  name: string;
  price: number;
  description?: string;
  optionId: string;
}

interface ProductAddonOption {
  id: string;
  name: string;
  price: number;
  allowsCustomText: boolean;
  customTextLabel?: string;
  maxTextLength?: number;
  subOptions?: ProductAddonSubOption[];
}

interface ProductAddon {
  id: string;
  name: string;
  description?: string;
  options: ProductAddonOption[];
  createdAt: string;
  updatedAt: string;
}

interface ProductAddonGroup {
  id: string;
  name: string;
  description?: string;
  maxSelections: number;
  required: boolean;
  position: number;
  addons: ProductAddon[];
  createdAt: string;
  updatedAt: string;
}

// Analytics interface
interface AddonAnalytics {
  totalAddons: number;
  totalGroups: number;
  totalOptions: number;
}

// API service for addons and groups
const addonApi = {
  getAddons: async (): Promise<ProductAddon[]> => {
    try {
      const response = await api.get('/api/product-addons');
      return response.data;
    } catch (error) {
      console.error('Error fetching addons:', error);
      throw new Error('Failed to fetch addons');
    }
  },
  
  getGroups: async (): Promise<ProductAddonGroup[]> => {
    try {
      const response = await api.get('/api/product-addon-groups');
      // Data includes sub-options by default
      return response.data;
    } catch (error) {
      console.error('Error fetching addon groups:', error);
      throw new Error('Failed to fetch addon groups');
    }
  },
  
  deleteAddon: async (id: string): Promise<void> => {
    try {
      await api.delete(`/api/product-addons/${id}`);
    } catch (error) {
      console.error('Error deleting addon:', error);
      throw new Error('Failed to delete addon');
    }
  },
  
  deleteGroup: async (id: string): Promise<void> => {
    try {
      await api.delete(`/api/product-addon-groups/${id}`);
    } catch (error) {
      console.error('Error deleting addon group:', error);
      throw new Error('Failed to delete addon group');
    }
  }
};

export default function ProductAddonsPage() {
  const router = useRouter();
  const { toast } = useToast();
  const { isSuperAdmin } = useAuth();
  const [activeTab, setActiveTab] = useState('addons');
  const [addons, setAddons] = useState<ProductAddon[]>([]);
  const [groups, setGroups] = useState<ProductAddonGroup[]>([]);
  const [analytics, setAnalytics] = useState<AddonAnalytics>({
    totalAddons: 0,
    totalGroups: 0,
    totalOptions: 0
  });
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<{ id: string, type: 'addon' | 'group' } | null>(null);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState<ProductAddonGroup | null>(null);

  // Fetch data
  const fetchData = async () => {
    setLoading(true);
    try {
      const [addonsData, groupsData] = await Promise.all([
        addonApi.getAddons(),
        addonApi.getGroups()
      ]);
      
      setAddons(addonsData);
      setGroups(groupsData);
      
      // Calculate analytics
      const totalOptions = addonsData.reduce((sum, addon) => sum + addon.options.length, 0);
      setAnalytics({
        totalAddons: addonsData.length,
        totalGroups: groupsData.length,
        totalOptions
      });
    } catch (error) {
      console.error('Error fetching data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load product addons data',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  // Handle delete
  const handleDelete = async () => {
    if (!itemToDelete) return;
    
    try {
      if (itemToDelete.type === 'addon') {
        await addonApi.deleteAddon(itemToDelete.id);
        setAddons(addons.filter(addon => addon.id !== itemToDelete.id));
      } else {
        await addonApi.deleteGroup(itemToDelete.id);
        setGroups(groups.filter(group => group.id !== itemToDelete.id));
      }
      
      toast({
        title: 'Success',
        description: `${itemToDelete.type === 'addon' ? 'Addon' : 'Addon group'} deleted successfully`,
      });
      
      // Refresh analytics
      const updatedAddons = itemToDelete.type === 'addon' 
        ? addons.filter(addon => addon.id !== itemToDelete.id)
        : addons;
        
      const updatedGroups = itemToDelete.type === 'group'
        ? groups.filter(group => group.id !== itemToDelete.id)
        : groups;
        
      const totalOptions = updatedAddons.reduce((sum, addon) => sum + addon.options.length, 0);
      
      setAnalytics({
        totalAddons: updatedAddons.length,
        totalGroups: updatedGroups.length,
        totalOptions
      });
    } catch (error) {
      console.error('Error deleting item:', error);
      toast({
        title: 'Error',
        description: `Failed to delete ${itemToDelete.type}`,
        variant: 'destructive'
      });
    } finally {
      setDeleteDialogOpen(false);
      setItemToDelete(null);
    }
  };

  // Filter addons based on search query
  const filteredAddons = addons.filter(addon => 
    addon.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    addon.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Filter groups based on search query
  const filteredGroups = groups.filter(group => 
    group.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    group.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Columns for addons table
  const addonColumns = [
    {
      accessorKey: 'name',
      header: 'Name',
    },
    {
      accessorKey: 'description',
      header: 'Description',
      cell: ({ row }: any) => <span>{row.original.description || 'N/A'}</span>
    },
    {
      accessorKey: 'options',
      header: 'Options',
      cell: ({ row }: any) => <span>{row.original.options.length}</span>
    },
    {
      id: 'actions',
      cell: ({ row }: any) => (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setSelectedGroup(row.original);
              setEditDialogOpen(true);
            }}
          >
            Edit
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={() => {
              setItemToDelete({ id: row.original.id, type: 'addon' });
              setDeleteDialogOpen(true);
            }}
          >
            Delete
          </Button>
        </div>
      )
    }
  ];

  // Columns for groups table
  const groupColumns = [
    {
      accessorKey: 'name',
      header: 'Name',
    },
    {
      accessorKey: 'description',
      header: 'Description',
      cell: ({ row }: any) => <span>{row.original.description || 'N/A'}</span>
    },
    {
      accessorKey: 'maxSelections',
      header: 'Max Selections',
    },
    {
      accessorKey: 'required',
      header: 'Required',
      cell: ({ row }: any) => <span>{row.original.required ? 'Yes' : 'No'}</span>
    },
    {
      accessorKey: 'addons',
      header: 'Addons',
      cell: ({ row }: any) => <span>{row.original.addons.length}</span>
    },
    {
      id: 'actions',
      cell: ({ row }: any) => (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setSelectedGroup(row.original);
              setEditDialogOpen(true);
            }}
          >
            Edit
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={() => {
              setItemToDelete({ id: row.original.id, type: 'group' });
              setDeleteDialogOpen(true);
            }}
          >
            Delete
          </Button>
        </div>
      )
    }
  ];

  return (
    <div className="flex-1 space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <Heading 
            title="Product Addons" 
            description="Manage your product addons and addon groups" 
          />
        </div>
        <div className="flex items-center gap-4">
          <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <PlusCircle className="mr-2 h-4 w-4" /> Create Addon Group
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Create Addon Group</DialogTitle>
                <DialogDescription>
                  Create a new addon group with multiple options in one step
                </DialogDescription>
              </DialogHeader>
              <AddonGroupForm onSuccess={() => {
                setCreateDialogOpen(false);
                fetchData();
              }} />
            </DialogContent>
          </Dialog>
        </div>
      </div>
      <Separator />
      
      <div className="grid gap-4 grid-cols-1 md:grid-cols-3">
        <AnalyticsCard 
          title="Total Addons" 
          value={analytics.totalAddons.toString()} 
          icon={Package}
        />
        <AnalyticsCard 
          title="Total Groups" 
          value={analytics.totalGroups.toString()} 
          icon={CheckCircle2}
        />
        <AnalyticsCard 
          title="Total Options" 
          value={analytics.totalOptions.toString()} 
          icon={AlertCircle}
        />
      </div>
      
      <div className="flex items-center gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search addons and groups..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          {searchQuery && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full"
              onClick={() => setSearchQuery('')}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
      
      <Tabs defaultValue="groups" onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="groups">Addon Groups</TabsTrigger>
        </TabsList>
        <TabsContent value="groups" className="space-y-4">
          <DataTable
            columns={groupColumns}
            data={filteredGroups}
            loading={loading}
            searchKey="name"
          />
        </TabsContent>
      </Tabs>
      
      {/* Edit Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Addon Group</DialogTitle>
            <DialogDescription>
              Edit this addon group and its options
            </DialogDescription>
          </DialogHeader>
          <AddonGroupForm 
            onSuccess={() => {
              setEditDialogOpen(false);
              fetchData();
            }} 
            groupToEdit={selectedGroup}
          />
        </DialogContent>
      </Dialog>

      <DeleteDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleDelete}
        title={`Delete ${itemToDelete?.type === 'addon' ? 'Addon' : 'Addon Group'}`}
        description={`Are you sure you want to delete this ${itemToDelete?.type === 'addon' ? 'addon' : 'addon group'}? This action cannot be undone.`}
      />
    </div>
  );
}
