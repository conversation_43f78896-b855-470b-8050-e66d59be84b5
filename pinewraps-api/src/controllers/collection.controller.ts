import { Request, Response } from 'express';
import { prisma } from '../lib/prisma';
import { generateSlug } from '../utils/slug';
import { CollectionStatus } from '@prisma/client';
import { RedirectService } from '../services/redirect.service';

export class CollectionController {
  // Create a new collection
  async create(req: Request, res: Response) {
    try {
      const { name, description, content, products = [], seoTitle, seoDescription, seoKeywords, image, status } = req.body;

      const collection = await prisma.collection.create({
        data: {
          name,
          slug: generateSlug(name),
          description,
          content,
          seoTitle: seoTitle || name,
          seoDescription: seoDescription || description,
          seoKeywords: seoKeywords || [],
          image,
          status: (status as CollectionStatus) || 'DRAFT',
          products: {
            create: products.map((productId: string, index: number) => ({
              productId,
              position: index
            }))
          }
        },
        include: {
          products: {
            include: {
              product: {
                include: {
                  images: true
                }
              }
            }
          }
        }
      });

      res.json(collection);
    } catch (error) {
      console.error('Error creating collection:', error);
      res.status(500).json({ error: 'Failed to create collection' });
    }
  }

  // Get all collections
  async getAll(req: Request, res: Response) {
    try {
      const { status, page = '1', limit = '10' } = req.query;
      
      // Parse pagination parameters
      const pageNumber = parseInt(page as string, 10);
      const pageSize = parseInt(limit as string, 10);
      const skip = (pageNumber - 1) * pageSize;
      
      // Get total count for pagination
      const totalCollections = await prisma.collection.count({
        where: status ? { status: status as CollectionStatus } : undefined,
      });
      
      // Get paginated collections
      const collections = await prisma.collection.findMany({
        where: status ? { status: status as CollectionStatus } : undefined,
        include: {
          products: {
            include: {
              product: {
                include: {
                  images: true
                }
              }
            },
            orderBy: {
              position: 'asc'
            }
          }
        },
        skip,
        take: pageSize,
        orderBy: {
          createdAt: 'desc'
        }
      });

      // Return paginated response
      res.json({
        data: collections,
        total: totalCollections,
        page: pageNumber,
        limit: pageSize
      });
    } catch (error) {
      console.error('Error fetching collections:', error);
      res.status(500).json({ error: 'Failed to fetch collections' });
    }
  }

  // Get collection by ID
  async getById(req: Request, res: Response) {
    try {
      const { id } = req.params;

      const collection = await prisma.collection.findUnique({
        where: { id },
        include: {
          products: {
            include: {
              product: {
                include: {
                  images: true
                }
              }
            },
            orderBy: {
              position: 'asc'
            }
          }
        }
      });

      if (!collection) {
        return res.status(404).json({ error: 'Collection not found' });
      }

      res.json(collection);
    } catch (error) {
      console.error('Error fetching collection:', error);
      res.status(500).json({ error: 'Failed to fetch collection' });
    }
  }

  // Get collection by slug
  async getBySlug(req: Request, res: Response) {
    try {
      const { slug } = req.params;

      const collection = await prisma.collection.findUnique({
        where: { slug },
        include: {
          products: {
            include: {
              product: {
                include: {
                  images: true
                }
              }
            },
            orderBy: {
              position: 'asc'
            }
          }
        }
      });

      if (!collection) {
        return res.status(404).json({ error: 'Collection not found' });
      }

      res.json(collection);
    } catch (error) {
      console.error('Error fetching collection:', error);
      res.status(500).json({ error: 'Failed to fetch collection' });
    }
  }

  // Update collection
  async update(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { name, slug, description, content, products, seoTitle, seoDescription, seoKeywords, image, status } = req.body;

      // Get existing collection to check for slug changes
      const existingCollection = await prisma.collection.findUnique({
        where: { id }
      });

      if (!existingCollection) {
        return res.status(404).json({ error: 'Collection not found' });
      }

      // Handle slug update - only update if explicitly provided
      let newSlug = existingCollection.slug; // Keep existing slug by default
      if (slug !== undefined) {
        // If a slug is explicitly provided (even if empty), use it
        newSlug = slug ? generateSlug(slug) : generateSlug(name || existingCollection.name);
      }

      // Update the collection in a transaction
      const collection = await prisma.$transaction(async (prisma) => {
        // Create redirect if slug changed
        if (newSlug !== existingCollection.slug) {
          await RedirectService.createSlugChangeRedirect(
            existingCollection.slug,
            newSlug,
            'collection',
            id,
            `Collection slug changed from ${existingCollection.slug} to ${newSlug}`
          );
        }

        // Update the collection
        return await prisma.collection.update({
          where: { id },
          data: {
            name,
            description,
            content,
            seoTitle,
            seoDescription,
            seoKeywords,
            image,
            status: status as CollectionStatus,
            slug: newSlug
          }
        });
      });

      // If products array is provided, update product associations
      if (products) {
        // Remove existing products
        await prisma.collectionProduct.deleteMany({
          where: { collectionId: id }
        });

        // Add new products
        await prisma.collectionProduct.createMany({
          data: products.map((productId: string, index: number) => ({
            collectionId: id,
            productId,
            position: index
          }))
        });
      }

      // Get updated collection with products
      const updatedCollection = await prisma.collection.findUnique({
        where: { id },
        include: {
          products: {
            include: {
              product: {
                include: {
                  images: true
                }
              }
            },
            orderBy: {
              position: 'asc'
            }
          }
        }
      });

      res.json(updatedCollection);
    } catch (error) {
      console.error('Error updating collection:', error);
      res.status(500).json({ error: 'Failed to update collection' });
    }
  }

  // Delete collection
  async delete(req: Request, res: Response) {
    try {
      const { id } = req.params;

      await prisma.collection.delete({
        where: { id }
      });

      res.json({ message: 'Collection deleted successfully' });
    } catch (error) {
      console.error('Error deleting collection:', error);
      res.status(500).json({ error: 'Failed to delete collection' });
    }
  }

  // Update product positions in collection
  async updateProductPositions(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { products } = req.body;

      // Update positions in transaction
      await prisma.$transaction(
        products.map((item: { productId: string; position: number }) =>
          prisma.collectionProduct.update({
            where: {
              productId_collectionId: {
                productId: item.productId,
                collectionId: id
              }
            },
            data: { position: item.position }
          })
        )
      );

      res.json({ message: 'Product positions updated successfully' });
    } catch (error) {
      console.error('Error updating product positions:', error);
      res.status(500).json({ error: 'Failed to update product positions' });
    }
  }
}
