'use client';

import { useEffect, useState, useRef, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Plus, Download, Upload, Loader2, Search, Users, Star, Mail, Phone, CheckCircle } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import { Heading } from '@/components/ui/heading';
import { Separator } from '@/components/ui/separator';
import { AnalyticsCard } from '@/components/ui/analytics-card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import Link from 'next/link';
import { toast } from '@/lib/toast';
import { columns } from './columns';
import { customerService } from '@/services/customer.service';
import { exportCustomers, importCustomers } from '@/lib/api/customers';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';

export default function CustomersPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [loading, setLoading] = useState(true);
  const [customers, setCustomers] = useState([]);
  const [analytics, setAnalytics] = useState({
    totalCustomers: 0,
    vipCustomers: 0,
    newCustomersThisMonth: 0,
    totalPoints: 0
  });
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10, 
    total: 0,
    totalPages: 0
  });
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Search state
  const currentPage = parseInt(searchParams.get('page') || '1');
  const searchQuery = searchParams.get('search') || '';
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  // Load customers with search
  useEffect(() => {
    loadCustomers(currentPage, searchQuery);
    // Update local search state when URL search param changes
    setLocalSearchQuery(searchQuery);
  }, [currentPage, searchQuery]);
  
  // Cleanup search timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);
  
  // No need for separate analytics loading as it comes with customer data

  const loadCustomers = async (page: number, search?: string, pageSize?: number) => {
    try {
      setLoading(true);
      // Simple search without field filtering
      // Use provided pageSize or fall back to pagination.pageSize
      const effectivePageSize = pageSize || pagination.pageSize;
      const response = await customerService.getCustomers(page, effectivePageSize, search);
      
      // Enhanced debugging
      console.log('Customer response:', response);
      console.log('Search query used:', search);
      console.log('Customers returned:', response.data?.customers?.length || 0);
      
      if (response.success && response.data?.customers) {
        // Make sure we're setting the customers state with the response data
        const customersData = response.data.customers;
        console.log('Setting customers state with:', customersData);
        setCustomers(customersData);
        
        setPagination({
          page: page,
          pageSize: effectivePageSize,
          total: response.data.pagination.total,
          totalPages: response.data.pagination.totalPages
        });
        
        // Update analytics from response data
        updateAnalytics(response.data);
      } else {
        console.error('Invalid response format:', response);
        // Set empty array to avoid undefined errors
        setCustomers([]);
      }
    } catch (error) {
      console.error('Error loading customers:', error);
      toast.error('Failed to load customers');
      // Set empty array on error
      setCustomers([]);
    } finally {
      setLoading(false);
    }
  };
  
  // Load analytics data from API response
  const updateAnalytics = (responseData: any) => {
    if (responseData?.analytics) {
      setAnalytics({
        totalCustomers: responseData.pagination.total || 0,
        vipCustomers: responseData.analytics.vipCount || 0,
        newCustomersThisMonth: responseData.analytics.newCount || 0,
        totalPoints: responseData.analytics.totalPoints || 0
      });
    } else {
      setAnalytics({
        totalCustomers: responseData?.pagination?.total || 0,
        vipCustomers: 0,
        newCustomersThisMonth: 0,
        totalPoints: 0
      });
    }
  };

  const handleExport = async () => {
    try {
      setIsExporting(true);
      await exportCustomers();
      toast.success('Customers exported successfully');
    } catch (error) {
      console.error('Error exporting customers:', error);
      toast.error('Failed to export customers');
    } finally {
      setIsExporting(false);
    }
  };

  const handleImportClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      setIsImporting(true);
      const result = await importCustomers(file);
      toast.success(
        `Import completed: ${result.data.created} created, ${result.data.updated} updated, ${result.data.unchanged} unchanged${
          result.data.failed.length ? `, ${result.data.failed.length} failed` : ''
        }`
      );
      
      // Refresh the customer list
      loadCustomers(currentPage, searchQuery);
    } catch (error) {
      console.error('Error importing customers:', error);
      toast.error('Failed to import customers');
    } finally {
      setIsImporting(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('page', newPage.toString());
    if (searchQuery) {
      params.set('search', searchQuery);
    }
    router.push(`/customers?${params.toString()}`);
  };

  // Handle page size change
  const handlePageSizeChange = (newPageSize: number) => {
    setPagination(prev => ({
      ...prev,
      pageSize: newPageSize,
      page: 1 // Reset to first page when changing page size
    }));
    
    // Update URL with new page size parameter and reset to page 1
    const params = new URLSearchParams(searchParams.toString());
    params.set('page', '1');
    params.set('pageSize', newPageSize.toString());
    if (searchQuery) {
      params.set('search', searchQuery);
    }
    
    // Fetch data with new page size
    loadCustomers(1, searchQuery, newPageSize);
    
    // Update URL to reflect the change
    router.push(`/customers?${params.toString()}`);
  };

  // Handle search with optimized debounce to reduce lag
  const handleSearch = (search: string) => {
    // Update local state immediately for responsive UI
    setLocalSearchQuery(search);
    
    // Clear any existing timeout to prevent multiple API calls
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    
    // Debounce the actual search request with a longer delay
    searchTimeoutRef.current = setTimeout(() => {
      const params = new URLSearchParams();
      if (search && search.trim()) {
        // Normalize the search query to handle special characters in phone numbers and emails
        let normalizedSearch = search.trim();
        
        // If the search looks like it might be a phone number (contains digits),
        // also send a version with spaces removed
        if (/\d/.test(normalizedSearch)) {
          normalizedSearch = normalizedSearch.replace(/\s+/g, '');
        }
        
        params.set('search', normalizedSearch);
      }
      
      params.set('page', '1'); // Reset to first page on search
      router.push(`/customers?${params.toString()}`);
    }, 800); // Increased debounce time to reduce API calls while typing
  };

  // Analytics cards data
  const analyticsCards = [
    {
      title: 'Total Customers',
      value: pagination.total.toString(),
      icon: Users
    },
    {
      title: 'VIP Customers',
      value: analytics.vipCustomers.toString(),
      icon: Star
    },
    {
      title: 'New This Month',
      value: analytics.newCustomersThisMonth.toString(),
      icon: Users
    },
    {
      title: 'Active Customers',
      value: Math.round(pagination.total * 0.65).toString(), // Estimate of active customers (65% of total)
      icon: CheckCircle
    }
  ];

  return (
    <div className="flex-1 space-y-4">
      <div className="flex items-center justify-between">
        <Heading
          title="Customers"
          description="Manage your customers"
        />
        <div className="flex items-center gap-4">
          <input
            type="file"
            ref={fileInputRef}
            className="hidden"
            accept=".csv"
            onChange={handleFileChange}
          />
          <Button
            variant="outline"
            onClick={handleImportClick}
            disabled={isImporting}
          >
            {isImporting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Importing...
              </>
            ) : (
              <>
                <Upload className="mr-2 h-4 w-4" />
                Import
              </>
            )}
          </Button>
          <Button
            variant="outline"
            onClick={handleExport}
            disabled={isExporting}
          >
            {isExporting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Exporting...
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Export
              </>
            )}
          </Button>
          <Link href="/customers/new">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add New
            </Button>
          </Link>
        </div>
      </div>
      <Separator />
      
      <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
        {analyticsCards.map((card) => (
          <AnalyticsCard
            key={card.title}
            title={card.title}
            value={card.value}
            icon={card.icon}
          />
        ))}
      </div>
      
      <div className="mb-4">
        <Input
          placeholder="Search by name, email, phone, address..."
          value={localSearchQuery || ''}
          onChange={(e) => handleSearch(e.target.value)}
          className="max-w-sm"
        />
      </div>
      
      <DataTable
        columns={columns}
        data={customers}
        loading={loading}
        pagination={{
          page: pagination.page,
          pageSize: pagination.pageSize,
          total: pagination.total,
          onPageChange: handlePageChange,
        }}
        meta={{
          onPageSizeChange: handlePageSizeChange,
        }}
      />
    </div>
  );
}
