import { Request, Response } from 'express';
import { parse } from 'csv-parse/sync';
import { stringify } from 'csv-stringify/sync';
import { prisma } from '../lib/prisma';
import { PaymentTerms, PurchaseOrderStatus } from '@prisma/client';
class BadRequestError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'BadRequestError';
  }
}

interface PurchaseOrderRow {
  'Order Number': string;
  'Supplier Name': string;
  'Supplier Code': string;
  'Supplier Email'?: string;
  'Supplier Phone'?: string;
  'Invoice Number': string;
  'Invoice Date (YYYY-MM-DD)': string;
  'Payment Terms': string;
  'Payment Reference': string;
  'Bank Payment Reference': string;
  'Bank Payment Reference Date (YYYY-MM-DD)': string;
  'Additional Charge': string;
  'Tax': string;
  'Subtotal'?: string;
  'Total'?: string;
  'Status': string;
  'Status Updated At'?: string;
  'Status Updated By'?: string;
  'Notes': string;
  'Created At'?: string;
  'Updated At'?: string;
  'Item ID'?: string;
  'Item SKU': string;
  'Item Name'?: string;
  'Item Description'?: string;
  'Item Category'?: string;
  'Item Unit'?: string;
  'Item Current Stock'?: string;
  'Item Quantity': string;
  'Item Unit Price': string;
  'Item Total': string;
  'Item Notes': string;
}

export const exportPurchaseOrders = async (req: Request, res: Response) => {
  try {
    // Fetch all purchase orders with their items and supplier
    const purchaseOrders = await prisma.purchaseOrder.findMany({
      where: {
        isActive: true,
      },
      include: {
        supplier: true,
        items: {
          include: {
            item: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Transform data into simple CSV rows
    const rows: any[] = [];

    for (const po of purchaseOrders) {
      // If there are no items, add a row for the purchase order with empty item fields
      if (!po.items || po.items.length === 0) {
        rows.push({
          // Purchase Order Fields
          'Order Number': po.orderNumber,
          'Supplier Name': po.supplier?.name || '',
          'Supplier Code': po.supplier?.code || '',
          'Supplier Email': po.supplier?.email || '',
          'Supplier Phone': po.supplier?.phone || '',
          'Supplier Address': po.supplier?.address || '',
          'Invoice Number': po.invoiceNumber || '',
          'Invoice Date': po.invoiceDate ? po.invoiceDate.toISOString().split('T')[0] : '',
          'Invoice PDF URL': po.invoicePdfUrl || '',
          'Payment Terms': po.paymentTerms,
          'Status': po.status,
          'Subtotal': po.subtotal.toString(),
          'Tax': po.tax.toString(),
          'Additional Charge': po.additionalCharge.toString(),
          'Total': po.total.toString(),
          'Payment Reference': po.paymentReference || '',
          'Bank Payment Reference': po.bankPaymentReference || '',
          'Bank Payment Reference Date': po.bankPaymentReferenceDate 
            ? po.bankPaymentReferenceDate.toISOString().split('T')[0] 
            : '',
          'Notes': po.notes || '',
          'Created At': po.createdAt.toISOString().split('T')[0],
          'Updated At': po.updatedAt.toISOString().split('T')[0],
          'Created By': po.createdBy || '',
          'Updated By': po.updatedBy || '',
          
          // Empty Item Fields
          'Item ID': '',
          'Item SKU': '',
          'Item Name': '',
          'Item Quantity': '',
          'Item Unit Price': '',
          'Item Total': '',
          'Item Notes': '',
        });
      } else {
        // Add a row for each item in the purchase order
        for (const item of po.items) {
          rows.push({
            // Purchase Order Fields
            'Order Number': po.orderNumber,
            'Supplier Name': po.supplier?.name || '',
            'Supplier Code': po.supplier?.code || '',
            'Supplier Email': po.supplier?.email || '',
            'Supplier Phone': po.supplier?.phone || '',
            'Supplier Address': po.supplier?.address || '',
            'Invoice Number': po.invoiceNumber || '',
            'Invoice Date': po.invoiceDate ? po.invoiceDate.toISOString().split('T')[0] : '',
            'Invoice PDF URL': po.invoicePdfUrl || '',
            'Payment Terms': po.paymentTerms,
            'Status': po.status,
            'Subtotal': po.subtotal.toString(),
            'Tax': po.tax.toString(),
            'Additional Charge': po.additionalCharge.toString(),
            'Total': po.total.toString(),
            'Payment Reference': po.paymentReference || '',
            'Bank Payment Reference': po.bankPaymentReference || '',
            'Bank Payment Reference Date': po.bankPaymentReferenceDate 
              ? po.bankPaymentReferenceDate.toISOString().split('T')[0] 
              : '',
            'Notes': po.notes || '',
            'Created At': po.createdAt.toISOString().split('T')[0],
            'Updated At': po.updatedAt.toISOString().split('T')[0],
            'Created By': po.createdBy || '',
            'Updated By': po.updatedBy || '',
            
            // Item Fields
            'Item ID': item.item.id,
            'Item SKU': item.item.sku,
            'Item Name': item.item.name,
            'Item Quantity': item.quantity.toString(),
            'Item Unit Price': item.unitPrice.toString(),
            'Item Total': item.total.toString(),
            'Item Notes': item.notes || '',
          });
        }
      }
    }

    // Convert to CSV with simple options
    const csvContent = stringify(rows, {
      header: true,
      delimiter: ','
    });

    // Set response headers for file download
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename=purchase-orders-export.csv');
    
    // Send the CSV content
    res.send(csvContent);
  } catch (error: any) {
    console.error('Export error:', error);
    res.status(500).json({
      message: 'Failed to export purchase orders',
      error: error.message
    });
  }
};

export const importPurchaseOrders = async (req: Request, res: Response) => {
  if (!req.file) {
    throw new BadRequestError('No file uploaded');
  }

  let records: PurchaseOrderRow[] = [];
  let orderRecords: any[] = [];
  let itemRecords: any[] = [];
  let isZipFile = false;

  // Check if the uploaded file is a ZIP file
  if (req.file.mimetype === 'application/zip' || 
      req.file.originalname.toLowerCase().endsWith('.zip')) {
    isZipFile = true;
    const JSZip = require('jszip');
    const zip = new JSZip();
    
    try {
      // Load the zip file
      const zipContent = await zip.loadAsync(req.file.buffer);
      
      // Extract purchase orders CSV
      const ordersFile = zipContent.file('purchase-orders.csv');
      if (!ordersFile) {
        throw new BadRequestError('Missing purchase-orders.csv in the ZIP file');
      }
      
      const ordersContent = await ordersFile.async('string');
      orderRecords = parse(ordersContent, {
        columns: true,
        skip_empty_lines: true,
        trim: true,
      });
      
      // Extract items CSV if it exists
      const itemsFile = zipContent.file('purchase-order-items.csv');
      if (itemsFile) {
        const itemsContent = await itemsFile.async('string');
        itemRecords = parse(itemsContent, {
          columns: true,
          skip_empty_lines: true,
          trim: true,
        });
      }
    } catch (error: any) {
      throw new BadRequestError(`Failed to process ZIP file: ${error.message}`);
    }
  } else {
    // Handle regular CSV file (old format)
    const csvContent = req.file.buffer.toString();
    records = parse(csvContent, {
      columns: true,
      skip_empty_lines: true,
      trim: true,
    }) as PurchaseOrderRow[];
  }

  // Process the records based on the file format
  let groupedRecords: Record<string, PurchaseOrderRow[]> = {};
  
  if (isZipFile) {
    // For ZIP format, group by order number
    for (const order of orderRecords) {
      const orderNumber = order['Order Number'];
      if (!orderNumber) {
        continue; // Skip orders without an order number
      }
      
      // Find all items for this order
      const orderItems = itemRecords.filter(item => item['Order Number'] === orderNumber);
      
      // Create combined records that match our expected format
      const combinedRecords: PurchaseOrderRow[] = [];
      
      if (orderItems.length > 0) {
        // Create a record for each item
        for (const item of orderItems) {
          combinedRecords.push({
            'Order Number': orderNumber,
            'Supplier Name': order['Supplier Name'],
            'Supplier Code': order['Supplier Code'],
            'Invoice Number': order['Invoice Number'],
            'Invoice Date (YYYY-MM-DD)': order['Invoice Date'],
            'Payment Terms': order['Payment Terms'],
            'Payment Reference': order['Payment Reference'],
            'Bank Payment Reference': order['Bank Payment Reference'],
            'Bank Payment Reference Date (YYYY-MM-DD)': order['Bank Payment Reference Date'],
            'Additional Charge': order['Additional Charge'],
            'Tax': order['Tax'],
            'Status': order['Status'],
            'Notes': order['Notes'],
            'Item SKU': item['Item SKU'],
            'Item Quantity': item['Quantity'],
            'Item Unit Price': item['Unit Price'],
            'Item Total': item['Total'],
            'Item Notes': item['Notes']
          } as any);
        }
      } else {
        // Create a record with no items
        combinedRecords.push({
          'Order Number': orderNumber,
          'Supplier Name': order['Supplier Name'],
          'Supplier Code': order['Supplier Code'],
          'Invoice Number': order['Invoice Number'],
          'Invoice Date (YYYY-MM-DD)': order['Invoice Date'],
          'Payment Terms': order['Payment Terms'],
          'Payment Reference': order['Payment Reference'],
          'Bank Payment Reference': order['Bank Payment Reference'],
          'Bank Payment Reference Date (YYYY-MM-DD)': order['Bank Payment Reference Date'],
          'Additional Charge': order['Additional Charge'],
          'Tax': order['Tax'],
          'Status': order['Status'],
          'Notes': order['Notes'],
          'Item SKU': '',
          'Item Quantity': '',
          'Item Unit Price': '',
          'Item Total': '',
          'Item Notes': ''
        } as any);
      }
      
      groupedRecords[orderNumber] = combinedRecords;
    }
  } else {
    // For old format CSV, group records by order number or supplier+invoice
    groupedRecords = records.reduce((acc, row) => {
      const key = row['Order Number'] || `${row['Supplier Name']}_${row['Invoice Number']}`;
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(row);
      return acc;
    }, {} as Record<string, PurchaseOrderRow[]>);
  }

  const results = {
    success: 0,
    failed: 0,
    errors: [] as string[],
  };

  // Process each group as a separate purchase order
  for (const [key, items] of Object.entries(groupedRecords)) {
    try {
      const firstItem = items[0];
      
      // Validate supplier
      const supplier = await prisma.supplier.findFirst({
        where: { name: firstItem['Supplier Name'] },
      });
      
      if (!supplier) {
        throw new Error(`Supplier not found: ${firstItem['Supplier Name']}`);
      }

      // Validate payment terms
      const paymentTerms = firstItem['Payment Terms'] as PaymentTerms;
      if (!Object.values(PaymentTerms).includes(paymentTerms)) {
        throw new Error(`Invalid payment terms: ${paymentTerms}`);
      }

      // Use provided order number or generate a new one
      let orderNumber = firstItem['Order Number'];
      
      if (!orderNumber) {
        const today = new Date();
        const datePart = today.toISOString().slice(0, 10).replace(/-/g, '');
        
        // Get the latest order number for today
        const latestOrder = await prisma.purchaseOrder.findFirst({
          where: {
            orderNumber: {
              startsWith: `PO-${datePart}`,
            },
          },
          orderBy: {
            orderNumber: 'desc',
          },
        });

        let sequence = 1;
        if (latestOrder) {
          const lastSequence = parseInt(latestOrder.orderNumber.split('-')[2]);
          sequence = lastSequence + 1;
        }

        orderNumber = `PO-${datePart}-${sequence.toString().padStart(3, '0')}`;
      } else {
        // Verify the order number doesn't already exist
        const existingOrder = await prisma.purchaseOrder.findUnique({
          where: { orderNumber },
        });
        
        if (existingOrder) {
          throw new Error(`Order number ${orderNumber} already exists`);
        }
      }

      // Create purchase order with items
      await prisma.$transaction(async (tx) => {
        // Create purchase order
        const purchaseOrder = await tx.purchaseOrder.create({
          data: {
            orderNumber,
            supplierId: supplier.id,
            invoiceNumber: firstItem['Invoice Number'] || null,
            invoiceDate: firstItem['Invoice Date (YYYY-MM-DD)']
              ? new Date(firstItem['Invoice Date (YYYY-MM-DD)'])
              : null,
            paymentTerms,
            status: (firstItem['Status'] as PurchaseOrderStatus) || 'DRAFT',
            notes: firstItem['Notes'] || null,
            paymentReference: firstItem['Payment Reference'] || null,
            bankPaymentReference: firstItem['Bank Payment Reference'] || null,
            bankPaymentReferenceDate: firstItem['Bank Payment Reference Date (YYYY-MM-DD)']
              ? new Date(firstItem['Bank Payment Reference Date (YYYY-MM-DD)'])
              : null,
            additionalCharge: firstItem['Additional Charge'] 
              ? parseFloat(firstItem['Additional Charge']) 
              : 0,
            tax: firstItem['Tax'] 
              ? parseFloat(firstItem['Tax']) 
              : 0,
          },
        });

        // Process items
        let subtotal = 0;
        for (const item of items) {
          // Validate item
          // Support both new and old format field names
          const itemSku = item['Item SKU'] || item['Item Code*'] || '';
          if (!itemSku) {
            throw new Error('Item SKU is required');
          }
          
          const inventoryItem = await tx.inventoryItem.findFirst({
            where: { sku: itemSku, isActive: true },
          });

          if (!inventoryItem) {
            throw new Error(`Item with code ${itemSku} not found`);
          }

          // Support both new and old format field names
          const quantityStr = item['Item Quantity'] || item['Quantity*'] || '';
          const unitPriceStr = item['Item Unit Price'] || item['Unit Price*'] || '';
          
          if (!quantityStr) {
            throw new Error(`Quantity is required for item ${itemSku}`);
          }
          
          if (!unitPriceStr) {
            throw new Error(`Unit price is required for item ${itemSku}`);
          }
          
          const quantity = parseInt(quantityStr);
          const unitPrice = parseFloat(unitPriceStr);

          if (isNaN(quantity) || quantity <= 0) {
            throw new Error(`Invalid quantity for item ${itemSku}`);
          }

          if (isNaN(unitPrice) || unitPrice <= 0) {
            throw new Error(`Invalid unit price for item ${itemSku}`);
          }

          const calculatedTotal = quantity * unitPrice;
          const providedTotal = parseFloat(item['Item Total'] || '0');
          
          // Validate that the provided total matches the calculated total (within a small margin for rounding)
          if (providedTotal > 0 && Math.abs(providedTotal - calculatedTotal) > 0.01) {
            throw new Error(`Total mismatch for item ${itemSku}: calculated ${calculatedTotal} but got ${providedTotal}`);
          }
          
          const total = calculatedTotal;
          subtotal += total;

          await tx.purchaseOrderItem.create({
            data: {
              purchaseOrderId: purchaseOrder.id,
              itemId: inventoryItem.id,
              quantity,
              unitPrice,
              total,
              notes: item['Item Notes'] || null,
            },
          });
        }

        // Update purchase order totals
        await tx.purchaseOrder.update({
          where: { id: purchaseOrder.id },
          data: {
            subtotal,
            total: subtotal, // Add tax and additional charges if needed
          },
        });
      });

      results.success++;
    } catch (error: any) {
      results.failed++;
      results.errors.push(`Error processing order for supplier ${items[0]['Supplier Name'] || items[0]['Supplier Code'] || 'Unknown'}: ${error.message}`);
    }
  }

  res.json({
    message: 'Import completed',
    data: results,
  });
};
