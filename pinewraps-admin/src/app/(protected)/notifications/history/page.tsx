'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { Badge } from '@/components/ui/badge';
import { Bell, RefreshCw, Users, User, Award, Crown, Eye } from 'lucide-react';
import { toast } from '@/lib/toast';
import { cn } from '@/lib/utils';
import { 
  getPushNotifications,
  resendPushNotification,
  PushNotificationDto,
  NotificationStatus,
  getNotificationStatusBadge
} from '@/lib/utils/notifications';
import Link from 'next/link';

export default function NotificationsHistoryPage() {
  const [notifications, setNotifications] = useState<PushNotificationDto[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isResending, setIsResending] = useState<string | null>(null);

  const fetchNotifications = async () => {
    setIsLoading(true);
    try {
      const result = await getPushNotifications(page);
      setNotifications(result.notifications);
      setTotalPages(result.pagination.totalPages);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      toast.error('Failed to load notifications');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchNotifications();
  }, [page]);

  const handleResend = async (id: string, title: string) => {
    setIsResending(id);
    try {
      toast.loading('Resending notification... This may take a moment');
      
      await resendPushNotification(id);
      
      toast.dismiss();
      toast.success(
        `Notification resent successfully! Title: ${title}. It may take a moment to process all recipients`
      );
      
      // Fetch updated notifications
      fetchNotifications();
    } catch (error) {
      console.error('Error resending notification:', error);
      
      toast.dismiss();
      toast.error(
        `Failed to resend notification: ${error instanceof Error ? error.message : 'An unexpected error occurred'}`
      );
    } finally {
      setIsResending(null);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const getTargetTypeLabel = (targetType: string) => {
    switch (targetType) {
      case 'ALL':
        return { label: 'All Customers', icon: Users, color: 'bg-blue-100 text-blue-800' };
      case 'VIP':
        return { label: 'VIP Customers', icon: Crown, color: 'bg-purple-100 text-purple-800' };
      case 'REWARD_TIER':
        return { label: 'Reward Tier', icon: Award, color: 'bg-yellow-100 text-yellow-800' };
      case 'SPECIFIC_CUSTOMERS':
        return { label: 'Specific Customers', icon: User, color: 'bg-green-100 text-green-800' };
      default:
        return { label: 'Unknown', icon: Bell, color: 'bg-gray-100 text-gray-800' };
    }
  };

  const getStatusBadge = (status: NotificationStatus, sentAt?: string | null, error?: string | null) => {
    const statusInfo = getNotificationStatusBadge(status, sentAt);
    
    const badgeClass = `bg-${statusInfo.color}-100 text-${statusInfo.color}-800 border-${statusInfo.color}-200`;
    
    return (
      <div className="flex flex-col">
        <Badge variant="outline" className={badgeClass}>{statusInfo.label}</Badge>
        {statusInfo.timestamp && (
          <span className="text-xs text-gray-500 mt-1">
            {statusInfo.timestamp.toLocaleDateString()} {statusInfo.timestamp.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
          </span>
        )}
        {error && status !== NotificationStatus.SENT && (
          <span className="text-xs text-red-500 mt-1 truncate max-w-[150px]" title={error}>
            {error}
          </span>
        )}
      </div>
    );
  };

  return (
    <div className="p-6 max-w-[1400px] mx-auto">
      {/* Header */}
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Notification History</h1>
          <p className="text-sm text-gray-500 mt-1">
            View and manage all sent notifications
          </p>
        </div>
        <div className="flex space-x-2">
          <Button 
            variant="outline" 
            onClick={fetchNotifications}
            disabled={isLoading}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button asChild>
            <Link href="/notifications">
              <Bell className="h-4 w-4 mr-2" />
              Send New
            </Link>
          </Button>
        </div>
      </div>

      {/* Notifications Table */}
      <Card className="overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Title</TableHead>
                <TableHead>Target</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Recipients</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={6} className="h-24 text-center">
                    <div className="flex justify-center">
                      <svg className="animate-spin h-6 w-6 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    </div>
                  </TableCell>
                </TableRow>
              ) : notifications.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="h-24 text-center text-gray-500">
                    No notifications found
                  </TableCell>
                </TableRow>
              ) : (
                notifications.map((notification) => {
                  const targetType = getTargetTypeLabel(notification.targetType);
                  const IconComponent = targetType.icon;
                  
                  return (
                    <TableRow key={notification.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center space-x-2">
                          <div className={cn("p-2 rounded-full", targetType.color.split(' ')[0])}>
                            <Bell className="h-4 w-4" />
                          </div>
                          <div>
                            <div className="font-medium">{notification.title}</div>
                            <div className="text-xs text-gray-500 truncate max-w-[200px]">
                              {notification.body}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline" className={cn("flex items-center space-x-1", targetType.color)}>
                            <IconComponent className="h-3 w-3" />
                            <span>{targetType.label}</span>
                          </Badge>
                          {notification.targetType === 'REWARD_TIER' && notification.targetData?.tier && (
                            <Badge variant="outline" className="bg-gray-100 text-gray-800">
                              {notification.targetData.tier}
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(notification.status, notification.sentAt, notification.error)}
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {notification._count?.recipients || 0} recipients
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm text-gray-500">
                          {notification.createdAt ? formatDate(notification.createdAt) : 'N/A'}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            asChild
                          >
                            <Link href={`/notifications/details/${notification.id}`}>
                              <Eye className="h-3 w-3 mr-1" />
                              View
                            </Link>
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleResend(notification.id, notification.title)}
                            disabled={isResending === notification.id || notification.status === NotificationStatus.PENDING}
                          >
                            {isResending === notification.id ? (
                              <>
                                <svg className="animate-spin h-3 w-3 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                              </>
                            ) : (
                              <>
                                <RefreshCw className="h-3 w-3 mr-1" />
                                Resend
                              </>
                            )}
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
        </div>
        
        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-center py-4">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious 
                    href="#" 
                    onClick={(e) => {
                      e.preventDefault();
                      if (page > 1) setPage(page - 1);
                    }}
                    className={page === 1 ? 'pointer-events-none opacity-50' : ''}
                  />
                </PaginationItem>
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((pageNum) => (
                  <PaginationItem key={pageNum}>
                    <PaginationLink
                      href="#"
                      onClick={(e) => {
                        e.preventDefault();
                        setPage(pageNum);
                      }}
                      isActive={page === pageNum}
                    >
                      {pageNum}
                    </PaginationLink>
                  </PaginationItem>
                ))}
                <PaginationItem>
                  <PaginationNext 
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      if (page < totalPages) setPage(page + 1);
                    }}
                    className={page === totalPages ? 'pointer-events-none opacity-50' : ''}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </Card>
    </div>
  );
}
