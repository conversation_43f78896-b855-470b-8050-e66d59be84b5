import { useState, useCallback, useRef } from 'react';
import api from '@/lib/api';

interface InvoiceValidationState {
  isChecking: boolean;
  isUnique: boolean;
  existingOrder: {
    id: string;
    orderNumber: string;
    supplierName: string;
    createdAt: string;
  } | null;
  error: string | null;
}

interface UseInvoiceValidationOptions {
  excludeId?: string; // For edit mode - exclude current purchase order
  debounceMs?: number; // Debounce delay in milliseconds
}

export const useInvoiceValidation = (options: UseInvoiceValidationOptions = {}) => {
  const { excludeId, debounceMs = 500 } = options;
  
  const [validationState, setValidationState] = useState<InvoiceValidationState>({
    isChecking: false,
    isUnique: true,
    existingOrder: null,
    error: null,
  });

  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  const validateInvoiceNumber = useCallback(
    async (invoiceNumber: string) => {
      // Clear any existing timeout
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }

      // Cancel any ongoing request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // Skip validation for empty/null values
      if (!invoiceNumber || invoiceNumber.trim() === '') {
        setValidationState({
          isChecking: false,
          isUnique: true,
          existingOrder: null,
          error: null,
        });
        return;
      }

      // Set loading state immediately
      setValidationState(prev => ({
        ...prev,
        isChecking: true,
        error: null,
      }));

      // Debounce the actual API call
      debounceTimeoutRef.current = setTimeout(async () => {
        try {
          // Create new abort controller for this request
          abortControllerRef.current = new AbortController();

          const response = await api.post(
            '/api/purchase-orders/check-invoice-number',
            {
              invoiceNumber: invoiceNumber.trim(),
              excludeId: excludeId || null,
            },
            {
              signal: abortControllerRef.current.signal,
            }
          );

          if (response.data.success) {
            setValidationState({
              isChecking: false,
              isUnique: response.data.data.isUnique,
              existingOrder: response.data.data.existingOrder,
              error: null,
            });
          } else {
            setValidationState({
              isChecking: false,
              isUnique: true,
              existingOrder: null,
              error: response.data.message || 'Validation failed',
            });
          }
        } catch (error: any) {
          // Don't update state if request was aborted
          if (error.name === 'AbortError' || error.code === 'ERR_CANCELED') {
            return;
          }

          console.error('Invoice validation error:', error);
          setValidationState({
            isChecking: false,
            isUnique: true, // Default to true on error to not block form
            existingOrder: null,
            error: 'Failed to validate invoice number. Please try again.',
          });
        }
      }, debounceMs);
    },
    [excludeId, debounceMs]
  );

  // Cleanup function to clear timeouts and abort requests
  const cleanup = useCallback(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, []);

  // Reset validation state
  const reset = useCallback(() => {
    cleanup();
    setValidationState({
      isChecking: false,
      isUnique: true,
      existingOrder: null,
      error: null,
    });
  }, [cleanup]);

  // Manual validation trigger (for immediate validation)
  const validateNow = useCallback(
    async (invoiceNumber: string) => {
      cleanup(); // Clear any pending debounced calls
      
      if (!invoiceNumber || invoiceNumber.trim() === '') {
        reset();
        return;
      }

      setValidationState(prev => ({
        ...prev,
        isChecking: true,
        error: null,
      }));

      try {
        abortControllerRef.current = new AbortController();

        const response = await api.post(
          '/api/purchase-orders/check-invoice-number',
          {
            invoiceNumber: invoiceNumber.trim(),
            excludeId: excludeId || null,
          },
          {
            signal: abortControllerRef.current.signal,
          }
        );

        if (response.data.success) {
          setValidationState({
            isChecking: false,
            isUnique: response.data.data.isUnique,
            existingOrder: response.data.data.existingOrder,
            error: null,
          });
        } else {
          setValidationState({
            isChecking: false,
            isUnique: true,
            existingOrder: null,
            error: response.data.message || 'Validation failed',
          });
        }
      } catch (error: any) {
        if (error.name === 'AbortError' || error.code === 'ERR_CANCELED') {
          return;
        }

        console.error('Invoice validation error:', error);
        setValidationState({
          isChecking: false,
          isUnique: true,
          existingOrder: null,
          error: 'Failed to validate invoice number. Please try again.',
        });
      }
    },
    [excludeId, cleanup, reset]
  );

  return {
    validationState,
    validateInvoiceNumber,
    validateNow,
    reset,
    cleanup,
  };
};
