'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Heading } from '@/components/ui/heading';
import { Separator } from '@/components/ui/separator';
import { DataTable } from '@/components/ui/data-table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Plus, Filter } from 'lucide-react';
import { blogPostColumns } from './columns';
import { useQuery } from '@tanstack/react-query';
import api from '@/lib/api';
import { toast } from '@/components/ui/use-toast';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

export default function BlogPage() {
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
    total: 0,
  });
  const [postToDelete, setPostToDelete] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Fetch blog posts with pagination
  const { data, isLoading, refetch } = useQuery({
    queryKey: ['blog-posts', pagination.page, pagination.pageSize, statusFilter],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.pageSize.toString(),
      });

      // Add status filter if not 'all'
      if (statusFilter !== 'all') {
        params.append('status', statusFilter.toUpperCase());
      } else {
        // For admin panel, we want to see ALL posts including drafts
        params.append('status', 'ALL');
      }

      const response = await api.get(`/api/blog/posts?${params.toString()}`);
      return response.data;
    },
  });

  const posts = data?.data || [];

  // Update pagination total when data changes
  useEffect(() => {
    if (data?.pagination?.total !== undefined) {
      setPagination(prev => ({
        ...prev,
        total: data.pagination.total
      }));
    }
  }, [data]);

  // Handle pagination changes
  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
  };

  const handlePageSizeChange = (pageSize: number) => {
    setPagination(prev => ({ ...prev, pageSize, page: 1 }));
  };

  // Handle delete post
  const handleDeletePost = async () => {
    if (!postToDelete) return;

    try {
      await api.delete(`/api/blog/posts/${postToDelete}`);
      toast({
        title: 'Post deleted',
        description: 'The blog post has been deleted successfully.',
      });
      refetch();
    } catch (error) {
      console.error('Error deleting post:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete the blog post. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setPostToDelete(null);
      setIsDeleteDialogOpen(false);
    }
  };

  // Listen for delete events from the actions column
  useEffect(() => {
    const handleDeleteEvent = (event: CustomEvent) => {
      setPostToDelete(event.detail);
      setIsDeleteDialogOpen(true);
    };

    window.addEventListener('deleteBlogPost', handleDeleteEvent as EventListener);
    return () => {
      window.removeEventListener('deleteBlogPost', handleDeleteEvent as EventListener);
    };
  }, []);

  return (
    <div className="flex-col">
      <div className="flex-1 space-y-4">
        <div className="flex items-center justify-between">
          <Heading
            title="Blog Management"
            description="Manage your blog posts and categories"
          />
          <div className="flex items-center gap-4">
            <Link href="/blog/categories">
              <Button variant="outline">Manage Categories</Button>
            </Link>
            <Link href="/blog/posts/new">
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add Post
              </Button>
            </Link>
          </div>
        </div>

        {/* Status Filter */}
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-muted-foreground" />
          <Select
            value={statusFilter}
            onValueChange={(value) => {
              setStatusFilter(value);
              // Reset to first page when filter changes
              setPagination(prev => ({ ...prev, page: 1 }));
            }}
          >
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Posts</SelectItem>
              <SelectItem value="published">Published</SelectItem>
              <SelectItem value="draft">Draft</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <Separator />
        
        <DataTable
          columns={blogPostColumns}
          data={posts}
          loading={isLoading}
          pagination={{
            page: pagination.page,
            pageSize: pagination.pageSize,
            total: pagination.total,
            onPageChange: handlePageChange,
          }}
          meta={{
            onPageSizeChange: handlePageSizeChange,
          }}
          searchKey="title"
          searchPlaceholder="Search by title..."
        />

        <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete Blog Post</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete this blog post? This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDeletePost}
                className="bg-red-500 hover:bg-red-600"
              >
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  );
}
