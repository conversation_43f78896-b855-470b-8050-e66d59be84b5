'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Plus, Pencil, Trash2, CheckCircle2, XCircle } from 'lucide-react';
import { format } from 'date-fns';
import { useToast } from "@/components/ui/use-toast";
import { Heading } from "@/components/ui/heading";
import { Separator } from "@/components/ui/separator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { EmployeeModal } from "@/components/finance/employee-modal";
import { apiClient } from '@/lib/api/client';
import { Employee as EmployeeType } from '@/services/employee.service';

interface Employee extends EmployeeType {
  joinDate?: string;
  _count?: {
    salaries: number;
  };
  salaries?: number;
}

// Header component with consistent styling
const Header = ({ onAddClick }: { onAddClick: () => void }) => (
  <div className="flex items-center justify-between">
    <div>
      <Heading 
        title="Employee Management" 
        description="Manage employees and their information" 
      />
    </div>
    <Button onClick={onAddClick}>
      <Plus className="mr-2 h-4 w-4" />
      Add Employee
    </Button>
  </div>
);

// Employee list component with static table structure
const EmployeeList = ({
  loading,
  error,
  employees,
  onEdit,
  onDelete,
  onStatusChange,
}: {
  loading: boolean;
  error: string | null;
  employees: Employee[];
  onEdit: (employee: Employee) => void;
  onDelete: (id: string) => void;
  onStatusChange: (id: string, isActive: boolean) => void;
}) => {
  const tableHeaders = [
    { key: 'employee', label: 'Employee' },
    { key: 'position', label: 'Position' },
    { key: 'department', label: 'Department' },
    { key: 'joinDate', label: 'Join Date' },
    { key: 'status', label: 'Status' },
    { key: 'actions', label: 'Actions' },
  ];

  if (loading) {
    return (
      <div className="rounded-md border">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {tableHeaders.map((header) => (
                <th key={header.key} className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {header.label}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            <tr>
              <td colSpan={6} className="px-6 py-4 text-center">
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-md border">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {tableHeaders.map((header) => (
                <th key={header.key} className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {header.label}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            <tr>
              <td colSpan={6} className="px-6 py-4 text-center text-red-500">
                {error}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    );
  }

  return (
    <div className="rounded-md border">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {tableHeaders.map((header) => (
                <th key={header.key} className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {header.label}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {employees.length === 0 ? (
              <tr>
                <td colSpan={6} className="px-6 py-4 text-center text-gray-500">
                  No employees found
                </td>
              </tr>
            ) : (
              employees.map((employee) => (
                <tr key={employee.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {employee.firstName} {employee.lastName}
                    </div>
                    <div className="text-sm text-gray-500">{employee.email}</div>
                    {employee.phone && (
                      <div className="text-sm text-gray-500">{employee.phone}</div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{employee.position}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{employee.department || '-'}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500">
                      {format(new Date(employee.joinDate), 'MMM dd, yyyy')}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        className={`${employee.isActive ? 'text-green-600' : 'text-red-600'}`}
                        onClick={() => onStatusChange(employee.id, !employee.isActive)}
                      >
                        {employee.isActive ? (
                          <CheckCircle2 className="h-5 w-5" />
                        ) : (
                          <XCircle className="h-5 w-5" />
                        )}
                      </Button>
                      <span className={`${employee.isActive ? 'text-green-600' : 'text-red-600'}`}>
                        {employee.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onEdit(employee)}
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onDelete(employee.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default function EmployeesPage() {
  const { toast } = useToast();
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);

  const loadEmployees = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await apiClient.get(
        '/employees' + 
        (statusFilter === 'active' ? '?active=true' : statusFilter === 'inactive' ? '?active=false' : '')
      );
      
      setEmployees(response.data.data || []);
    } catch (err) {
      console.error('Failed to load employees:', err);
      setError('Failed to load employees. Please try again.');
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load employees. Please try again.",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadEmployees();
  }, [statusFilter]);

  const filteredEmployees = employees.filter(employee => {
    const matchesStatus = statusFilter === 'all' || 
      (statusFilter === 'active' && employee.isActive) || 
      (statusFilter === 'inactive' && !employee.isActive);

    const matchesSearch = searchQuery.trim() === '' || 
      employee.firstName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      employee.lastName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      employee.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      employee.position.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesStatus && matchesSearch;
  });

  const handleEdit = (employee: Employee) => {
    setSelectedEmployee(employee);
    setIsModalOpen(true);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this employee?')) {
      return;
    }

    try {
      await apiClient.delete(`/employees/${id}`);

      toast({
        title: "Success",
        description: "Employee deleted successfully",
      });

      loadEmployees();
    } catch (err) {
      console.error('Failed to delete employee:', err);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to delete employee. Please try again.",
      });
    }
  };

  const handleStatusChange = async (id: string, isActive: boolean) => {
    try {
      await apiClient.patch(`/employees/${id}`, { isActive });
      toast({
        title: "Success",
        description: `Employee ${isActive ? 'activated' : 'deactivated'} successfully`,
      });
      loadEmployees(); // Refresh the list
    } catch (error: any) {
      console.error('Error updating employee status:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.response?.data?.message || error.message || 'Failed to update employee status',
      });
    }
  };

  const handleModalClose = (refresh?: boolean) => {
    setIsModalOpen(false);
    setSelectedEmployee(null);
    if (refresh) {
      loadEmployees();
    }
  };

  return (
    <div className="flex-1 space-y-4">
      <Header onAddClick={() => setIsModalOpen(true)} />
      <Separator />
      
      <Card>
        <CardContent className="pt-6">
          <div className="flex gap-4 mb-6">
            <Select
              value={statusFilter}
              onValueChange={(value: 'all' | 'active' | 'inactive') => setStatusFilter(value)}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Employees</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>

            <div className="flex-1 max-w-sm">
              <input
                type="text"
                placeholder="Search employees..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-3 py-2 border rounded-md border-input bg-background text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
              />
            </div>
          </div>

          <EmployeeList
            loading={loading}
            error={error}
            employees={filteredEmployees}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onStatusChange={handleStatusChange}
          />
        </CardContent>
      </Card>

      <EmployeeModal
        open={isModalOpen}
        onClose={handleModalClose}
        employee={selectedEmployee}
      />
    </div>
  );
}
