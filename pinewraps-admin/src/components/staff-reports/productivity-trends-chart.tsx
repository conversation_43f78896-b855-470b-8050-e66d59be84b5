'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  AreaChart,
  Area
} from 'recharts';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';
import staffReportsService, { ProductivityTrend } from '@/services/staff-reports.service';

interface ProductivityTrendsChartProps {
  timeRange: string;
  department?: string;
  isLoading: boolean;
}

export default function ProductivityTrendsChart({ 
  timeRange, 
  department, 
  isLoading 
}: ProductivityTrendsChartProps) {
  const [trendsData, setTrendsData] = useState<ProductivityTrend[]>([]);
  const [isLoadingTrends, setIsLoadingTrends] = useState(false);

  useEffect(() => {
    const fetchTrends = async () => {
      try {
        setIsLoadingTrends(true);
        const data = await staffReportsService.getProductivityTrends({
          timeRange: timeRange as '7d' | '14d' | '30d' | '3m' | 'all' | 'custom',
          department: department === 'all' ? undefined : department as any,
        });
        setTrendsData(data);
      } catch (error) {
        console.error('Error fetching productivity trends:', error);
      } finally {
        setIsLoadingTrends(false);
      }
    };

    if (!isLoading) {
      fetchTrends();
    }
  }, [timeRange, department, isLoading]);

  const calculateTrend = () => {
    if (trendsData.length < 2) return { direction: 'stable', percentage: 0 };
    
    const recent = trendsData.slice(-3);
    const earlier = trendsData.slice(0, 3);
    
    const recentAvg = recent.reduce((sum, item) => sum + item.averageProductivity, 0) / recent.length;
    const earlierAvg = earlier.reduce((sum, item) => sum + item.averageProductivity, 0) / earlier.length;
    
    const percentage = earlierAvg > 0 ? ((recentAvg - earlierAvg) / earlierAvg) * 100 : 0;
    
    if (Math.abs(percentage) < 2) return { direction: 'stable', percentage: 0 };
    return { 
      direction: percentage > 0 ? 'up' : 'down', 
      percentage: Math.abs(percentage) 
    };
  };

  const trend = calculateTrend();

  const getTrendIcon = () => {
    switch (trend.direction) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-600" />;
      default:
        return <Minus className="h-4 w-4 text-gray-600" />;
    }
  };

  const getTrendColor = () => {
    switch (trend.direction) {
      case 'up':
        return 'text-green-600';
      case 'down':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    });
  };

  if (isLoading || isLoadingTrends) {
    return (
      <div className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Productivity Trends</CardTitle>
          </CardHeader>
          <CardContent>
            <Skeleton className="h-64 w-full" />
          </CardContent>
        </Card>
        
        <div className="grid gap-4 md:grid-cols-3">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-4">
                <Skeleton className="h-16 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Main Trends Chart */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Productivity Trends</CardTitle>
            <div className="flex items-center space-x-2">
              {getTrendIcon()}
              <span className={`text-sm font-medium ${getTrendColor()}`}>
                {trend.direction === 'stable' ? 'Stable' : 
                 `${trend.percentage.toFixed(1)}% ${trend.direction === 'up' ? 'increase' : 'decrease'}`}
              </span>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={trendsData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <defs>
                  <linearGradient id="productivityGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
                    <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1}/>
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="date" 
                  tickFormatter={formatDate}
                />
                <YAxis 
                  domain={[0, 100]}
                  tickFormatter={(value) => `${value}%`}
                />
                <Tooltip 
                  formatter={(value: number, name: string) => {
                    if (name === 'averageProductivity') return [`${value.toFixed(1)}%`, 'Productivity'];
                    if (name === 'totalOrders') return [value, 'Orders'];
                    return [value, name];
                  }}
                  labelFormatter={(label) => `Date: ${formatDate(label)}`}
                />
                <Area
                  type="monotone"
                  dataKey="averageProductivity"
                  stroke="#3b82f6"
                  fillOpacity={1}
                  fill="url(#productivityGradient)"
                  name="averageProductivity"
                />
                <Line
                  type="monotone"
                  dataKey="totalOrders"
                  stroke="#10b981"
                  strokeWidth={2}
                  dot={{ fill: '#10b981', strokeWidth: 2, r: 3 }}
                  name="totalOrders"
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Summary Statistics */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Average Productivity</p>
                <p className="text-2xl font-bold">
                  {trendsData.length > 0 
                    ? `${(trendsData.reduce((sum, item) => sum + item.averageProductivity, 0) / trendsData.length).toFixed(1)}%`
                    : '0%'
                  }
                </p>
              </div>
              <div className="text-right">
                <Badge variant="outline">
                  {staffReportsService.getTimeRangeDisplayName(timeRange)}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Orders</p>
                <p className="text-2xl font-bold">
                  {trendsData.reduce((sum, item) => sum + item.totalOrders, 0).toLocaleString()}
                </p>
              </div>
              <div className="text-right">
                <p className="text-sm text-muted-foreground">
                  Avg: {trendsData.length > 0 
                    ? Math.round(trendsData.reduce((sum, item) => sum + item.totalOrders, 0) / trendsData.length)
                    : 0
                  }/day
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Peak Performance</p>
                <p className="text-2xl font-bold">
                  {trendsData.length > 0 
                    ? `${Math.max(...trendsData.map(item => item.averageProductivity)).toFixed(1)}%`
                    : '0%'
                  }
                </p>
              </div>
              <div className="text-right">
                <p className="text-sm text-muted-foreground">
                  Best day
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Insights */}
      {trendsData.length === 0 && (
        <Card>
          <CardContent className="p-6">
            <div className="text-center text-muted-foreground">
              <p>No productivity trend data available for the selected period.</p>
              <p className="text-sm mt-1">
                Try selecting a different time range or department filter.
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
