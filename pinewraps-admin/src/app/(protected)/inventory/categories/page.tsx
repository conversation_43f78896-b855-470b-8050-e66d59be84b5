'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import toast from 'react-hot-toast';
import { Heading } from '@/components/ui/heading';
import { Separator } from '@/components/ui/separator';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { CategoryForm } from '@/components/inventory/CategoryForm';
import { DataTable } from '@/components/ui/data-table';
import { columns, type InventoryCategory } from './columns';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import api from '@/lib/api';

interface CategoryFormData {
  name: string;
  description: string | null;
  isActive: boolean;
  id?: string;
}

export default function InventoryCategoriesPage() {
  const [categories, setCategories] = useState<InventoryCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<InventoryCategory | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [statusFilter, setStatusFilter] = useState('all');

  const fetchCategories = async () => {
    try {
      const response = await api.get('/api/inventory-categories');
      if (response.data?.success) {
        setCategories(response.data.data || []);
      } else {
        setCategories([]);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
      toast.error('Failed to fetch categories');
      setCategories([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  const handleEdit = (category: InventoryCategory) => {
    setSelectedCategory(category);
    setIsFormOpen(true);
  };

  const handleDelete = async (category: InventoryCategory) => {
    try {
      await api.delete(`/api/inventory-categories/${category.id}`);
      toast.success('Category deleted successfully');
      fetchCategories();
    } catch (error: any) {
      console.error('Error deleting category:', error);
      const errorMessage = error.response?.data?.error || 'Failed to delete category';
      toast.error(errorMessage);
    }
  };

  const handleSubmit = async (data: CategoryFormData) => {
    try {
      if (selectedCategory) {
        await api.patch(`/api/inventory-categories/${selectedCategory.id}`, data);
        toast.success('Category updated successfully');
      } else {
        await api.post('/api/inventory-categories', data);
        toast.success('Category created successfully');
      }
      fetchCategories();
      setIsFormOpen(false);
      setSelectedCategory(null);
    } catch (error: any) {
      console.error('Error saving category:', error);
      const errorMessage = error.response?.data?.error || 'Failed to save category';
      toast.error(errorMessage);
    }
  };

  // Create columns with handlers
  const tableColumns = columns({
    onEdit: handleEdit,
    onDelete: handleDelete,
  });

  // Filter categories based on selected filters
  const filteredCategories = categories.filter((category) => {
    const statusMatch = statusFilter === 'all' ||
      (statusFilter === 'active' && category.isActive) ||
      (statusFilter === 'inactive' && !category.isActive);

    return statusMatch;
  });

  return (
    <div className="flex-1 space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <Heading 
            title="Inventory Categories" 
            description="Manage inventory categories" 
          />
        </div>
        <Button 
          onClick={() => {
            setSelectedCategory(null);
            setIsFormOpen(true);
          }}
        >
          <Plus className="mr-2 h-4 w-4" />
          Add Category
        </Button>
      </div>
      <Separator />

      <DataTable
        columns={tableColumns}
        data={filteredCategories}
        loading={loading}
        searchKey="name"
        searchPlaceholder="Search categories..."
        filterComponent={
          <div className="flex items-center space-x-2 ml-2">
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>
        }
      />

      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {selectedCategory ? 'Edit Category' : 'Add Category'}
            </DialogTitle>
          </DialogHeader>
          <CategoryForm
            initialData={selectedCategory}
            onSubmit={handleSubmit}
            onCancel={() => {
              setIsFormOpen(false);
              setSelectedCategory(null);
            }}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}
