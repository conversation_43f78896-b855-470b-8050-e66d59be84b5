'use client';

import { useEffect, useState } from 'react';
import { Plus } from 'lucide-react';
import { format } from 'date-fns';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import { Heading } from '@/components/ui/heading';
import { Separator } from '@/components/ui/separator';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';

import { columns } from './columns';
import api from '@/lib/api';
import { useAuth } from '@/providers/auth-provider';

type AdjustmentType = 'damage' | 'loss' | 'correction' | 'expiry' | 'all';

export default function InventoryAdjustmentsPage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [dateRange, setDateRange] = useState<{
    from: Date;
    to?: Date;
  } | null>(null);
  const [selectedType, setSelectedType] = useState<AdjustmentType>('all');

  // Check authentication
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/login');
    }
  }, [user, authLoading, router]);

  // Check if user has inventory access
  useEffect(() => {
    if (user && !user.adminAccess?.includes('inventory') && user.role !== 'ADMIN' && user.role !== 'SUPER_ADMIN') {
      toast.error('You do not have access to this page');
      router.push('/dashboard');
      return;
    }
  }, [user, router]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (searchQuery) params.append('search', searchQuery);
      if (dateRange?.from) params.append('startDate', dateRange.from.toISOString());
      if (dateRange?.to) params.append('endDate', dateRange.to.toISOString());
      if (selectedType && selectedType !== 'all') params.append('type', selectedType);

      const response = await api.get('/api/inventory-adjustments?' + params.toString());
      setData(response.data.data.items);
    } catch (error) {
      console.error('Error fetching adjustments:', error);
      toast.error('Failed to load adjustments');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      fetchData();
    }
  }, [searchQuery, dateRange, selectedType, user]);

  // Show loading state while checking auth
  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  // Don't render anything if not authenticated
  if (!user) {
    return null;
  }

  return (
    <div className="flex-1 space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <Heading 
            title="Inventory Adjustments" 
            description="Manage inventory adjustments and stock corrections" 
          />
        </div>
        <Button onClick={() => router.push('/inventory/adjustments/new')}>
          <Plus className="mr-2 h-4 w-4" /> New Adjustment
        </Button>
      </div>
      <Separator />

      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex flex-wrap gap-4 mb-6">
            <div className="flex-1 min-w-[200px] max-w-xs">
              <Input
                placeholder="Search adjustments..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full"
              />
            </div>

            <Select
              value={selectedType}
              onValueChange={(value) => setSelectedType(value as AdjustmentType)}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="damage">Damage</SelectItem>
                <SelectItem value="loss">Loss</SelectItem>
                <SelectItem value="correction">Correction</SelectItem>
                <SelectItem value="expiry">Expiry</SelectItem>
              </SelectContent>
            </Select>

            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={dateRange?.from ? 'w-[300px]' : 'w-[300px] text-muted-foreground'}
                >
                  {dateRange?.from ? (
                    dateRange.to ? (
                      <>
                        {format(dateRange.from, 'LLL dd, y')} -{' '}
                        {format(dateRange.to, 'LLL dd, y')}
                      </>
                    ) : (
                      format(dateRange.from, 'LLL dd, y')
                    )
                  ) : (
                    <span>Pick a date range</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="end">
                <Calendar
                  initialFocus
                  mode="range"
                  defaultMonth={dateRange?.from}
                  selected={dateRange}
                  onSelect={setDateRange}
                  numberOfMonths={2}
                />
              </PopoverContent>
            </Popover>
          </div>
        </CardContent>
      </Card>

      <DataTable
        columns={columns}
        data={data}
        loading={loading}
      />
    </div>
  );
}
