'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { toast } from '@/lib/toast';
import { Heading } from '@/components/ui/heading';
import { Separator } from '@/components/ui/separator';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { CategoryForm } from '@/components/categories/CategoryForm';
import { categoriesApi } from '@/lib/api';
import { DataTable } from '@/components/ui/data-table';
import { columns, type Category } from './columns';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

export default function CategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState('all');
  const [visibilityFilter, setVisibilityFilter] = useState('all');

  const fetchCategories = async () => {
    try {
      setLoading(true);
      const response = await categoriesApi.getAll();
      setCategories(response.data.data || []);
    } catch (error) {
      console.error('Error fetching categories:', error);
      toast.error('Failed to fetch categories');
      setCategories([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  const handleEdit = (category: Category) => {
    setSelectedCategory(category);
    setIsFormOpen(true);
  };

  const handleDelete = async (category: Category) => {
    try {
      setActionLoading(category.id);
      await categoriesApi.delete(category.id);
      toast.success('Category deleted successfully');
      fetchCategories();
    } catch (error) {
      console.error('Error deleting category:', error);
      toast.error('Failed to delete category');
    } finally {
      setActionLoading(null);
    }
  };

  const handleToggleActive = async (category: Category) => {
    try {
      setActionLoading(category.id);
      await categoriesApi.update(category.id, {
        ...category,
        isActive: !category.isActive
      });
      toast.success(`Category ${!category.isActive ? 'activated' : 'deactivated'} successfully`);
      fetchCategories();
    } catch (error) {
      console.error('Error updating category:', error);
      toast.error('Failed to update category status');
    } finally {
      setActionLoading(null);
    }
  };

  const onFormSuccess = () => {
    setIsFormOpen(false);
    setSelectedCategory(null);
    fetchCategories();
  };

  // Create columns with handlers
  const tableColumns = columns({
    onEdit: handleEdit,
    onDelete: handleDelete,
    onToggleActive: handleToggleActive,
  });

  // Filter categories based on selected filters
  const filteredCategories = categories.filter((category) => {
    const statusMatch = statusFilter === 'all' ||
      (statusFilter === 'active' && category.isActive) ||
      (statusFilter === 'inactive' && !category.isActive);

    const visibilityMatch = visibilityFilter === 'all' || category.visibility === visibilityFilter;

    return statusMatch && visibilityMatch;
  });

  return (
    <div className="flex-1 space-y-4">
      <div className="flex items-center justify-between">
        <Heading
          title="Categories"
          description="Manage product categories"
        />
        <Button onClick={() => {
          setSelectedCategory(null);
          setIsFormOpen(true);
        }}>
          <Plus className="mr-2 h-4 w-4" />
          Add New
        </Button>
      </div>
      <Separator />

      <DataTable
        columns={tableColumns}
        data={filteredCategories}
        loading={loading}
        searchKey="name"
        searchPlaceholder="Search categories..."
        filterComponent={
          <div className="flex items-center space-x-2 ml-2">
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
            <Select value={visibilityFilter} onValueChange={setVisibilityFilter}>
              <SelectTrigger className="w-[160px]">
                <SelectValue placeholder="Visibility" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Platforms</SelectItem>
                <SelectItem value="ALL">All Platforms</SelectItem>
                <SelectItem value="POS_ONLY">POS Only</SelectItem>
                <SelectItem value="WEB_ONLY">Website Only</SelectItem>
                <SelectItem value="APP_ONLY">Mobile App Only</SelectItem>
              </SelectContent>
            </Select>
          </div>
        }
      />

      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {selectedCategory ? 'Edit Category' : 'Create Category'}
            </DialogTitle>
          </DialogHeader>
          <CategoryForm
            initialData={selectedCategory}
            categories={categories.filter(cat => cat.id !== selectedCategory?.id)}
            onSuccess={onFormSuccess}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}
