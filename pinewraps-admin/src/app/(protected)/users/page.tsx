'use client';

import { useEffect, useState } from 'react';
import { Plus } from 'lucide-react';
import { useRouter } from 'next/navigation';

import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import { Heading } from '@/components/ui/heading';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { columns } from './columns';
import api from '@/lib/api';
import { getFirebaseToken } from '@/lib/firebase';
import { toast } from '@/lib/toast';

export default function UsersPage() {
  const router = useRouter();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0
  });

  const fetchUsers = async (page = 1, retryCount = 0, pageSize?: number) => {
    try {
      setLoading(true);
      setError(null);
      const token = await getFirebaseToken();
      
      // Get users with pagination
      const response = await api.get('/api/users', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        params: {
          page,
          pageSize: pageSize || pagination.pageSize
        }
      });
      
      // Set users data
      console.log('Users data:', response.data);
      setUsers(response.data.data || []);
      
      // Update pagination information
      if (response.data.pagination) {
        setPagination({
          page: response.data.pagination.page || 1,
          pageSize: response.data.pagination.pageSize || 10,
          total: response.data.pagination.total || response.data.data.length,
          totalPages: response.data.pagination.totalPages || 1
        });
      } else {
        // If no pagination info, calculate based on data length
        const total = response.data.data?.length || 0;
        setPagination({
          page,
          pageSize: 10,
          total: total,
          totalPages: Math.max(1, Math.ceil(total / 10))
        });
      }
      
      setLoading(false);
    } catch (error: any) {
      console.error('Error fetching users:', error);
      const isVisibilityError = error?.message?.includes('visibility-check-was-unavailable');
      
      if (isVisibilityError && retryCount < 3) {
        // Wait for a bit before retrying
        await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));
        return fetchUsers(page, retryCount + 1);
      }

      const errorMessage = error.response?.data?.message ||
        'Failed to fetch users. Please try refreshing the page.';
      setError(errorMessage);
      toast.crud.fetchError('User', errorMessage);
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers(pagination.page);
  }, []);
  
  // Handle page change
  const handlePageChange = (newPage: number) => {
    fetchUsers(newPage);
  };

  // Handle page size change
  const handlePageSizeChange = (newPageSize: number) => {
    console.log('Page size changed to:', newPageSize);
    
    // Update pagination state
    setPagination(prev => ({
      ...prev,
      pageSize: newPageSize,
      page: 1 // Reset to first page when changing page size
    }));
    
    // Pass the new page size to fetchUsers
    // We need to modify fetchUsers to use the provided pageSize
    fetchUsers(1);
  };

  // Filter users based on selected filters
  const filteredUsers = users.filter((user: any) => {
    const roleMatch = roleFilter === 'all' || user.role === roleFilter;
    const statusMatch = statusFilter === 'all' ||
      (statusFilter === 'active' && user.isActive) ||
      (statusFilter === 'inactive' && !user.isActive);

    return roleMatch && statusMatch;
  });

  return (
    <div className="flex-1 space-y-4">
      <div className="flex items-center justify-between">
        <Heading title="Users" description="Manage user accounts" />
        <Button onClick={() => router.push('/users/new')}>
          <Plus className="mr-2 h-4 w-4" />
          Add New
        </Button>
      </div>
      <Separator />
      {error ? (
        <div className="bg-destructive/10 text-destructive p-4 rounded-lg mb-4">
          <p>{error}</p>
          <Button
            variant="outline"
            className="mt-2"
            onClick={() => window.location.reload()}
          >
            Retry
          </Button>
        </div>
      ) : (
        <DataTable
          columns={columns}
          data={filteredUsers}
          searchKey="email"
          searchPlaceholder="Search by email or name..."
          loading={loading}
          filterComponent={
            <div className="flex items-center space-x-2 ml-2">
              <Select value={roleFilter} onValueChange={setRoleFilter}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="Role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Roles</SelectItem>
                  <SelectItem value="SUPER_ADMIN">Super Admin</SelectItem>
                  <SelectItem value="ADMIN">Admin</SelectItem>
                  <SelectItem value="POS_USER">POS User</SelectItem>
                  <SelectItem value="DRIVER">Driver</SelectItem>
                </SelectContent>
              </Select>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          }
          pagination={{
            page: pagination.page,
            pageSize: pagination.pageSize,
            total: pagination.total,
            onPageChange: handlePageChange
          }}
          meta={{
            onPageSizeChange: handlePageSizeChange
          }}
        />
      )}
    </div>
  );
}
