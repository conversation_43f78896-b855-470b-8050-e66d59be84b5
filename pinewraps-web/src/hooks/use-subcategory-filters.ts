'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams, usePathname } from 'next/navigation';
import { Category } from '@/services/api';

interface UseSubcategoryFiltersProps {
  categoryName?: string; // For specific category pages like 'Cakes', 'Flowers', 'Sets'
  selectedCategory?: string; // For shop page with dynamic category selection
}

interface UseSubcategoryFiltersReturn {
  subcategories: Category[];
  selectedSubcategory: string;
  isLoading: boolean;
  error: string | null;
  handleSubcategoryChange: (subcategoryId: string) => void;
  hasSubcategories: boolean;
}

// Cache for subcategories to avoid repeated API calls
const subcategoryCache = new Map<string, { data: Category[]; timestamp: number; version: string }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Cache version for deployment cache busting
const getCacheVersion = () => {
  // Use build time or deployment ID to bust cache on new deployments
  return process.env.NEXT_PUBLIC_BUILD_ID || process.env.NEXT_PUBLIC_VERCEL_GIT_COMMIT_SHA || Date.now().toString();
};

export function useSubcategoryFilters({
  categoryName,
  selectedCategory
}: UseSubcategoryFiltersProps = {}): UseSubcategoryFiltersReturn {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  
  const [subcategories, setSubcategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Get selected subcategory from URL params
  const selectedSubcategory = searchParams.get('subcategory') || '';

  // Create query string helper
  const createQueryString = useCallback(
    (name: string, value: string) => {
      const params = new URLSearchParams(searchParams.toString());
      if (value) {
        params.set(name, value);
      } else {
        params.delete(name);
      }
      // Reset page when changing subcategory
      params.delete('page');
      return params.toString();
    },
    [searchParams]
  );

  // Handle subcategory change
  const handleSubcategoryChange = useCallback((subcategoryId: string) => {
    const queryString = createQueryString('subcategory', subcategoryId);
    const newUrl = `${pathname}${queryString ? `?${queryString}` : ''}`;
    router.push(newUrl, { scroll: false });
  }, [createQueryString, pathname, router]);

  // Fetch subcategories
  const fetchSubcategories = useCallback(async (categoryId: string, categoryNameForFallback?: string) => {
    // Check cache first
    const cacheKey = categoryId || categoryNameForFallback || '';
    const cached = subcategoryCache.get(cacheKey);
    const now = Date.now();
    const currentVersion = getCacheVersion();

    // Check if cache is valid (not expired and same version)
    if (cached &&
        now - cached.timestamp < CACHE_DURATION &&
        cached.version === currentVersion) {
      setSubcategories(cached.data);
      return;
    }

    // Clear cache if version mismatch (deployment cache bust)
    if (cached && cached.version !== currentVersion) {
      subcategoryCache.clear();
    }

    setIsLoading(true);
    setError(null);

    try {
      // First try hierarchical endpoint
      const hierarchicalResponse = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/categories/public/hierarchical?platform=WEB`,
        {
          headers: {
            'X-Platform': 'WEB'
          }
        }
      );

      if (hierarchicalResponse.ok) {
        const hierarchicalData = await hierarchicalResponse.json();

        if (hierarchicalData.success && Array.isArray(hierarchicalData.data)) {
          let targetCategory;
          
          if (categoryId) {
            // Find by ID (for shop page)
            targetCategory = hierarchicalData.data.find((cat: any) => cat.id === categoryId);
          } else if (categoryNameForFallback) {
            // Find by name (for category pages)
            targetCategory = hierarchicalData.data.find((cat: any) =>
              cat.name.toLowerCase() === categoryNameForFallback.toLowerCase() ||
              cat.name === categoryNameForFallback
            );
          }

          if (targetCategory && targetCategory.children && targetCategory.children.length > 0) {
            const subcats = targetCategory.children;
            setSubcategories(subcats);
            // Cache the result with version for cache busting
            subcategoryCache.set(cacheKey, {
              data: subcats,
              timestamp: now,
              version: getCacheVersion()
            });
            return;
          }
        }
      }

      // Fallback: Get all categories and filter for subcategories
      const allCategoriesResponse = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/categories/public`,
        {
          headers: {
            'X-Platform': 'WEB'
          }
        }
      );

      if (allCategoriesResponse.ok) {
        const allCategoriesData = await allCategoriesResponse.json();

        if (allCategoriesData.success && Array.isArray(allCategoriesData.data)) {
          let parentCategory;
          
          if (categoryId) {
            parentCategory = allCategoriesData.data.find((cat: any) => cat.id === categoryId);
          } else if (categoryNameForFallback) {
            parentCategory = allCategoriesData.data.find((cat: any) =>
              cat.name.toLowerCase() === categoryNameForFallback.toLowerCase() ||
              cat.name === categoryNameForFallback
            );
          }

          if (parentCategory) {
            // Find all subcategories that have this category as parent
            const subcats = allCategoriesData.data.filter((cat: any) =>
              cat.parentId === parentCategory.id
            );
            setSubcategories(subcats);
            // Cache the result with version for cache busting
            subcategoryCache.set(cacheKey, {
              data: subcats,
              timestamp: now,
              version: getCacheVersion()
            });
          } else {
            setSubcategories([]);
          }
        }
      } else {
        throw new Error('Failed to fetch categories');
      }
    } catch (err) {
      console.error('Error fetching subcategories:', err);
      setError('Failed to load subcategories');
      setSubcategories([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Effect to fetch subcategories when category changes
  useEffect(() => {
    if (selectedCategory) {
      // For shop page - use selected category ID
      fetchSubcategories(selectedCategory);
    } else if (categoryName) {
      // For category pages - use category name
      fetchSubcategories('', categoryName);
    } else {
      // No category selected, clear subcategories
      setSubcategories([]);
      setIsLoading(false);
      setError(null);
    }
  }, [selectedCategory, categoryName, fetchSubcategories]);

  return {
    subcategories,
    selectedSubcategory,
    isLoading,
    error,
    handleSubcategoryChange,
    hasSubcategories: subcategories.length > 0
  };
}
