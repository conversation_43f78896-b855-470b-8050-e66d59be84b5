import express from 'express';
import { requireAdminPanelAccess, requireAuth } from '../middleware/auth';
import { operatingExpensesController } from '../controllers/operating-expenses.controller';

const router = express.Router();

// First authenticate the user, then check for admin panel access
router.use(requireAuth);
router.use(requireAdminPanelAccess);

// Expense Categories Routes
router.post('/categories', operatingExpensesController.createExpenseCategory.bind(operatingExpensesController));
router.get('/categories', operatingExpensesController.getExpenseCategories.bind(operatingExpensesController));
router.get('/categories/:id', operatingExpensesController.getExpenseCategoryById.bind(operatingExpensesController));
router.put('/categories/:id', operatingExpensesController.updateExpenseCategory.bind(operatingExpensesController));
router.delete('/categories/:id', operatingExpensesController.deleteExpenseCategory.bind(operatingExpensesController));

// Operating Expenses Routes
router.post('/', operatingExpensesController.createOperatingExpense.bind(operatingExpensesController));
router.get('/', operatingExpensesController.getOperatingExpenses.bind(operatingExpensesController));
router.get('/stats', operatingExpensesController.getExpenseStats.bind(operatingExpensesController));
router.get('/:id', operatingExpensesController.getOperatingExpenseById.bind(operatingExpensesController));
router.put('/:id', operatingExpensesController.updateOperatingExpense.bind(operatingExpensesController));
router.delete('/:id', operatingExpensesController.deleteOperatingExpense.bind(operatingExpensesController));
router.patch('/:id/mark-paid', operatingExpensesController.markExpenseAsPaid.bind(operatingExpensesController));

export default router;
