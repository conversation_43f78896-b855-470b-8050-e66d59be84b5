'use client';

import React from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Users,
  ChefHat,
  Palette,
  CheckCircle,
  Calculator,
  BarChart3,
  ArrowLeft
} from 'lucide-react';

interface StaffReportsLayoutProps {
  children: React.ReactNode;
}

const staffDepartments = [
  {
    id: 'overview',
    name: 'Overview',
    path: '/staff-reports',
    icon: BarChart3,
    description: 'Overall staff performance dashboard',
    color: 'bg-blue-500',
  },
  {
    id: 'kitchen',
    name: 'Kitchen Staff',
    path: '/staff-reports/kitchen',
    icon: ChefHat,
    description: 'Kitchen operations and food preparation',
    color: 'bg-red-500',
  },
  {
    id: 'design',
    name: 'Design Staff',
    path: '/staff-reports/design',
    icon: Palette,
    description: 'Custom design and creative work',
    color: 'bg-blue-500',
  },
  {
    id: 'final-check',
    name: 'Final Check Staff',
    path: '/staff-reports/final-check',
    icon: CheckCircle,
    description: 'Quality control and final inspection',
    color: 'bg-green-500',
  },
  {
    id: 'cashier',
    name: 'Cashier Staff',
    path: '/staff-reports/cashier',
    icon: Calculator,
    description: 'POS operations and customer service',
    color: 'bg-amber-500',
  },
];

export default function StaffReportsLayout({ children }: StaffReportsLayoutProps) {
  const pathname = usePathname();
  const router = useRouter();

  // Check if we're on a specific staff member page
  const isStaffDetailPage = pathname.includes('/staff-reports/') && pathname.split('/').length > 3;
  const isSubDepartmentPage = pathname !== '/staff-reports' && !isStaffDetailPage;

  const getCurrentDepartment = () => {
    if (pathname === '/staff-reports') return 'overview';
    if (pathname.includes('/kitchen')) return 'kitchen';
    if (pathname.includes('/design')) return 'design';
    if (pathname.includes('/final-check')) return 'final-check';
    if (pathname.includes('/cashier')) return 'cashier';
    return 'overview';
  };

  const currentDepartment = getCurrentDepartment();

  // If we're on a staff detail page, show a simplified header
  if (isStaffDetailPage) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Staff Reports
          </Button>
        </div>
        {children}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Main Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Staff Reports</h1>
          <p className="text-muted-foreground">
            Monitor staff performance across all departments
          </p>
        </div>
      </div>

      {/* Department Navigation */}
      <Card className="border-0 shadow-none">
        <CardContent className="p-0">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
            {staffDepartments.map((dept) => {
              const Icon = dept.icon;
              const isActive = currentDepartment === dept.id;
              
              return (
                <div
                  key={dept.id}
                  className={`relative p-4 rounded-lg border-2 cursor-pointer transition-all hover:shadow-md ${
                    isActive 
                      ? 'border-primary bg-primary/5 shadow-md' 
                      : 'border-border hover:border-primary/50'
                  }`}
                  onClick={() => router.push(dept.path)}
                >
                  <div className="flex items-center space-x-3 mb-2">
                    <div className={`p-2 rounded-lg ${dept.color} text-white`}>
                      <Icon className="h-5 w-5" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-sm">{dept.name}</h3>
                      {isActive && (
                        <Badge variant="default" className="mt-1">
                          Active
                        </Badge>
                      )}
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {dept.description}
                  </p>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Page Content */}
      <div className="min-h-[400px]">
        {children}
      </div>
    </div>
  );
}
