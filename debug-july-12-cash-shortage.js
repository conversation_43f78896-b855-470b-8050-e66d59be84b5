const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
});

async function debugJuly12CashShortage() {
  try {
    console.log('🔍 Debugging July 12th Cash Shortage...\n');

    // Find drawer sessions for July 12th
    const july12Sessions = await prisma.drawerSession.findMany({
      where: {
        OR: [
          {
            openedAt: {
              gte: new Date('2024-07-12T00:00:00.000Z'),
              lte: new Date('2024-07-12T23:59:59.999Z')
            }
          },
          {
            closedAt: {
              gte: new Date('2024-07-12T00:00:00.000Z'),
              lte: new Date('2024-07-12T23:59:59.999Z')
            }
          }
        ]
      },
      include: {
        operations: {
          orderBy: {
            createdAt: 'asc'
          }
        },
        user: {
          select: {
            firstName: true,
            lastName: true,
            email: true
          }
        }
      },
      orderBy: {
        openedAt: 'desc'
      }
    });

    console.log(`📊 Found ${july12Sessions.length} drawer sessions for July 12th\n`);

    for (const session of july12Sessions) {
      console.log(`\n🏪 SESSION: ${session.id}`);
      console.log(`👤 User: ${session.user.firstName} ${session.user.lastName} (${session.user.email})`);
      console.log(`📅 Opened: ${session.openedAt}`);
      console.log(`📅 Closed: ${session.closedAt || 'Still Open'}`);
      console.log(`💰 Opening Amount: AED ${parseFloat(session.openingAmount).toFixed(2)}`);
      console.log(`💰 Closing Amount: AED ${session.closingAmount ? parseFloat(session.closingAmount).toFixed(2) : 'N/A'}`);
      console.log(`📊 Status: ${session.status}`);

      // Analyze operations
      console.log(`\n📋 OPERATIONS (${session.operations.length} total):`);
      
      let totalPayIn = 0;
      let totalPayOut = 0;
      let totalSales = 0;
      
      session.operations.forEach((op, index) => {
        const amount = parseFloat(op.amount);
        console.log(`  ${index + 1}. ${op.type}: AED ${amount.toFixed(2)} - ${op.notes || 'No notes'} (${op.createdAt})`);
        
        switch (op.type) {
          case 'ADD_CASH':
            totalPayIn += amount;
            break;
          case 'TAKE_CASH':
            totalPayOut += Math.abs(amount);
            break;
          case 'SALE':
            totalSales += amount;
            break;
        }
      });

      console.log(`\n📊 OPERATION TOTALS:`);
      console.log(`  💵 Total Pay In (ADD_CASH): AED ${totalPayIn.toFixed(2)}`);
      console.log(`  💸 Total Pay Out (TAKE_CASH): AED ${totalPayOut.toFixed(2)}`);
      console.log(`  🛒 Total Sales (SALE): AED ${totalSales.toFixed(2)}`);

      // Get payments made during this session
      if (session.closedAt) {
        const sessionStartTime = session.openedAt;
        const sessionEndTime = session.closedAt;

        const payments = await prisma.pOSPayment.findMany({
          where: {
            createdAt: {
              gte: sessionStartTime,
              lte: sessionEndTime
            },
            status: {
              in: ['FULLY_PAID', 'PARTIALLY_PAID']
            },
            order: {
              status: {
                not: 'CANCELLED'
              }
            }
          },
          include: {
            order: {
              select: {
                id: true,
                orderNumber: true,
                status: true,
                total: true,
                createdAt: true
              }
            }
          }
        });

        console.log(`\n💳 PAYMENTS DURING SESSION (${payments.length} total):`);
        
        const paymentTotals = {
          CASH: 0,
          CARD: 0,
          BANK_TRANSFER: 0,
          PBL: 0,
          TALABAT: 0,
          COD: 0
        };

        payments.forEach((payment, index) => {
          const amount = parseFloat(payment.amount);
          console.log(`  ${index + 1}. Order #${payment.order.orderNumber}: ${payment.method} - AED ${amount.toFixed(2)} (${payment.createdAt})`);
          
          if (paymentTotals.hasOwnProperty(payment.method)) {
            paymentTotals[payment.method] += amount;
          }
        });

        console.log(`\n💰 PAYMENT TOTALS:`);
        Object.entries(paymentTotals).forEach(([method, total]) => {
          if (total > 0) {
            console.log(`  ${method}: AED ${total.toFixed(2)}`);
          }
        });

        // Calculate expected cash using the same formula as the system
        const openingAmount = parseFloat(session.openingAmount);
        const cashFromPayments = paymentTotals.CASH;
        const expectedBalance = openingAmount + cashFromPayments + totalPayIn - totalPayOut;
        
        console.log(`\n🧮 CASH SHORTAGE CALCULATION:`);
        console.log(`  Opening Amount: AED ${openingAmount.toFixed(2)}`);
        console.log(`  + Cash from Payments: AED ${cashFromPayments.toFixed(2)}`);
        console.log(`  + Manual Pay In: AED ${totalPayIn.toFixed(2)}`);
        console.log(`  - Manual Pay Out: AED ${totalPayOut.toFixed(2)}`);
        console.log(`  = Expected Balance: AED ${expectedBalance.toFixed(2)}`);
        
        if (session.closingAmount) {
          const actualClosing = parseFloat(session.closingAmount);
          const discrepancy = actualClosing - expectedBalance;
          
          console.log(`  Actual Closing Amount: AED ${actualClosing.toFixed(2)}`);
          console.log(`  Discrepancy: AED ${discrepancy.toFixed(2)}`);
          
          if (Math.abs(discrepancy) > 0.01) {
            console.log(`  🚨 ${discrepancy < 0 ? 'CASH SHORTAGE' : 'CASH EXCESS'}: AED ${Math.abs(discrepancy).toFixed(2)}`);
          } else {
            console.log(`  ✅ No significant shortage/excess`);
          }
        }

        // Compare with SALE operations
        const cashDiscrepancy = Math.abs(cashFromPayments - totalSales);
        if (cashDiscrepancy > 0.01) {
          console.log(`\n⚠️  CASH CALCULATION MISMATCH:`);
          console.log(`  Payment totals cash: AED ${cashFromPayments.toFixed(2)}`);
          console.log(`  SALE operations cash: AED ${totalSales.toFixed(2)}`);
          console.log(`  Discrepancy: AED ${cashDiscrepancy.toFixed(2)}`);
        }
      }

      console.log('\n' + '='.repeat(80));
    }

  } catch (error) {
    console.error('❌ Error debugging cash shortage:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the debug script
debugJuly12CashShortage();
