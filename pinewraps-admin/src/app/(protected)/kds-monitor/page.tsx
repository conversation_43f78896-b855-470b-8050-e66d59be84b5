'use client';

import { useEffect, useState } from 'react';
import { Monitor, Users, Clock, ChefHat, Palette, CheckCircle2, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Heading } from '@/components/ui/heading';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/providers/auth-provider';
import { auth } from '@/lib/firebase';
import Cookies from 'js-cookie';
import toast from 'react-hot-toast';

interface StaffMember {
  id: string;
  name: string;
  email: string;
}

interface KDSOrder {
  id: string;
  orderNumber: string;
  status: string;
  customerName: string;
  totalAmount: number;
  createdAt: string;
  assignedStaff: StaffMember | null;
  kitchenStartTime?: string;
  kitchenEndTime?: string;
  designStartTime?: string;
  designEndTime?: string;
  finalCheckStartTime?: string;
  finalCheckEndTime?: string;
}

interface KDSStatus {
  kitchen: {
    totalOrders: number;
    orders: KDSOrder[];
  };
  design: {
    totalOrders: number;
    orders: KDSOrder[];
  };
  finalCheck: {
    totalOrders: number;
    orders: KDSOrder[];
  };
}

export default function KDSMonitorPage() {
  const [kdsStatus, setKdsStatus] = useState<KDSStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const { user } = useAuth();

  const fetchKDSStatus = async () => {
    try {
      setRefreshing(true);

      // Get token from cookie first
      let token = Cookies.get('admin-token');

      // If no token in cookie, try to get from Firebase auth
      if (!token) {
        const currentUser = auth.currentUser;
        if (currentUser) {
          token = await currentUser.getIdToken();
        } else {
          toast.error('Authentication required');
          return;
        }
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/pos/orders/kds-status`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch KDS status');
      }

      const data = await response.json();

      if (data.success) {
        setKdsStatus(data.data);
      } else {
        throw new Error(data.message || 'Failed to fetch KDS status');
      }
    } catch (error) {
      console.error('Error fetching KDS status:', error);
      toast.error('Failed to fetch KDS status');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchKDSStatus();

    // Set up auto-refresh every 30 seconds
    const interval = setInterval(fetchKDSStatus, 30000);

    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'KITCHEN_QUEUE':
      case 'DESIGN_QUEUE':
      case 'FINAL_CHECK_QUEUE':
        return 'bg-blue-100 text-blue-800';
      case 'KITCHEN_PROCESSING':
      case 'DESIGN_PROCESSING':
      case 'FINAL_CHECK_PROCESSING':
        return 'bg-yellow-100 text-yellow-800';
      case 'KITCHEN_READY':
      case 'DESIGN_READY':
      case 'FINAL_CHECK_COMPLETE':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatStatus = (status: string) => {
    return status.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getElapsedTime = (startTime: string) => {
    const start = new Date(startTime);
    const now = new Date();
    const diffMs = now.getTime() - start.getTime();
    const diffMins = Math.floor(diffMs / 60000);

    if (diffMins < 60) {
      return `${diffMins}m`;
    } else {
      const hours = Math.floor(diffMins / 60);
      const mins = diffMins % 60;
      return `${hours}h ${mins}m`;
    }
  };

  if (loading) {
    return (
      <div className="flex-1 space-y-4">
        <div className="flex items-center justify-between">
          <Heading 
            title="KDS Monitor" 
            description="Monitor Kitchen Display System status and staff assignments" 
          />
        </div>
        <Separator />
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-3 text-sm text-muted-foreground">Loading KDS status...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 space-y-4">
      <div className="flex flex-col space-y-2 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">KDS Monitor</h2>
          <p className="text-muted-foreground">
            Monitor Kitchen Display System status and staff assignments
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            onClick={fetchKDSStatus}
            disabled={refreshing}
            variant="outline"
            size="sm"
            className="h-9"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>
      <Separator />

      {!kdsStatus ? (
        <div className="flex flex-col items-center justify-center py-12 space-y-4">
          <Monitor className="h-10 w-10 text-muted-foreground" />
          <p className="text-muted-foreground">No KDS data available</p>
          <Button 
            variant="outline" 
            size="sm" 
            className="mt-2"
            onClick={fetchKDSStatus}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Retry
          </Button>
        </div>
      ) : (
        <div className="grid gap-6 md:grid-cols-3">
          {/* Kitchen Section */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg">
                <ChefHat className="h-5 w-5 text-orange-600" />
                Kitchen
                <Badge variant="secondary" className="ml-auto">
                  {kdsStatus.kitchen.totalOrders}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {kdsStatus.kitchen.orders.length === 0 ? (
                <p className="text-sm text-gray-500 text-center py-4">No active kitchen orders</p>
              ) : (
                kdsStatus.kitchen.orders.map((order) => (
                  <div key={order.id} className="border rounded-lg p-3 space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-sm">#{order.orderNumber}</span>
                      <Badge className={getStatusColor(order.status)} variant="secondary">
                        {formatStatus(order.status)}
                      </Badge>
                    </div>
                    <div className="text-sm text-gray-600">
                      <p>{order.customerName}</p>
                      <p>AED {order.totalAmount.toFixed(2)}</p>
                    </div>
                    {order.assignedStaff ? (
                      <div className="flex items-center gap-2 text-sm">
                        <Users className="h-3 w-3" />
                        <span className="text-green-600">{order.assignedStaff.name}</span>
                      </div>
                    ) : (
                      <div className="flex items-center gap-2 text-sm text-gray-500">
                        <Users className="h-3 w-3" />
                        <span>Unassigned</span>
                      </div>
                    )}
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <Clock className="h-3 w-3" />
                      <span>Created: {formatTime(order.createdAt)}</span>
                      {order.kitchenStartTime && (
                        <span className="ml-2">
                          Processing: {getElapsedTime(order.kitchenStartTime)}
                        </span>
                      )}
                    </div>
                  </div>
                ))
              )}
            </CardContent>
          </Card>

          {/* Design Section */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Palette className="h-5 w-5 text-purple-600" />
                Design
                <Badge variant="secondary" className="ml-auto">
                  {kdsStatus.design.totalOrders}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {kdsStatus.design.orders.length === 0 ? (
                <p className="text-sm text-gray-500 text-center py-4">No active design orders</p>
              ) : (
                kdsStatus.design.orders.map((order) => (
                  <div key={order.id} className="border rounded-lg p-3 space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-sm">#{order.orderNumber}</span>
                      <Badge className={getStatusColor(order.status)} variant="secondary">
                        {formatStatus(order.status)}
                      </Badge>
                    </div>
                    <div className="text-sm text-gray-600">
                      <p>{order.customerName}</p>
                      <p>AED {order.totalAmount.toFixed(2)}</p>
                    </div>
                    {order.assignedStaff ? (
                      <div className="flex items-center gap-2 text-sm">
                        <Users className="h-3 w-3" />
                        <span className="text-green-600">{order.assignedStaff.name}</span>
                      </div>
                    ) : (
                      <div className="flex items-center gap-2 text-sm text-gray-500">
                        <Users className="h-3 w-3" />
                        <span>Unassigned</span>
                      </div>
                    )}
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <Clock className="h-3 w-3" />
                      <span>Created: {formatTime(order.createdAt)}</span>
                      {order.designStartTime && (
                        <span className="ml-2">
                          Processing: {getElapsedTime(order.designStartTime)}
                        </span>
                      )}
                    </div>
                  </div>
                ))
              )}
            </CardContent>
          </Card>

          {/* Final Check Section */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg">
                <CheckCircle2 className="h-5 w-5 text-green-600" />
                Final Check
                <Badge variant="secondary" className="ml-auto">
                  {kdsStatus.finalCheck.totalOrders}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {kdsStatus.finalCheck.orders.length === 0 ? (
                <p className="text-sm text-gray-500 text-center py-4">No orders in final check</p>
              ) : (
                kdsStatus.finalCheck.orders.map((order) => (
                  <div key={order.id} className="border rounded-lg p-3 space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-sm">#{order.orderNumber}</span>
                      <Badge className={getStatusColor(order.status)} variant="secondary">
                        {formatStatus(order.status)}
                      </Badge>
                    </div>
                    <div className="text-sm text-gray-600">
                      <p>{order.customerName}</p>
                      <p>AED {order.totalAmount.toFixed(2)}</p>
                    </div>
                    {order.assignedStaff ? (
                      <div className="flex items-center gap-2 text-sm">
                        <Users className="h-3 w-3" />
                        <span className="text-green-600">{order.assignedStaff.name}</span>
                      </div>
                    ) : (
                      <div className="flex items-center gap-2 text-sm text-gray-500">
                        <Users className="h-3 w-3" />
                        <span>Unassigned</span>
                      </div>
                    )}
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <Clock className="h-3 w-3" />
                      <span>Created: {formatTime(order.createdAt)}</span>
                      {order.finalCheckStartTime && (
                        <span className="ml-2">
                          Processing: {getElapsedTime(order.finalCheckStartTime)}
                        </span>
                      )}
                    </div>
                  </div>
                ))
              )}
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
