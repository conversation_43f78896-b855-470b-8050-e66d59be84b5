'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import api from '@/lib/api';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

// Define custom unit metric type
interface UnitMetric {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  isActive: boolean;
  isDefault: boolean;
}

// Import types from the inventory page
interface Category {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
}

interface InventoryItem {
  id: string;
  name: string;
  description?: string;
  sku?: string;
  minQuantity: number;
  currentStock: number;
  unitMetricId: string;
  unitMetric?: UnitMetric;
  conversionRate?: number;
  isActive: boolean;
  categoryId?: string;
  category?: {
    id: string;
    name: string;
  };
}

// Backend uses nanoid() for category IDs
const formSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  sku: z.string().optional(),
  minQuantity: z.coerce.number().min(0, 'Minimum quantity must be at least 0').default(0),
  currentStock: z.coerce.number().min(0, 'Current stock must be at least 0').default(0),
  unitMetricId: z.string().min(1, 'Unit metric is required'),
  conversionRate: z.coerce.number().positive().optional(),
  categoryId: z.string().min(1, 'Category is required'),
  isActive: z.boolean().default(true),
});

type FormData = z.infer<typeof formSchema>;



interface InventoryFormProps {
  initialData?: Partial<InventoryItem>;
  categories: Category[];
  onSubmit: (data: FormData) => void;
  onCancel: () => void;
  loading?: boolean;
}

export function InventoryForm({
  initialData,
  categories,
  onSubmit,
  onCancel,
  loading = false,
}: InventoryFormProps) {
  const [unitMetrics, setUnitMetrics] = useState<UnitMetric[]>([]);
  const [loadingUnitMetrics, setLoadingUnitMetrics] = useState(true);
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      description: '',
      sku: '',
      minQuantity: 0,
      currentStock: 0,
      unitMetric: 'UNIT',
      conversionRate: undefined,
      categoryId: '',
      isActive: true,
      ...initialData,
    },
  });

  // Fetch unit metrics from the API
  useEffect(() => {
    const fetchUnitMetrics = async () => {
      try {
        setLoadingUnitMetrics(true);
        const response = await api.get('/api/unit-metrics', {
          params: { isActive: true }
        });
        setUnitMetrics(response.data.data.items || []);
      } catch (error) {
        console.error('Error fetching unit metrics:', error);
      } finally {
        setLoadingUnitMetrics(false);
      }
    };

    fetchUnitMetrics();
  }, []);

  // Set form values from initialData
  useEffect(() => {
    if (initialData) {
      console.log('Setting form values from initialData:', initialData);
      
      // Only set values that exist in initialData and match our form schema
      const validKeys = Object.keys(formSchema.shape) as Array<keyof FormData>;
      validKeys.forEach((key) => {
        const value = initialData[key];
        if (value !== undefined && value !== null) {
          form.setValue(key, value);
        }
      });

      // Handle categoryId specifically
      if (initialData.category && initialData.category.id && !initialData.categoryId) {
        form.setValue('categoryId', initialData.category.id);
      }
      
      // Handle unitMetric specifically
      if (initialData.unitMetric) {
        // If unitMetric is an object with an id
        if (typeof initialData.unitMetric === 'object' && initialData.unitMetric.id) {
          form.setValue('unitMetricId', initialData.unitMetric.id);
        } 
        // For backward compatibility with old data format where unitMetric is a string
        else if (typeof initialData.unitMetric === 'string') {
          const unitMetricName = initialData.unitMetric;
          const unitMetric = unitMetrics.find(um => um.name === unitMetricName);
          if (unitMetric) {
            form.setValue('unitMetricId', unitMetric.id);
          }
        }
      }
      // If unitMetricId is directly available
      else if (initialData.unitMetricId) {
        form.setValue('unitMetricId', initialData.unitMetricId);
      }
    }
  }, [form, initialData, unitMetrics]);

  // Filter out inactive categories
  const activeCategories = categories.filter((cat): cat is Category => 
    cat.isActive !== false
  );

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input {...field} placeholder="Enter item name" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea {...field} placeholder="Enter item description" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="sku"
          render={({ field }) => (
            <FormItem>
              <FormLabel>SKU</FormLabel>
              <FormControl>
                <Input {...field} placeholder="Enter SKU (optional)" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="minQuantity"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Minimum Quantity</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    type="number"
                    min={0}
                    step="any"
                    placeholder="0"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="currentStock"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Current Stock</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    type="number"
                    min={0}
                    step="any"
                    placeholder="0"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="unitMetricId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Unit Metric</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  value={field.value}
                  disabled={loadingUnitMetrics}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder={loadingUnitMetrics ? "Loading..." : "Select unit metric"} />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {unitMetrics.map((metric) => (
                      <SelectItem key={metric.id} value={metric.id.toLowerCase()}>
                        {metric.displayName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="conversionRate"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Conversion Rate
                  <span className="text-sm text-muted-foreground ml-2">
                    (e.g., 1 BOX = 12 UNITS)
                  </span>
                </FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    type="number"
                    min={0}
                    step="any"
                    placeholder="Enter conversion rate"
                    value={field.value === null ? '' : field.value}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="categoryId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Category</FormLabel>
              <Select
                onValueChange={field.onChange}
                value={field.value || ''}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a category" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {activeCategories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="isActive"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">Active</FormLabel>
                <div className="text-sm text-muted-foreground">
                  This item will be visible in the inventory
                </div>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={loading}>
            {loading ? 'Saving...' : `${initialData ? 'Update' : 'Create'} Item`}
          </Button>
        </div>
      </form>
    </Form>
  );
}
