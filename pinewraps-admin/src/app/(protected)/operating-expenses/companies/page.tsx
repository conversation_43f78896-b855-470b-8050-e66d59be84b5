"use client";

import { useState, useEffect } from "react";
import { Heading } from "@/components/ui/heading";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { DataTable } from "@/components/ui/data-table";
import { Plus } from "lucide-react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { CompanyDialog } from "@/components/companies/company-dialog";
import { columns, CompanyColumn } from "@/components/companies/columns";
import { companyService, Company } from "@/services/company.service";

// Using the imported columns from components/companies/columns.tsx

export default function CompaniesPage() {
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const [editingCompany, setEditingCompany] = useState<CompanyColumn | undefined>(undefined);
  const [companies, setCompanies] = useState<CompanyColumn[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Directly fetch companies using the service instead of the hook
  useEffect(() => {
    const fetchCompanies = async () => {
      try {
        setLoading(true);
        setError(null);
        console.log('Fetching companies...');
        const data = await companyService.getCompanies();
        console.log('Companies data received:', data);
        
        // The service now guarantees an array (even if empty)
        const formattedData = data.map(company => ({
          id: company.id,
          name: company.name,
          email: company.email || null,
          phone: company.phone || null,
          address: company.address || null
        }));
        
        setCompanies(formattedData);
        console.log('Formatted companies data:', formattedData);
      } catch (err) {
        console.error('Error fetching companies:', err);
        setError('Failed to load companies');
        toast.error('Failed to load companies');
        setCompanies([]);
      } finally {
        setLoading(false);
      }
    };

    fetchCompanies();
  }, []);

  const handleAddCompany = () => {
    setEditingCompany(undefined);
    setOpen(true);
  };
  
  const handleEditCompany = (company: CompanyColumn) => {
    setEditingCompany(company);
    setOpen(true);
  };
  
  const handleDialogClose = () => {
    setOpen(false);
  };
  
  const handleDialogSuccess = () => {
    setOpen(false);
    // Manually refetch companies
    const fetchCompanies = async () => {
      try {
        setLoading(true);
        const data = await companyService.getCompanies();
        // The service now guarantees an array
        const formattedData = data.map(company => ({
          id: company.id,
          name: company.name,
          email: company.email || null,
          phone: company.phone || null,
          address: company.address || null
        }));
        setCompanies(formattedData);
      } catch (err) {
        console.error('Error refetching companies:', err);
      } finally {
        setLoading(false);
      }
    };
    
    fetchCompanies();
  };

  return (
    <div className="flex-col">
      <div className="flex-1 space-y-4 p-8 pt-6">
        <div className="flex items-center justify-between">
          <Heading
            title="Companies"
            description="Manage operating expense companies"
          />
          <Button onClick={handleAddCompany}>
            <Plus className="mr-2 h-4 w-4" />
            Add Company
          </Button>
        </div>
        <Separator />
        
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}
        
        {loading ? (
          <div className="flex justify-center items-center h-40">
            <p>Loading companies...</p>
          </div>
        ) : companies.length === 0 ? (
          <div className="text-center py-10">
            <p className="text-gray-500">No companies found. Add your first company using the button above.</p>
          </div>
        ) : (
          <DataTable
            columns={columns}
            data={companies}
            searchKey="name"
            loading={loading}
          />
        )}
        
        <CompanyDialog 
          open={open}
          onClose={handleDialogClose}
          onSuccess={handleDialogSuccess}
          initialData={editingCompany}
        />
      </div>
    </div>
  );
}
