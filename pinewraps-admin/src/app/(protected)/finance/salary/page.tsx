'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, Plus, CheckCircle2, Clock, Download } from 'lucide-react';
import { format } from 'date-fns';
import { Heading } from "@/components/ui/heading";
import { Separator } from "@/components/ui/separator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { AddSalaryModal } from '@/components/finance/add-salary-modal';
import { SalaryDetailsModal } from '@/components/finance/salary-details-modal';
import { apiClient } from '@/lib/api/client';
import { salaryService } from '@/services/salary.service';
import type { Salary } from '@/services/salary.service';
import { cn } from "@/lib/utils";

interface Employee {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  position: string;
}

// Header component with consistent styling
const Header = ({ onAdd, onExport }: { onAdd: () => void; onExport: () => void }) => (
  <div className="flex items-center justify-between">
    <div>
      <Heading 
        title="Payroll Management" 
        description="Manage employee salaries and payments" 
      />
    </div>
    <div className="flex gap-2">
      <Button variant="outline" onClick={onExport} className="gap-2">
        <Download className="h-4 w-4" /> Export
      </Button>
      <Button onClick={onAdd} className="gap-2">
        <Plus className="h-4 w-4" /> Add Salary
      </Button>
    </div>
  </div>
);

// Stats cards component
const StatsCards = ({ totalPayroll, paidSalaries, pendingSalaries }: { 
  totalPayroll: number;
  paidSalaries: number;
  pendingSalaries: number;
}) => (
  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
    <Card>
      <CardContent className="flex items-center p-6">
        <div className="bg-blue-100 p-3 rounded-full mr-4">
          <TrendingUp className="h-6 w-6 text-blue-600" />
        </div>
        <div>
          <p className="text-sm font-medium text-muted-foreground">Total Payroll</p>
          <h3 className="text-2xl font-bold">AED {totalPayroll.toLocaleString()}</h3>
        </div>
      </CardContent>
    </Card>
    <Card>
      <CardContent className="flex items-center p-6">
        <div className="bg-green-100 p-3 rounded-full mr-4">
          <CheckCircle2 className="h-6 w-6 text-green-600" />
        </div>
        <div>
          <p className="text-sm font-medium text-muted-foreground">Paid Salaries</p>
          <h3 className="text-2xl font-bold">AED {paidSalaries.toLocaleString()}</h3>
        </div>
      </CardContent>
    </Card>
    <Card>
      <CardContent className="flex items-center p-6">
        <div className="bg-yellow-100 p-3 rounded-full mr-4">
          <Clock className="h-6 w-6 text-yellow-600" />
        </div>
        <div>
          <p className="text-sm font-medium text-muted-foreground">Pending Salaries</p>
          <h3 className="text-2xl font-bold">AED {pendingSalaries.toLocaleString()}</h3>
        </div>
      </CardContent>
    </Card>
  </div>
);

// Salary list component
const SalaryList = ({
  loading,
  error,
  salaries,
  onViewDetails,
  onEdit,
}: {
  loading: boolean;
  error: string | null;
  salaries: Salary[];
  onViewDetails: (salary: Salary) => void;
  onEdit: (salary: Salary) => void;
}) => (
  <Card>
    <CardContent className="p-6">
      {loading ? (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      ) : error ? (
        <div className="text-center py-8 text-destructive">{error}</div>
      ) : salaries.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">No salaries found</div>
      ) : (
        <div className="relative overflow-x-auto">
          <table className="w-full text-sm text-left">
            <thead className="text-xs uppercase bg-muted">
              <tr>
                <th className="px-4 py-3">Employee</th>
                <th className="px-4 py-3">Position</th>
                <th className="px-4 py-3">Month</th>
                <th className="px-4 py-3">Amount</th>
                <th className="px-4 py-3">Status</th>
                <th className="px-4 py-3">Transaction ID</th>
                <th className="px-4 py-3">Actions</th>
              </tr>
            </thead>
            <tbody>
              {salaries.map((salary) => (
                <tr
                  key={salary.id}
                  className="border-b hover:bg-muted/50 transition-colors"
                >
                  <td className="px-4 py-3">
                    {salary.employee?.firstName} {salary.employee?.lastName}
                  </td>
                  <td className="px-4 py-3">{salary.employee?.position}</td>
                  <td className="px-4 py-3">
                    {format(new Date(salary.month), 'MMMM yyyy')}
                  </td>
                  <td className="px-4 py-3">
                    {new Intl.NumberFormat('en-AE', {
                      style: 'currency',
                      currency: 'AED',
                    }).format(salary.amount)}
                  </td>
                  <td className="px-4 py-3">
                    <Badge
                      variant={
                        salary.status === 'PAID'
                          ? 'secondary'
                          : salary.status === 'PENDING'
                          ? 'outline'
                          : 'destructive'
                      }
                      className="gap-1"
                    >
                      {salary.status === 'PAID' ? (
                        <CheckCircle2 className="h-3 w-3" />
                      ) : (
                        <Clock className="h-3 w-3" />
                      )}
                      {salary.status}
                    </Badge>
                  </td>
                  <td className="px-4 py-3">
                    {salary.transactionId || '-'}
                  </td>
                  <td className="px-4 py-3">
                    <div className="flex gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onViewDetails(salary)}
                      >
                        View
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onEdit(salary)}
                      >
                        Edit
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </CardContent>
  </Card>
);

// Filters component
const Filters = ({
  searchQuery,
  onSearchChange,
  selectedYear,
  onYearChange,
  selectedMonth,
  onMonthChange,
  selectedStatus,
  onStatusChange,
  onResetFilters
}: {
  searchQuery: string;
  onSearchChange: (value: string) => void;
  selectedYear: string;
  onYearChange: (value: string) => void;
  selectedMonth: string;
  onMonthChange: (value: string) => void;
  selectedStatus: string;
  onStatusChange: (value: string) => void;
  onResetFilters: () => void;
}) => {
  const months = [
    { value: "01", label: "January" },
    { value: "02", label: "February" },
    { value: "03", label: "March" },
    { value: "04", label: "April" },
    { value: "05", label: "May" },
    { value: "06", label: "June" },
    { value: "07", label: "July" },
    { value: "08", label: "August" },
    { value: "09", label: "September" },
    { value: "10", label: "October" },
    { value: "11", label: "November" },
    { value: "12", label: "December" }
  ];

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 3 }, (_, i) => {
    const year = currentYear - 1 + i;
    return { value: year.toString(), label: year.toString() };
  });

  // Check if any filters are active
  const hasActiveFilters = searchQuery || selectedYear !== 'all' || selectedMonth !== 'all' || selectedStatus !== 'all';

  return (
    <div className="mb-6">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-3">
        <div className="flex items-center space-x-2">
          <input
            type="text"
            placeholder="Search employee..."
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          />
        </div>
        <Select value={selectedYear} onValueChange={onYearChange}>
          <SelectTrigger>
            <SelectValue placeholder="Select year" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Years</SelectItem>
            {years.map((year) => (
              <SelectItem key={year.value} value={year.value}>
                {year.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select value={selectedMonth} onValueChange={onMonthChange}>
          <SelectTrigger>
            <SelectValue placeholder="Select month" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Months</SelectItem>
            {months.map((month) => (
              <SelectItem key={month.value} value={month.value}>
                {month.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select value={selectedStatus} onValueChange={onStatusChange}>
          <SelectTrigger>
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="PAID">Paid</SelectItem>
            <SelectItem value="PENDING">Pending</SelectItem>
          </SelectContent>
        </Select>
      </div>
      {hasActiveFilters && (
        <div className="flex justify-end">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={onResetFilters}
            className="text-sm"
          >
            Reset Filters
          </Button>
        </div>
      )}
    </div>
  );
};

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-AE', {
    style: 'currency',
    currency: 'AED',
  }).format(amount);
};

const formatDate = (date: string | null | undefined) => {
  if (!date) return '-';
  return format(new Date(date), 'dd MMM yyyy');
};

const downloadCSV = (salaries: Salary[]) => {
  // Define headers
  const headers = [
    'Employee Name',
    'Position',
    'Month',
    'Amount',
    'Status',
    'Paid Amount',
    'Paid Date',
    'Transaction ID',
    'Notes',
    'Created At',
  ];

  // Format data rows
  const rows = salaries.map((salary) => [
    `${salary.employee?.firstName} ${salary.employee?.lastName}`,
    salary.employee?.position || '-',
    format(new Date(salary.month), 'MMMM yyyy'),
    formatCurrency(salary.amount),
    salary.status,
    salary.paidAmount ? formatCurrency(salary.paidAmount) : '-',
    formatDate(salary.paidAt),
    salary.transactionId || '-',
    salary.notes || '-',
    formatDate(salary.createdAt),
  ]);

  // Combine headers and rows
  const csvContent = [
    headers.join(','),
    ...rows.map(row => row.map(cell => `"${cell}"`).join(',')),
  ].join('\n');

  // Create blob and download
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `salary_report_${format(new Date(), 'yyyy-MM-dd')}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

export default function PayrollPage() {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [salaries, setSalaries] = useState<Salary[]>([]);
  const [selectedSalary, setSelectedSalary] = useState<Salary | null>(null);
  const [modalMode, setModalMode] = useState<'view' | 'edit'>('view');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  // Filters
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedYear, setSelectedYear] = useState('all');
  const [selectedMonth, setSelectedMonth] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');

  const loadSalaries = async () => {
    setLoading(true);
    setError(null);
    try {
      const params: any = {};
      
      // Only add status filter if it's not 'all'
      if (selectedStatus && selectedStatus !== 'all') {
        params.status = selectedStatus;
      }
      
      // Get all salaries - we'll filter them client-side for better UX
      const data = await salaryService.getSalaries(params);
      setSalaries(data);
    } catch (error: any) {
      console.error('Error loading salaries:', error);
      setError(error.message || 'Failed to load salaries');
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || 'Failed to load salaries',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadSalaries();
  }, [selectedStatus]); // Only reload from API when status changes

  const handleRefresh = () => {
    loadSalaries();
  };

  const handleViewDetails = (salary: Salary) => {
    setSelectedSalary(salary);
    setModalMode('view');
    setShowDetailsModal(true);
  };

  const handleEdit = (salary: Salary) => {
    setSelectedSalary(salary);
    setModalMode('edit');
    setShowDetailsModal(true);
  };

  const handleUpdateSalary = async (id: string, data: any) => {
    try {
      await salaryService.updateSalary(id, data);
      await loadSalaries();
      toast({
        title: "Success",
        description: "Salary updated successfully",
      });
    } catch (error: any) {
      console.error('Error updating salary:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || 'Failed to update salary',
      });
      throw error;
    }
  };

  const handleExport = async () => {
    try {
      await salaryService.exportSalaries();
      toast({
        title: "Success",
        description: "Salaries exported successfully",
      });
    } catch (error: any) {
      console.error('Error exporting salaries:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || 'Failed to export salaries',
      });
    }
  };

  const handleResetFilters = () => {
    setSearchQuery('');
    setSelectedYear('all');
    setSelectedMonth('all');
    setSelectedStatus('all');
  };

  // Filter salaries based on search and filters
  const filteredSalaries = salaries.filter(salary => {
    // Search filter - match employee name or position
    const matchesSearch = !searchQuery || 
      (salary.employee?.firstName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      salary.employee?.lastName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      salary.employee?.position?.toLowerCase().includes(searchQuery.toLowerCase()));

    // Year filter
    const salaryDate = new Date(salary.month);
    const matchesYear = selectedYear === 'all' || 
      salaryDate.getFullYear().toString() === selectedYear;
    
    // Month filter - getMonth() returns 0-11, so add 1 to match our 01-12 values
    const monthValue = (salaryDate.getMonth() + 1).toString().padStart(2, '0');
    const matchesMonth = selectedMonth === 'all' || 
      monthValue === selectedMonth;
    
    // Status filter
    const matchesStatus = selectedStatus === 'all' || 
      salary.status === selectedStatus;

    return matchesSearch && matchesYear && matchesMonth && matchesStatus;
  });

  // Calculate stats
  const totalPayroll = filteredSalaries.reduce((sum, salary) => sum + salary.amount, 0);
  const paidSalaries = filteredSalaries.filter(s => s.status === 'PAID').length;
  const pendingSalaries = filteredSalaries.filter(s => s.status === 'PENDING').length;

  return (
    <div className="flex-1 space-y-4">
      <Header 
        onAdd={() => setShowAddModal(true)}
        onExport={handleExport}
      />
      <Separator />
      
      <StatsCards
        totalPayroll={totalPayroll}
        paidSalaries={paidSalaries}
        pendingSalaries={pendingSalaries}
      />

      <Filters
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        selectedYear={selectedYear}
        onYearChange={setSelectedYear}
        selectedMonth={selectedMonth}
        onMonthChange={setSelectedMonth}
        selectedStatus={selectedStatus}
        onStatusChange={setSelectedStatus}
        onResetFilters={handleResetFilters}
      />

      <SalaryList
        loading={loading}
        error={error}
        salaries={filteredSalaries}
        onViewDetails={handleViewDetails}
        onEdit={handleEdit}
      />

      <AddSalaryModal
        open={showAddModal}
        onClose={() => setShowAddModal(false)}
        onSuccess={handleRefresh}
      />

      <SalaryDetailsModal
        open={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        salary={selectedSalary}
        mode={modalMode}
        onUpdate={handleUpdateSalary}
      />
    </div>
  );
}
