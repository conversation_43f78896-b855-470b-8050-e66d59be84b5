'use client';

import { Suspense, useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { useSubcategoryFilters } from '@/hooks/use-subcategory-filters';
import { Skeleton } from '@/components/ui/skeleton';

interface SubcategoryFiltersProps {
  categoryName?: string; // For specific category pages like 'Cakes', 'Flowers', 'Sets'
  selectedCategory?: string; // For shop page with dynamic category selection
  className?: string;
  label?: string;
}

// Loading skeleton component
function SubcategoryFiltersSkeleton() {
  return (
    <div className="flex flex-wrap gap-2 items-center animate-pulse">
      <span className="text-sm text-gray-600 mr-2">Loading filters...</span>
      {[...Array(4)].map((_, i) => (
        <Skeleton key={i} className="h-8 w-20 rounded-md" />
      ))}
    </div>
  );
}

// Error component
function SubcategoryFiltersError({ error }: { error: string }) {
  return (
    <div className="text-sm text-red-600">
      {error}
    </div>
  );
}

// Main filters component (wrapped in Suspense)
function SubcategoryFiltersContent({
  categoryName,
  selectedCategory,
  className = '',
  label = 'Subcategories:'
}: SubcategoryFiltersProps) {
  const {
    subcategories,
    selectedSubcategory,
    isLoading,
    error,
    handleSubcategoryChange,
    hasSubcategories
  } = useSubcategoryFilters({ categoryName, selectedCategory });

  // Force a refresh on client-side to ensure filters are visible
  useEffect(() => {
    // This will run only on the client side after hydration
    const refreshTimeout = setTimeout(() => {
      // Force a small state update to trigger a re-render
      setForceRefresh(prev => !prev);
    }, 100);

    return () => clearTimeout(refreshTimeout);
  }, []);

  // State to force refresh after hydration
  const [forceRefresh, setForceRefresh] = useState(false);

  // Don't render if no subcategories and not loading
  if (!hasSubcategories && !isLoading && !error) {
    return null;
  }

  // Show error state
  if (error) {
    return <SubcategoryFiltersError error={error} />;
  }

  // Show loading state
  if (isLoading) {
    return <SubcategoryFiltersSkeleton />;
  }

  return (
    <div className={`flex flex-wrap gap-2 items-center ${className}`}>
      <span className="text-sm text-gray-600 mr-2 flex-shrink-0">{label}</span>
      <div className="flex flex-wrap gap-2">
        <Button
          variant={selectedSubcategory === '' ? 'default' : 'outline'}
          onClick={() => handleSubcategoryChange('')}
          className="flex items-center gap-2"
          size="sm"
        >
          All
        </Button>
        {subcategories.map((subcategory) => (
          <Button
            key={subcategory.id}
            variant={selectedSubcategory === subcategory.id ? 'default' : 'outline'}
            onClick={() => handleSubcategoryChange(subcategory.id)}
            className="flex items-center gap-2"
            size="sm"
          >
            {subcategory.name}
          </Button>
        ))}
      </div>
    </div>
  );
}

// Main exported component with Suspense boundary
export function SubcategoryFilters(props: SubcategoryFiltersProps) {
  return (
    <Suspense fallback={<SubcategoryFiltersSkeleton />}>
      <SubcategoryFiltersContent {...props} />
    </Suspense>
  );
}

// Mobile-optimized version
export function MobileSubcategoryFilters(props: SubcategoryFiltersProps) {
  return (
    <div className="block sm:hidden">
      <Suspense fallback={<SubcategoryFiltersSkeleton />}>
        <SubcategoryFiltersContent 
          {...props} 
          className="px-4 py-2 bg-gray-50 rounded-lg"
        />
      </Suspense>
    </div>
  );
}

// Desktop version
export function DesktopSubcategoryFilters(props: SubcategoryFiltersProps) {
  return (
    <div className="hidden sm:block">
      <Suspense fallback={<SubcategoryFiltersSkeleton />}>
        <SubcategoryFiltersContent {...props} />
      </Suspense>
    </div>
  );
}

export default SubcategoryFilters;
