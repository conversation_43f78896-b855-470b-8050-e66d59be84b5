'use client';

import { useEffect, useState } from 'react';
import { Heading } from '@/components/ui/heading';
import { Separator } from '@/components/ui/separator';
import { AnalyticsCard } from '@/components/ui/analytics-card';
import { ShoppingBag, Users, DollarSign, TrendingUp, RefreshCw } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import { getAuth } from 'firebase/auth';
import { useAuthState } from 'react-firebase-hooks/auth';
import { DateRangePicker } from '@/components/ui/date-range-picker';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface DateRange {
  from?: Date;
  to?: Date;
}

interface Analytics {
  totalOrders: number;
  totalCustomers: number;
  totalRevenue: number;
  growthRate: number;
  pendingOrders: number;
  processingOrders: number;
  completedOrders: number;
  dateRange?: {
    startDate: string;
    endDate: string;
  };
}

const timeRanges = [
  { value: '7d', label: 'Last 7 days' },
  { value: '14d', label: 'Last 14 days' },
  { value: '30d', label: 'Last 30 days' },
  { value: '3m', label: 'Last 3 months' },
  { value: 'all', label: 'All time' },
  { value: 'custom', label: 'Custom Range' },
];

export default function Page() {
  const [timeRange, setTimeRange] = useState('7d');
  const [dateRange, setDateRange] = useState<DateRange>();
  const [analytics, setAnalytics] = useState<Analytics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [user] = useAuthState(getAuth());
  const { toast } = useToast();

  const fetchAnalytics = async () => {
    try {
      if (!user) {
        toast({
        title: 'Authentication required',
        variant: 'destructive'
      });
        return;
      }

      setIsRefreshing(true);
      const token = await user.getIdToken();

      // Build query parameters
      const params = new URLSearchParams();
      params.append('timeRange', timeRange);
      
      if (timeRange === 'custom' && dateRange?.from) {
        params.append('startDate', format(dateRange.from, 'yyyy-MM-dd'));
        if (dateRange.to) {
          params.append('endDate', format(dateRange.to, 'yyyy-MM-dd'));
        }
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/orders/analytics?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('API Error:', errorData);
        throw new Error(errorData.message || 'Failed to fetch analytics');
      }
      
      const data = await response.json();
      if (!data.success) {
        throw new Error(data.error?.message || 'Failed to fetch analytics');
      }
      setAnalytics(data.data);
    } catch (error) {
      toast({
        title: 'Failed to load analytics data',
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: 'destructive'
      });
      console.error('Error fetching analytics:', error);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Reset date range when switching away from custom range
  useEffect(() => {
    if (timeRange !== 'custom') {
      setDateRange(undefined);
    }
  }, [timeRange]);

  // Fetch analytics when time range or date range changes
  useEffect(() => {
    if (user && (timeRange !== 'custom' || (timeRange === 'custom' && dateRange?.from))) {
      fetchAnalytics();
    }
  }, [timeRange, dateRange, user]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AE', {
      style: 'currency',
      currency: 'AED',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="flex-1 space-y-4">
      <div className="flex items-center justify-between">
        <Heading title="Dashboard" description="Welcome to your dashboard" />
        <div className="flex items-center gap-4">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              {timeRanges.map((range) => (
                <SelectItem key={range.value} value={range.value}>
                  {range.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {timeRange === 'custom' && (
            <DateRangePicker
              value={dateRange}
              onChange={setDateRange}
              onReset={() => setDateRange(undefined)}
            />
          )}

          <Button
            variant="outline"
            size="icon"
            onClick={fetchAnalytics}
            disabled={isRefreshing}
          >
            <RefreshCw className={cn("h-4 w-4", isRefreshing && "animate-spin")} />
          </Button>
        </div>
      </div>

      <Separator />

      <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
        <AnalyticsCard
          title="Total Orders"
          icon={ShoppingBag}
          value={analytics?.totalOrders || 0}
          isLoading={isLoading}
        />
        <AnalyticsCard
          title="Total Customers"
          icon={Users}
          value={analytics?.totalCustomers || 0}
          isLoading={isLoading}
        />
        <AnalyticsCard
          title="Total Revenue"
          icon={DollarSign}
          value={analytics?.totalRevenue || 0}
          isCurrency
          isLoading={isLoading}
        />
        <AnalyticsCard
          title="Growth Rate"
          icon={TrendingUp}
          value={analytics?.growthRate || 0}
          trend={analytics?.growthRate ? {
            value: Math.abs(analytics.growthRate),
            isPositive: analytics.growthRate >= 0
          } : undefined}
          isLoading={isLoading}
        />
      </div>
    </div>
  );
}
