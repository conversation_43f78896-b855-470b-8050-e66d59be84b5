'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';

export default function NotFound() {
  return (
    <div className="min-h-[80vh] flex items-center justify-center bg-background px-4">
      <div className="max-w-md w-full text-center">
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <div className="relative mb-8">
            <h1 className="text-9xl font-bold text-primary/10">404</h1>
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-4xl font-bold text-primary">Oops!</span>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.5 }}
        >
          <h2 className="text-2xl font-semibold mb-4 text-foreground">
            Page Not Found
          </h2>
          <p className="text-muted-foreground mb-8">
            The page you are looking for might have been removed, had its name changed,
            or is temporarily unavailable.
          </p>

          <div className="space-y-4">
            <Link href="/">
              <Button variant="default" size="lg" className="w-full">
                Return Home
              </Button>
            </Link>
            <Link href="/shop">
              <Button variant="outline" size="lg" className="w-full">
                Browse Products
              </Button>
            </Link>
          </div>
        </motion.div>

        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.4, duration: 0.5 }}
          className="mt-12 text-sm text-muted-foreground"
        >
          <p>Need help? Contact our support team at</p>
          <a href="mailto:<EMAIL>" className="text-primary hover:underline">
            <EMAIL>
          </a>
        </motion.div>
      </div>
    </div>
  );
}
