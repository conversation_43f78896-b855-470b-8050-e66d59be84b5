'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ArrowLeft, Save, Loader2, Check, ChevronsUpDown } from 'lucide-react';
import { useRouter } from 'next/navigation';
import api from '@/lib/api';
import { getFirebaseToken } from '@/lib/firebase';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { cn } from '@/lib/utils';

interface ExpenseCategory {
  id: string;
  name: string;
  color?: string;
}

interface Company {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
}

interface OperatingExpense {
  id: string;
  invoiceNumber: string;
  amount: number;
  currency: string;
  invoiceDate: string;
  dueDate?: string;
  categoryId: string;
  companyId: string;
  description?: string;
  bankReference?: string;
  attachments: string[];
  status: string;
  category: ExpenseCategory;
  company: Company;
}

interface ExpenseFormProps {
  expense?: OperatingExpense;
  onSuccess?: (expense: OperatingExpense) => void;
  onCancel?: () => void;
}

export default function ExpenseForm({ expense, onSuccess, onCancel }: ExpenseFormProps) {
  const router = useRouter();
  const [categories, setCategories] = useState<ExpenseCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    invoiceNumber: '',
    companyId: '',
    amount: '',
    currency: 'AED',
    invoiceDate: '',
    dueDate: '',
    categoryId: '',
    description: '',
    bankReference: '',
    attachments: [] as string[],
  });
  const [companies, setCompanies] = useState<Company[]>([]);

  const isEditing = !!expense;

  useEffect(() => {
    fetchCategories();
    fetchCompanies();
    if (expense) {
      setFormData({
        invoiceNumber: expense.invoiceNumber,
        companyId: expense.companyId,
        amount: expense.amount.toString(),
        currency: expense.currency,
        invoiceDate: expense.invoiceDate.split('T')[0],
        dueDate: expense.dueDate ? expense.dueDate.split('T')[0] : '',
        categoryId: expense.categoryId,
        description: expense.description || '',
        bankReference: expense.bankReference || '',
        attachments: expense.attachments || [],
      });
    }
  }, [expense]);

  const fetchCompanies = async () => {
    try {
      const token = await getFirebaseToken();
      const response = await api.get('/api/companies', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      // Handle both response formats
      const companiesData = response.data.data || response.data;
      if (Array.isArray(companiesData)) {
        setCompanies(companiesData);
      } else {
        console.error('Unexpected companies data format:', response.data);
        setCompanies([]);
      }
    } catch (error) {
      console.error('Error fetching companies:', error);
      alert('Failed to load companies');
    }
  };



  const fetchCategories = async () => {
    try {
      const token = await getFirebaseToken();
      const response = await api.get('/api/operating-expenses/categories', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      setCategories(response.data.data);
    } catch (error) {
      console.error('Error fetching categories:', error);
      alert('Failed to load categories');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.invoiceNumber.trim() || !formData.companyId || 
        !formData.amount || !formData.invoiceDate || !formData.categoryId) {
      alert('Please fill in all required fields');
      return;
    }

    setLoading(true);
    try {
      const token = await getFirebaseToken();
      // Find the selected company to get its name
      const selectedCompany = companies.find(company => company.id === formData.companyId);
      if (!selectedCompany) {
        alert('Please select a valid company');
        setLoading(false);
        return;
      }

      const submitData = {
        invoiceNumber: formData.invoiceNumber.trim(),
        companyId: formData.companyId,
        companyName: selectedCompany.name, // Add companyName as required by the API
        amount: parseFloat(formData.amount),
        currency: formData.currency,
        invoiceDate: formData.invoiceDate,
        dueDate: formData.dueDate || undefined,
        categoryId: formData.categoryId,
        description: formData.description.trim() || undefined,
        bankReference: formData.bankReference.trim() || undefined,
        attachments: formData.attachments,
      };

      let response;
      if (isEditing) {
        response = await api.put(`/api/operating-expenses/${expense.id}`, submitData, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });
        alert('Expense updated successfully');
      } else {
        response = await api.post('/api/operating-expenses', submitData, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });
        alert('Expense created successfully');
      }

      if (onSuccess) {
        onSuccess(response.data.data);
      } else {
        router.push('/operating-expenses');
      }
    } catch (error: any) {
      console.error('Error saving expense:', error);
      alert(error.response?.data?.error || 'Failed to save expense');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      router.back();
    }
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={handleCancel}
          disabled={loading}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <div className="flex-1">
          <h1 className="text-3xl font-bold tracking-tight">
            {isEditing ? 'Edit Operating Expense' : 'Add Operating Expense'}
          </h1>
          <p className="text-gray-600">
            {isEditing ? 'Update expense details below' : 'Create a new operating expense entry'}
          </p>
        </div>
      </div>

      {/* Form */}
      <div className="border rounded-lg p-6 bg-white">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Invoice Number *</label>
              <Input
                placeholder="e.g., DEWA-2024-001234"
                value={formData.invoiceNumber}
                onChange={(e) => setFormData(prev => ({ ...prev, invoiceNumber: e.target.value }))}
                required
                disabled={loading}
              />
            </div>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Company *</label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      className="w-full justify-between font-normal"
                      disabled={loading}
                    >
                      {formData.companyId
                        ? companies.find((company) => company.id === formData.companyId)?.name
                        : "Select company..."}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="p-0" align="start" side="bottom" sideOffset={5} style={{ width: "var(--radix-popover-trigger-width)" }}>
                    <Command>
                      <CommandInput placeholder="Search company..." />
                      <CommandEmpty>No company found.</CommandEmpty>
                      <CommandList>
                        <CommandGroup>
                          {companies.map((company) => (
                            <CommandItem
                              key={company.id}
                              value={company.name}
                              onSelect={() => {
                                setFormData(prev => ({ ...prev, companyId: company.id }));
                              }}
                            >
                              <Check
                                className={cn(
                                  "mr-2 h-4 w-4",
                                  formData.companyId === company.id ? "opacity-100" : "opacity-0"
                                )}
                              />
                              {company.name}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </div>

          {/* Amount and Currency */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="md:col-span-2">
              <label className="block text-sm font-medium mb-1">Amount *</label>
              <Input
                type="number"
                step="0.01"
                placeholder="0.00"
                value={formData.amount}
                onChange={(e) => setFormData(prev => ({ ...prev, amount: e.target.value }))}
                required
                disabled={loading}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Currency</label>
              <select
                className="w-full p-2 border rounded-md"
                value={formData.currency}
                onChange={(e) => setFormData(prev => ({ ...prev, currency: e.target.value }))}
                disabled={loading}
              >
                <option value="AED">AED</option>
                <option value="USD">USD</option>
                <option value="EUR">EUR</option>
              </select>
            </div>
          </div>

          {/* Dates */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Invoice Date *</label>
              <Input
                type="date"
                value={formData.invoiceDate}
                onChange={(e) => setFormData(prev => ({ ...prev, invoiceDate: e.target.value }))}
                required
                disabled={loading}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Due Date</label>
              <Input
                type="date"
                value={formData.dueDate}
                onChange={(e) => setFormData(prev => ({ ...prev, dueDate: e.target.value }))}
                disabled={loading}
              />
            </div>
          </div>

          {/* Category */}
          <div>
            <label className="block text-sm font-medium mb-1">Category *</label>
            <select
              className="w-full p-2 border rounded-md"
              value={formData.categoryId}
              onChange={(e) => setFormData(prev => ({ ...prev, categoryId: e.target.value }))}
              required
              disabled={loading}
            >
              <option value="">Select a category</option>
              {categories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium mb-1">Description</label>
            <textarea
              className="w-full p-2 border rounded-md"
              placeholder="Additional notes about this expense..."
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
              disabled={loading}
            />
          </div>

          {/* Bank Reference */}
          <div>
            <label className="block text-sm font-medium mb-1">Bank Reference</label>
            <Input
              placeholder="e.g., TXN123456789, REF-2024-001"
              value={formData.bankReference}
              onChange={(e) => setFormData(prev => ({ ...prev, bankReference: e.target.value }))}
              disabled={loading}
            />
            <p className="text-xs text-gray-500 mt-1">
              Bank transaction reference or payment reference number
            </p>
          </div>

          {/* Submit Buttons */}
          <div className="flex gap-2 pt-4">
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  {isEditing ? 'Updating...' : 'Creating...'}
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {isEditing ? 'Update Expense' : 'Create Expense'}
                </>
              )}
            </Button>
            <Button 
              type="button" 
              variant="outline" 
              onClick={handleCancel}
              disabled={loading}
            >
              Cancel
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
