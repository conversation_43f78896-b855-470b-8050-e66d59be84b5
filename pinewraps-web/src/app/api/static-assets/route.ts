import { NextRequest, NextResponse } from 'next/server';

// This route handler will add X-Robots-Tag headers to static assets
// to prevent them from being indexed by search engines

export async function GET(request: NextRequest) {
  const url = request.nextUrl.pathname;
  
  // Check if this is a static CSS file
  const isStaticCss = url.includes('/_next/static/css/') || 
                     (url.includes('.css') && request.nextUrl.searchParams.toString().includes('dpl='));
  
  if (isStaticCss) {
    // Return a response with X-Robots-Tag headers
    return new NextResponse(null, {
      status: 200,
      headers: {
        'X-Robots-Tag': 'noindex, nofollow',
        'Cache-Control': 'public, max-age=31536000'
      }
    });
  }
  
  // For other requests, just pass through
  return NextResponse.next();
}

export const dynamic = 'force-dynamic';
