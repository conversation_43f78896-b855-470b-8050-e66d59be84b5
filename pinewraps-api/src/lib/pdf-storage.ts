import type { Bucket } from '@google-cloud/storage';
import { getBucket } from './firebase-admin';
import { nanoid } from 'nanoid';

export interface PdfUpload {
  buffer: Buffer;
  mimetype: string;
  originalname: string;
}

export class PdfStorageService {
  private bucket: Bucket;

  constructor() {
    this.bucket = getBucket();
  }

  /**
   * Upload a PDF file to Firebase Storage
   */
  async uploadPurchaseOrderInvoice(
    purchaseOrderId: string,
    file: PdfUpload
  ): Promise<string> {
    try {
      // Generate a unique filename
      const fileExtension = file.originalname.split('.').pop()?.toLowerCase() || 'pdf';
      const filename = `${nanoid()}.${fileExtension}`;

      // Define the file path in the storage bucket
      const filePath = `purchase-orders/${purchaseOrderId}/invoices/${filename}`;

      // Create a new blob in the bucket
      const blob = this.bucket.file(filePath);

      // Upload the file
      await blob.save(file.buffer, {
        metadata: {
          contentType: file.mimetype,
          metadata: {
            purchaseOrderId,
            originalName: file.originalname
          }
        }
      });

      // Make the file publicly accessible
      await blob.makePublic();

      // Get the public URL
      const publicUrl = `https://storage.googleapis.com/${this.bucket.name}/${filePath}`;
      
      return publicUrl;
    } catch (error) {
      console.error('Error uploading PDF:', error);
      throw new Error(`Failed to upload PDF: ${error.message}`);
    }
  }

  /**
   * Delete a PDF file from Firebase Storage
   */
  async deletePurchaseOrderInvoice(fileUrl: string): Promise<void> {
    try {
      // Extract the file path from the URL
      const urlParts = fileUrl.split(`https://storage.googleapis.com/${this.bucket.name}/`);
      
      if (urlParts.length !== 2) {
        throw new Error('Invalid file URL format');
      }
      
      const filePath = urlParts[1];
      
      // Get the file reference
      const file = this.bucket.file(filePath);
      
      // Check if file exists
      const [exists] = await file.exists();
      
      if (!exists) {
        console.warn(`File ${filePath} does not exist in storage`);
        return;
      }
      
      // Delete the file
      await file.delete();
      
      console.log(`Successfully deleted file: ${filePath}`);
    } catch (error) {
      console.error('Error deleting PDF:', error);
      throw new Error(`Failed to delete PDF: ${error.message}`);
    }
  }
}

// Export a singleton instance
export const pdfStorageService = new PdfStorageService();
