# Database Migration Guide: Supabase to Local pgAdmin4

This guide will help you export your Supabase database and import it into pgAdmin4 for local development and management.

## Prerequisites

1. PostgreSQL command-line tools installed (`pg_dump` and `pg_restore`)
2. Node.js installed
3. Access to your Supabase database credentials
4. pgAdmin4 installed locally

## Step 1: Install Dependencies

First, make sure you have the required dependencies:

```bash
npm install dotenv
```

## Step 2: Export Your Supabase Database

1. Make sure your `.env` file contains the correct Supabase database connection URL:
   - `DATABASE_URL` or `DIRECT_URL` should be set to your Supabase PostgreSQL connection string

2. Run the export script:

```bash
node scripts/export-db.js
```

This will:
- Connect to your Supabase database using the credentials in your `.env` file
- Export the database to a file in the `db-export` directory
- The file will be named with the database name and timestamp

## Step 3: Import Into pgAdmin4

### Option 1: Using pgAdmin4 GUI

1. Open pgAdmin4
2. Create a new database or select an existing one
3. Right-click on the database and select "Restore..."
4. In the "Restore" dialog:
   - Select the exported file from the `db-export` directory
   - Set Format to "Custom or tar"
   - Click "Restore"

### Option 2: Using the Import Script

1. Run the import script:

```bash
node scripts/import-db.js path/to/exported/file.sql
```

2. Follow the prompts to enter your local PostgreSQL connection details:
   - Host (default: localhost)
   - Port (default: 5432)
   - Username (default: postgres)
   - Password
   - Database name

3. The script will:
   - Create the database if it doesn't exist
   - Import the data from the exported file
   - Optionally create a `.env.local` file with the local database connection

## Troubleshooting

### Export Issues

1. **Error: DATABASE_URL not found**
   - Make sure your `.env` file contains the `DATABASE_URL` or `DIRECT_URL` variable
   - Ensure the URL follows the format: `postgresql://user:password@host:port/dbname`

2. **pg_dump command not found**
   - Install PostgreSQL command-line tools
   - On Mac: `brew install postgresql`
   - On Windows: Install from PostgreSQL website and add bin directory to PATH

### Import Issues

1. **Permission denied**
   - Make sure you have the correct credentials for your local PostgreSQL instance
   - Check that the user has permission to create databases

2. **Database already exists**
   - If you're importing to an existing database, make sure it's empty or you're prepared to overwrite data
   - You can drop the database first: `DROP DATABASE dbname;`

3. **Version mismatch**
   - If you get version mismatch errors, make sure your local PostgreSQL version is compatible with Supabase's version

## Using the Local Database

After importing, you can connect your application to the local database by:

1. Creating a `.env.local` file with:
```
DATABASE_URL=postgresql://user:password@localhost:5432/dbname
DIRECT_URL=postgresql://user:password@localhost:5432/dbname
```

2. Running your application with:
```bash
npx dotenv -e .env.local -- npm run dev
```

## Important Notes

- **Never push database dumps to version control**
- **Keep your database credentials secure**
- The export file contains all your data, so handle it with care
- For large databases, the export/import process may take some time
