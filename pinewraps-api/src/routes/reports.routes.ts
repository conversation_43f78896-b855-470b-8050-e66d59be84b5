import { Router } from 'express';
import { requireAuth, requireAccess } from '../middleware/auth';
import { asyncHandler } from '../middleware/async';
import { ReportsController } from '../controllers/reports.controller';
import { UserRole } from '@prisma/client';

const router = Router();

// All reports routes require admin access
router.use(requireAuth);
router.use(requireAccess([UserRole.SUPER_ADMIN, UserRole.ADMIN]));

// Sales overview reports
router.get('/sales/summary', asyncHandler(ReportsController.getSalesSummary));
router.get('/sales/daily', asyncHandler(ReportsController.getDailySales));
router.get('/products/top', asyncHandler(ReportsController.getTopProducts));
router.get('/categories/sales', asyncHandler(ReportsController.getTopCategories));

// Export reports
router.get('/export', asyncHandler(ReportsController.exportReport));

export default router;
