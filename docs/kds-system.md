# Kitchen Display System (KDS) Documentation

## Overview
The KDS system handles all department screens (Kitchen, Design, Final Check) for both POS and Online orders. It provides a unified interface for managing orders across different departments while maintaining separate sources.

## Controller Structure

### 1. Core Functions

```typescript
kdsController {
  // Get Orders for Department
  getDepartmentOrders(department: 'KITCH<PERSON>' | 'DESIGN' | 'FINAL_CHECK', source: 'POS' | 'ONLINE')
  
  // Update Order Status
  updateDepartmentStatus(orderId: string, department: string, action: 'START' | 'COMPLETE', notes?: string)
  
  // Add Department Notes
  addDepartmentNotes(orderId: string, department: string, notes: string)
}
```

### 2. Status Mapping

```typescript
DEPARTMENT_STATUS_MAP = {
  KITCHEN: {
    QUEUE: { POS: 'KITCHEN_QUEUE', ONLINE: 'KITCHEN_QUEUE' },
    PROCESSING: { POS: 'KITCHEN_PROCESSING', ONLINE: 'KITCHEN_PROCESSING' },
    READY: { POS: 'KITCHEN_READY', ONLINE: 'KITCHEN_READY' }
  },
  DESIGN: {
    QUEUE: { POS: 'DESIGN_QUEUE', ONLINE: 'DESIGN_QUEUE' },
    PROCESSING: { POS: 'DESIGN_PROCESSING', ONLINE: 'DESIGN_PROCESSING' },
    READY: { POS: 'DESIGN_READY', ONLINE: 'DESIGN_READY' }
  },
  FINAL_CHECK: {
    QUEUE: { POS: 'FINAL_CHECK_QUEUE', ONLINE: 'FINAL_CHECK_QUEUE' },
    PROCESSING: { POS: 'FINAL_CHECK_PROCESSING', ONLINE: 'FINAL_CHECK_PROCESSING' },
    READY: { POS: 'READY_FOR_PICKUP', ONLINE: 'READY_FOR_PICKUP' }
  }
}
```

### 3. Common Order Interface

```typescript
interface KDSOrder {
  id: string;
  orderNumber: string;
  source: 'POS' | 'ONLINE';
  status: string;
  
  // Customer Info
  customerName: string;
  customerPhone: string;
  customerEmail?: string;
  
  // Delivery Info
  deliveryMethod: 'PICKUP' | 'DELIVERY';
  deliveryDate?: Date;
  deliveryTimeSlot?: string;
  
  // Department Info
  departmentStartTime?: Date;
  departmentEndTime?: Date;
  departmentNotes?: string;
  
  // Items
  items: {
    id: string;
    name: string;
    quantity: number;
    variations: Array<{
      type: string;
      value: string;
    }>;
    notes?: string;
  }[];
  
  // Metadata
  createdAt: Date;
  updatedAt: Date;
  requiresKitchen: boolean;
  requiresDesign: boolean;
  requiresFinalCheck: boolean;
}
```

## Implementation Plan

### Backend

1. Controller Implementation
   - Create KDS controller file
   - Implement order fetching for both sources
   - Add status management functions
   - Add department note handling

2. Data Transformation
   - Transform POS orders to KDS format
   - Transform Online orders to KDS format
   - Validate transformed data

3. Status Management
   - Implement status transition logic
   - Add validation for status changes
   - Track department timing

### Frontend

1. Department Screens
   - Kitchen Screen
   - Design Screen
   - Final Check Screen

2. Common Components
   - Order Card Component
   - Department Action Buttons
   - Status Indicators
   - Notes Section

3. Features
   - POS/Online Tabs
   - Order Search
   - Status Filtering
   - Department Notes
   - Timer Tracking

## Status Flow

1. Kitchen Department
   ```
   QUEUE -> PROCESSING -> READY
   ```

2. Design Department
   ```
   QUEUE -> PROCESSING -> READY
   ```

3. Final Check Department
   ```
   QUEUE -> PROCESSING -> READY_FOR_PICKUP
   ```

## Key Features

1. Order Management
   - View orders by department
   - Filter by source (POS/Online)
   - Search functionality
   - Status updates

2. Department Actions
   - Start processing
   - Add notes
   - Mark as ready
   - Print tickets

3. Tracking
   - Processing time
   - Department notes
   - Status history
   - Staff assignment

## Benefits

1. Unified Interface
   - Consistent UI across departments
   - Shared components
   - Common status flow

2. Improved Efficiency
   - Quick status updates
   - Easy order tracking
   - Department-specific views

3. Better Organization
   - Clear status progression
   - Department separation
   - Source tracking

## Next Steps

1. Backend Development
   - [ ] Create KDS controller
   - [ ] Implement order fetching
   - [ ] Add status management
   - [ ] Set up department notes

2. Frontend Development
   - [ ] Create department screens
   - [ ] Build shared components
   - [ ] Implement POS/Online tabs
   - [ ] Add order management features

3. Testing
   - [ ] Unit tests for controller
   - [ ] Integration tests
   - [ ] UI/UX testing
   - [ ] Performance testing
