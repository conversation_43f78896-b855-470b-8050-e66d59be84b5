#!/usr/bin/env node

/**
 * Safe Migration Script
 * Prevents accidental database resets during migrations
 */

const { spawn } = require('child_process');
const DatabaseProtection = require('./db-protection');

async function safeMigrate() {
  const protection = new DatabaseProtection();
  
  console.log('🛡️  Safe Database Migration - Protection Enabled');
  console.log('═══════════════════════════════════════════════');
  
  const args = process.argv.slice(2);
  const command = args[0];
  
  // Check for dangerous migration commands
  const dangerousCommands = ['reset', 'dev --create-only'];
  const isDangerous = dangerousCommands.some(dangerous => 
    args.join(' ').includes(dangerous)
  );

  if (isDangerous) {
    console.log('🚨 DANGEROUS MIGRATION DETECTED - Checking permissions...');
    const allowed = await protection.confirmDestructiveOperation(`prisma migrate ${args.join(' ')}`);
    
    if (!allowed) {
      console.log('❌ Dangerous migration blocked by database protection');
      process.exit(1);
    }
  }

  // Create backup before migration (if in production)
  if (protection.isProductionDatabase() || protection.isProductionEnvironment()) {
    protection.createBackup();
  }

  console.log('✅ Running safe database migration...');
  
  const prisma = spawn('npx', ['prisma', 'migrate', ...args], {
    stdio: 'inherit',
    shell: true
  });

  prisma.on('close', (code) => {
    if (code === 0) {
      console.log('✅ Database migration completed successfully');
    } else {
      console.log(`❌ Database migration failed with code ${code}`);
    }
    process.exit(code);
  });

  prisma.on('error', (error) => {
    console.error('❌ Error running database migration:', error);
    process.exit(1);
  });
}

safeMigrate().catch(error => {
  console.error('❌ Safe migration script error:', error);
  process.exit(1);
});
