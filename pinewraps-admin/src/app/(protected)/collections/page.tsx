'use client';

import { useEffect, useState } from 'react';
import { Collection } from '@/types/collection';
import { CollectionService } from '@/services/collection.service';
import { Button } from '@/components/ui/button';
import { Plus, Download, Upload } from 'lucide-react';
import { Heading } from '@/components/ui/heading';
import { Separator } from '@/components/ui/separator';
import { useRouter } from 'next/navigation';
import { DataTable } from '@/components/ui/data-table';
import { ColumnDef } from '@tanstack/react-table';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import { toast } from '@/lib/toast';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { MoreHorizontal } from 'lucide-react';

export default function CollectionsPage() {
  const [collections, setCollections] = useState<Collection[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [totalCollections, setTotalCollections] = useState(0);
  const pageSize = 10;
  const router = useRouter();

  useEffect(() => {
    loadCollections(page);
  }, [page]);

  const loadCollections = async (currentPage: number) => {
    try {
      setLoading(true);
      const response = await CollectionService.getCollections(currentPage, pageSize);
      setCollections(response.data);
      setTotalCollections(response.total);
    } catch (error) {
      console.error('Error loading collections:', error);
      toast.crud.fetchError('Collection');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this collection?')) return;

    try {
      await CollectionService.deleteCollection(id);
      toast.crud.deleted('Collection');
      loadCollections(page);
    } catch (error: any) {
      console.error('Error deleting collection:', error);
      toast.crud.deleteError('Collection', error?.message);
    }
  };

  const columns: ColumnDef<Collection>[] = [
    {
      accessorKey: 'name',
      header: 'Name',
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => (
        <Badge variant={row.original.status === 'PUBLISHED' ? 'default' : 'secondary'}>
          {row.original.status}
        </Badge>
      ),
    },
    {
      accessorKey: 'products',
      header: 'Products',
      cell: ({ row }) => row.original.products.length,
    },
    {
      accessorKey: 'createdAt',
      header: 'Created',
      cell: ({ row }) => format(new Date(row.original.createdAt), 'MMM d, yyyy'),
    },
    {
      id: 'actions',
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger>
            <MoreHorizontal className="h-4 w-4" />
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem onClick={() => router.push(`/collections/${row.original.id}`)}>
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleDelete(row.original.id)}>
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ];

  return (
    <div className="flex-1 space-y-4">
      <div className="flex items-center justify-between">
        <Heading title="Collections" description="Manage your collections" />
        <div className="flex items-center gap-4">
          <Button variant="outline" disabled>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button variant="outline" disabled>
            <Upload className="mr-2 h-4 w-4" />
            Import
          </Button>
          <Button onClick={() => router.push('/collections/new')}>
            <Plus className="mr-2 h-4 w-4" />
            Add New
          </Button>
        </div>
      </div>
      <Separator />

      <DataTable
        columns={columns}
        data={collections}
        loading={loading}
        searchKey="name"
        searchPlaceholder="Search collections..."
        pagination={{
          page,
          pageSize,
          total: totalCollections,
          onPageChange: (newPage) => setPage(newPage)
        }}
      />
    </div>
  );
}
