"use client";

import { useState } from "react";
import { Edit, MoreHorizontal, Trash } from "lucide-react";
import { toast } from "react-hot-toast";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { AlertModal } from "@/components/modals/alert-modal";
import { CompanyColumn } from "@/components/companies/columns";
import { CompanyDialog } from "@/components/companies/company-dialog";
import { useCompanies } from "@/hooks/use-companies";

interface CellActionProps {
  data: CompanyColumn;
}

export function CellAction({ data }: CellActionProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [editOpen, setEditOpen] = useState(false);
  const { deleteCompany, updateCompany } = useCompanies();

  const onDelete = async () => {
    try {
      setLoading(true);
      await deleteCompany(data.id);
    } catch (error) {
      toast.error("Something went wrong");
    } finally {
      setLoading(false);
      setOpen(false);
    }
  };

  return (
    <>
      <AlertModal
        isOpen={open}
        onClose={() => setOpen(false)}
        onConfirm={onDelete}
        loading={loading}
      />
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuItem onClick={() => setEditOpen(true)}>
            <Edit className="mr-2 h-4 w-4" /> Edit
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => setOpen(true)}>
            <Trash className="mr-2 h-4 w-4" /> Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <CompanyDialog
        open={editOpen}
        onClose={() => setEditOpen(false)}
        onSuccess={(data) => {
          // Use the current company's ID and update with new data
          updateCompany({
            id: data.id,    
            data: {
              name: data.name,
              email: data.email || undefined,
              phone: data.phone || undefined,
              address: data.address || undefined,
            }
          });
          setEditOpen(false);
        }}
        initialData={data}
      />
    </>
  );
}
