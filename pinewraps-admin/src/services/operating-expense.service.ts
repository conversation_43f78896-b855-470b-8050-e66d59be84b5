import { AxiosError } from 'axios';
import axios from 'axios';
import { auth } from '@/lib/firebase';

// Operating Expense interfaces
export interface ExpenseCategory {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
}

export interface OperatingExpense {
  id: string;
  name: string;
  amount: number;
  dueDate: string;
  categoryId: string;
  paid: boolean;
  createdAt: string;
  updatedAt: string;
}

// Create axios instance with auth token
const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to add auth token
apiClient.interceptors.request.use(async (config) => {
  const user = auth.currentUser;
  if (user) {
    const token = await user.getIdToken();
    config.headers.Authorization = `Bearer ${token}`;
    console.log('Setting auth token for operating expenses API request');
  } else {
    console.warn('No current user for auth token');
  }
  return config;
});

const BASE_ENDPOINT = '/api/operating-expenses';
const CATEGORIES_ENDPOINT = `${BASE_ENDPOINT}/categories`;

class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public data?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

export const operatingExpenseService = {
  // Categories
  async getCategories(): Promise<ExpenseCategory[]> {
    try {
      console.log('Fetching expense categories from service...');
      const response = await apiClient.get<ExpenseCategory[]>(CATEGORIES_ENDPOINT);
      console.log('Expense categories API response:', response);
      return response.data;
    } catch (error) {
      console.error('Error fetching expense categories:', error);
      if (error instanceof AxiosError) {
        throw new ApiError(
          error.response?.data?.message || 'Failed to fetch expense categories',
          error.response?.status,
          error.response?.data
        );
      }
      throw error;
    }
  },

  async getCategoryById(id: string): Promise<ExpenseCategory> {
    try {
      const response = await apiClient.get<ExpenseCategory>(`${CATEGORIES_ENDPOINT}/${id}`);
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        throw new ApiError(
          error.response?.data?.message || 'Failed to fetch expense category',
          error.response?.status,
          error.response?.data
        );
      }
      throw error;
    }
  },

  async createCategory(data: Omit<ExpenseCategory, 'id' | 'createdAt' | 'updatedAt'>): Promise<ExpenseCategory> {
    try {
      const response = await apiClient.post<ExpenseCategory>(CATEGORIES_ENDPOINT, data);
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        throw new ApiError(
          error.response?.data?.message || 'Failed to create expense category',
          error.response?.status,
          error.response?.data
        );
      }
      throw error;
    }
  },

  async updateCategory(id: string, data: Partial<ExpenseCategory>): Promise<ExpenseCategory> {
    try {
      const response = await apiClient.patch<ExpenseCategory>(`${CATEGORIES_ENDPOINT}/${id}`, data);
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        throw new ApiError(
          error.response?.data?.message || 'Failed to update expense category',
          error.response?.status,
          error.response?.data
        );
      }
      throw error;
    }
  },

  async deleteCategory(id: string): Promise<void> {
    try {
      await apiClient.delete(`${CATEGORIES_ENDPOINT}/${id}`);
    } catch (error) {
      if (error instanceof AxiosError) {
        throw new ApiError(
          error.response?.data?.message || 'Failed to delete expense category',
          error.response?.status,
          error.response?.data
        );
      }
      throw error;
    }
  },

  // Expenses
  async getExpenses(): Promise<OperatingExpense[]> {
    try {
      console.log('Fetching operating expenses from service...');
      const response = await apiClient.get<OperatingExpense[]>(BASE_ENDPOINT);
      console.log('Operating expenses API response:', response);
      return response.data;
    } catch (error) {
      console.error('Error fetching operating expenses:', error);
      if (error instanceof AxiosError) {
        throw new ApiError(
          error.response?.data?.message || 'Failed to fetch operating expenses',
          error.response?.status,
          error.response?.data
        );
      }
      throw error;
    }
  },

  async getExpenseById(id: string): Promise<OperatingExpense> {
    try {
      const response = await apiClient.get<OperatingExpense>(`${BASE_ENDPOINT}/${id}`);
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        throw new ApiError(
          error.response?.data?.message || 'Failed to fetch operating expense',
          error.response?.status,
          error.response?.data
        );
      }
      throw error;
    }
  },

  async createExpense(data: Omit<OperatingExpense, 'id' | 'createdAt' | 'updatedAt' | 'paid'>): Promise<OperatingExpense> {
    try {
      const response = await apiClient.post<OperatingExpense>(BASE_ENDPOINT, data);
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        throw new ApiError(
          error.response?.data?.message || 'Failed to create operating expense',
          error.response?.status,
          error.response?.data
        );
      }
      throw error;
    }
  },

  async updateExpense(id: string, data: Partial<OperatingExpense>): Promise<OperatingExpense> {
    try {
      const response = await apiClient.patch<OperatingExpense>(`${BASE_ENDPOINT}/${id}`, data);
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        throw new ApiError(
          error.response?.data?.message || 'Failed to update operating expense',
          error.response?.status,
          error.response?.data
        );
      }
      throw error;
    }
  },

  async deleteExpense(id: string): Promise<void> {
    try {
      await apiClient.delete(`${BASE_ENDPOINT}/${id}`);
    } catch (error) {
      if (error instanceof AxiosError) {
        throw new ApiError(
          error.response?.data?.message || 'Failed to delete operating expense',
          error.response?.status,
          error.response?.data
        );
      }
      throw error;
    }
  },

  async markAsPaid(id: string): Promise<OperatingExpense> {
    try {
      const response = await apiClient.patch<OperatingExpense>(`${BASE_ENDPOINT}/${id}/mark-paid`);
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        throw new ApiError(
          error.response?.data?.message || 'Failed to mark operating expense as paid',
          error.response?.status,
          error.response?.data
        );
      }
      throw error;
    }
  }
};
