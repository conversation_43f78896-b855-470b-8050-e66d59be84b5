import { prisma } from '../lib/prisma';
import { POSOrderStatus } from '@prisma/client';

export interface StaffPerformanceMetrics {
  staffId: string;
  staffName: string;
  department: 'kitchen' | 'design' | 'finalCheck' | 'cashier'; // Current role context
  departments: string[]; // All departments this staff works in
  ordersCompleted: number; // Orders completed in THIS role
  averageProcessingTime: number; // in minutes for THIS role
  ordersSentBack: number;
  qualityScore: number; // percentage
  productivityScore: number;
  totalWorkingTime: number; // in minutes
  ordersPerHour: number;
  isMultiRole: boolean; // Indicates if staff has multiple roles
}

export interface DepartmentMetrics {
  department: string;
  totalOrders: number;
  averageProcessingTime: number;
  totalStaff: number;
  efficiency: number; // percentage
  returnRate: number; // percentage
  staffPerformance: StaffPerformanceMetrics[];
}

export interface QualityMetrics {
  totalOrdersProcessed: number;
  ordersSentBack: number;
  returnRate: number;
  returnsByDepartment: {
    kitchen: number;
    design: number;
  };
  departmentOrderCounts: {
    kitchen: number;
    design: number;
  };
  commonIssues: Array<{
    issue: string;
    count: number;
    department: string;
  }>;
}

export interface StaffOrderDetail {
  orderId: string;
  orderNumber: string;
  customerName: string;
  customerPhone: string;
  orderTotal: number;
  role: 'kitchen' | 'design' | 'finalCheck' | 'cashier';
  startTime: Date | null;
  endTime: Date | null;
  processingTime: number | null; // in minutes
  status: string;
  items: Array<{
    id: string;
    productName: string;
    quantity: number;
    totalPrice: number;
    variations?: any;
    notes?: string;
    customImages?: any[];
  }>;
  notes: string | null;
  wasSentBack: boolean;
  complexity: 'low' | 'medium' | 'high';
  createdAt: Date;
  deliveryMethod: string;
  requiresKitchen: boolean;
  requiresDesign: boolean;
  requiresFinalCheck: boolean;
}

export interface StaffAdvancedAnalytics {
  dailyPerformance: Array<{
    date: string;
    ordersProcessed: number;
    totalTime: number;
    avgTimePerOrder: number;
    qualityScore: number;
    efficiency: number;
  }>;
  orderComplexityBreakdown: {
    low: { count: number; avgTime: number; qualityRate: number };
    medium: { count: number; avgTime: number; qualityRate: number };
    high: { count: number; avgTime: number; qualityRate: number };
  };
  performanceTrends: {
    speedTrend: 'improving' | 'declining' | 'stable';
    qualityTrend: 'improving' | 'declining' | 'stable';
    productivityTrend: 'improving' | 'declining' | 'stable';
    trendPercentage: number;
  };
  peakPerformanceHours: Array<{
    hour: number;
    avgProcessingTime: number;
    orderCount: number;
    efficiency: number;
  }>;
  qualityMetrics: {
    totalOrdersProcessed: number;
    ordersSentBack: number;
    qualityRate: number;
    commonIssues: Array<{
      issue: string;
      count: number;
      percentage: number;
    }>;
    improvementSuggestions: string[];
  };
  timeDistribution: {
    fastOrders: number; // < 15 minutes
    normalOrders: number; // 15-45 minutes
    slowOrders: number; // > 45 minutes
    averageByComplexity: {
      low: number;
      medium: number;
      high: number;
    };
  };
}

export interface IndividualStaffDetails {
  staffInfo: {
    id: string;
    name: string;
    email: string;
    department: string;
    departments: string[];
    primaryDepartment: string;
    role: string;
    joinDate: string;
    isActive: boolean;
    isMultiRole: boolean;
  };
  performanceMetrics: StaffPerformanceMetrics;
  orderHistory: Array<{
    id: string;
    orderNumber: string;
    status: string;
    createdAt: string;
    completedAt?: string;
    processingTime?: number;
    totalAmount: number;
    itemCount: number;
    customerName: string;
    wasSentBack: boolean;
    qualityNotes?: string;
    complexity: 'low' | 'medium' | 'high';
  }>;
  timeAnalytics: {
    dailyHours: Array<{
      date: string;
      hoursWorked: number;
      ordersCompleted: number;
      efficiency: number;
    }>;
    peakHours: Array<{
      hour: number;
      orderCount: number;
      avgProcessingTime: number;
    }>;
    weeklyTrends: Array<{
      week: string;
      productivity: number;
      qualityScore: number;
      ordersCompleted: number;
    }>;
  };
  qualityAnalytics: {
    qualityTrend: Array<{
      period: string;
      qualityScore: number;
      ordersSentBack: number;
    }>;
    commonIssues: Array<{
      issue: string;
      count: number;
      lastOccurrence: string;
    }>;
    improvementAreas: string[];
  };
  performanceComparison: {
    departmentRank: number;
    departmentTotal: number;
    percentile: number;
    comparedToAverage: {
      productivity: number;
      quality: number;
      speed: number;
    };
  };
}

export class StaffAnalyticsService {
  /**
   * Get default processing time for department when no timing data exists
   */
  private static getDefaultProcessingTime(department: string): number {
    const defaults = {
      kitchen: 15,      // 15 minutes average for kitchen tasks
      design: 20,       // 20 minutes average for design tasks
      finalCheck: 5,    // 5 minutes average for final check
      cashier: 2        // 2 minutes average for order creation
    };
    return defaults[department as keyof typeof defaults] || 10;
  }

  /**
   * Sanitize numeric values to prevent NaN, Infinity, or unreasonable values
   */
  private static sanitizeNumber(value: number): number {
    if (!isFinite(value) || isNaN(value) || value < 0) {
      return 0;
    }
    // Round to 2 decimal places
    return Math.round(value * 100) / 100;
  }

  /**
   * Sanitize percentage values (0-100)
   */
  private static sanitizePercentage(value: number): number {
    if (!isFinite(value) || isNaN(value)) {
      return 0;
    }
    // Clamp between 0 and 100, round to 2 decimal places
    return Math.round(Math.max(0, Math.min(100, value)) * 100) / 100;
  }

  /**
   * Helper function to determine all departments a staff member works in
   */
  private static getStaffDepartments(staff: any): string[] {
    const departments: string[] = [];
    if (staff.isKitchenStaff) departments.push('kitchen');
    if (staff.isDesignStaff) departments.push('design');
    if (staff.isFinalCheckStaff) departments.push('finalCheck');
    if (staff.isCashierStaff) departments.push('cashier');
    return departments;
  }

  /**
   * Helper function to get primary department for a staff member
   */
  private static getPrimaryDepartment(staff: any): string {
    // Priority order: finalCheck > kitchen > design > cashier
    if (staff.isFinalCheckStaff) return 'finalCheck';
    if (staff.isKitchenStaff) return 'kitchen';
    if (staff.isDesignStaff) return 'design';
    if (staff.isCashierStaff) return 'cashier';
    return 'cashier'; // Default fallback
  }

  /**
   * Calculate order complexity based on various factors
   */
  private static calculateOrderComplexity(order: any): 'low' | 'medium' | 'high' {
    let score = 0;

    // Item count factor
    score += order.items?.length * 2 || 0;

    // Custom variations and complexity factors
    if (order.items) {
      order.items.forEach((item: any) => {
        if (item.variations && Object.keys(item.variations).length > 0) {
          score += 3;
        }
        if (item.notes) score += 2;
        if (item.customImages?.length > 0) score += 5;
      });
    }

    // Processing requirements
    if (order.requiresKitchen && order.requiresDesign) score += 10;
    else if (order.requiresKitchen || order.requiresDesign) score += 5;

    // Order value factor
    if (order.total > 500) score += 5;
    else if (order.total > 200) score += 3;

    // Special notes or instructions
    if (order.designNotes || order.kitchenNotes || order.finalCheckNotes) score += 3;

    if (score <= 10) return 'low';
    if (score <= 25) return 'medium';
    return 'high';
  }

  /**
   * Optimized method to get all staff performance metrics in a single query
   */
  static async getOptimizedStaffPerformance(
    department?: string,
    startDate?: Date,
    endDate?: Date,
    showAllRoles: boolean = false
  ): Promise<StaffPerformanceMetrics[]> {
    console.log('DEBUG: Starting optimized staff performance calculation');

    // Build date filter with status exclusion
    const dateFilter: any = {
      status: { notIn: ['CANCELLED', 'REFUNDED'] }
    };
    if (startDate && endDate) {
      dateFilter.createdAt = {
        gte: startDate,
        lte: endDate,
      };
    }

    // Get all relevant orders with minimal data in a single query
    const orders = await prisma.pOSOrder.findMany({
      where: dateFilter,
      select: {
        id: true,
        orderNumber: true,
        status: true,
        createdAt: true,
        isSentBack: true,
        kitchenById: true,
        kitchenStartTime: true,
        kitchenEndTime: true,
        designById: true,
        designStartTime: true,
        designEndTime: true,
        finalCheckById: true,
        finalCheckStartTime: true,
        finalCheckEndTime: true,
        createdById: true,
        items: {
          select: {
            quantity: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 2000 // Reasonable limit for performance
    });

    // Get all staff members in a single query with proper filtering
    const staffQuery: any = {
      where: {
        isDeleted: false,
        isActive: true,
        role: { not: 'SUPER_ADMIN' },
        OR: [
          { isKitchenStaff: true },
          { isDesignStaff: true },
          { isFinalCheckStaff: true },
          { isCashierStaff: true }
        ]
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        role: true,
        isKitchenStaff: true,
        isDesignStaff: true,
        isFinalCheckStaff: true,
        isCashierStaff: true
      }
    };

    const allStaff = await prisma.user.findMany(staffQuery);
    console.log(`DEBUG: Found ${allStaff.length} staff members and ${orders.length} orders`);

    // Debug: Check department-specific staff
    if (department === 'finalCheck') {
      const finalCheckStaff = allStaff.filter(s => s.isFinalCheckStaff);
      console.log(`DEBUG: Final check staff found: ${finalCheckStaff.length}`);
      finalCheckStaff.forEach(staff => {
        console.log(`DEBUG: Final check staff: ${staff.firstName} ${staff.lastName} (${staff.id})`);
      });

      const ordersWithFinalCheck = orders.filter(o => o.finalCheckById);
      console.log(`DEBUG: Orders with final check assignment: ${ordersWithFinalCheck.length}`);
    }

    if (department === 'cashier') {
      const cashierStaff = allStaff.filter(s => s.isCashierStaff);
      console.log(`DEBUG: Cashier staff found: ${cashierStaff.length}`);
      cashierStaff.forEach(staff => {
        console.log(`DEBUG: Cashier staff: ${staff.firstName} ${staff.lastName} (${staff.id}) - Multi-role: ${this.getStaffDepartments(staff).length > 1 ? 'Yes' : 'No'}`);
      });

      const ordersCreatedByCashiers = orders.filter(o => o.createdById && cashierStaff.some(s => s.id === o.createdById));
      console.log(`DEBUG: Orders created by cashiers: ${ordersCreatedByCashiers.length}`);

      // Check for orders by each cashier individually
      cashierStaff.forEach(staff => {
        const staffOrders = orders.filter(o => o.createdById === staff.id);
        console.log(`DEBUG: ${staff.firstName} ${staff.lastName} created ${staffOrders.length} orders`);
      });

      // Sample some orders for debugging
      if (ordersCreatedByCashiers.length > 0) {
        console.log(`DEBUG: Sample cashier orders:`, ordersCreatedByCashiers.slice(0, 3).map(o => ({
          orderNumber: o.orderNumber,
          createdById: o.createdById,
          status: o.status,
          createdAt: o.createdAt
        })));
      }
    }

    // Process all staff performance in memory for better performance
    const performanceMetrics: StaffPerformanceMetrics[] = [];

    for (const staffMember of allStaff) {
      const staffDepartments = this.getStaffDepartments(staffMember);

      if (department && department !== 'all') {
        // When filtering by specific department, only show staff who work in that department
        // and show their metrics for that specific department only
        if (staffDepartments.includes(department)) {
          const metrics = this.calculateStaffMetricsFromOrders(
            staffMember,
            department,
            orders
          );

          // Always include staff who work in the requested department, even if they have 0 orders
          // This ensures multi-role staff appear in the correct department lists
          performanceMetrics.push(metrics);
        }
      } else {
        // When not filtering by department, behavior depends on showAllRoles parameter
        if (showAllRoles) {
          // Show staff in ALL their departments (for comprehensive staff performance view)
          const staffDepartments = this.getStaffDepartments(staffMember);
          for (const dept of staffDepartments) {
            const metrics = this.calculateStaffMetricsFromOrders(
              staffMember,
              dept,
              orders
            );
            // Include all roles, even with 0 orders, to show complete staff roster
            performanceMetrics.push(metrics);
          }
        } else {
          // Show staff in their primary department only (for dashboard overview)
          const primaryDepartment = this.getPrimaryDepartment(staffMember);
          const metrics = this.calculateStaffMetricsFromOrders(
            staffMember,
            primaryDepartment,
            orders
          );

          if (metrics.ordersCompleted > 0) {
            performanceMetrics.push(metrics);
          }
        }
      }
    }

    console.log(`DEBUG: Calculated metrics for ${performanceMetrics.length} staff-department combinations`);
    return performanceMetrics;
  }

  /**
   * Calculate optimized staff metrics using department-specific queries
   */
  private static async calculateOptimizedStaffMetrics(
    staff: any,
    department: string,
    baseFilter: any
  ): Promise<StaffPerformanceMetrics> {
    const staffName = `${staff.firstName} ${staff.lastName}`;
    console.log(`DEBUG: Calculating optimized metrics for ${staffName} in ${department}`);

    // Build department-specific query
    const whereClause = {
      ...baseFilter,
    };

    // Add department-specific staff filter
    switch (department) {
      case 'kitchen':
        whereClause.kitchenById = staff.id;
        break;
      case 'design':
        whereClause.designById = staff.id;
        break;
      case 'finalCheck':
        whereClause.finalCheckById = staff.id;
        break;
      case 'cashier':
        whereClause.createdById = staff.id;
        break;
    }

    // Get orders count and basic metrics in a single query
    const [orderCount, orders] = await Promise.all([
      prisma.pOSOrder.count({ where: whereClause }),
      prisma.pOSOrder.findMany({
        where: whereClause,
        select: {
          id: true,
          orderNumber: true,
          status: true,
          createdAt: true,
          isSentBack: true,
          kitchenStartTime: true,
          kitchenEndTime: true,
          designStartTime: true,
          designEndTime: true,
          finalCheckStartTime: true,
          finalCheckEndTime: true,
          items: {
            select: { quantity: true }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 500 // Limit for performance
      })
    ]);

    console.log(`DEBUG: ${staffName} (${department}): Found ${orderCount} total orders, fetched ${orders.length} for analysis`);

    // Calculate metrics from the fetched orders
    return this.calculateStaffMetricsFromOrders(staff, department, orders);
  }

  /**
   * Calculate staff metrics from pre-loaded orders (optimized for bulk processing)
   */
  private static calculateStaffMetricsFromOrders(
    staff: any,
    department: string,
    orders: any[]
  ): StaffPerformanceMetrics {
    const staffName = `${staff.firstName} ${staff.lastName}`;
    let ordersProcessed = 0;
    let ordersSentBack = 0;
    const processingTimes: number[] = [];

    // Process orders for this staff member in this department
    for (const order of orders) {
      let wasProcessedByStaff = false;
      let startTime: Date | null = null;
      let endTime: Date | null = null;

      // Check if staff member processed this order in the specified department
      switch (department) {
        case 'kitchen':
          if (order.kitchenById === staff.id) {
            wasProcessedByStaff = true;
            startTime = order.kitchenStartTime;
            endTime = order.kitchenEndTime;
          }
          break;
        case 'design':
          if (order.designById === staff.id) {
            wasProcessedByStaff = true;
            startTime = order.designStartTime;
            endTime = order.designEndTime;
          }
          break;
        case 'finalCheck':
          if (order.finalCheckById === staff.id) {
            wasProcessedByStaff = true;
            startTime = order.finalCheckStartTime;
            endTime = order.finalCheckEndTime;
          }
          break;
        case 'cashier':
          if (order.createdById === staff.id) {
            wasProcessedByStaff = true;
            // For cashier, we estimate processing time based on order complexity
            const itemCount = order.items?.reduce((sum: number, item: any) => sum + item.quantity, 0) || 1;
            const estimatedTime = Math.max(2, Math.min(15, itemCount * 1.5)); // 2-15 minutes based on items
            startTime = order.createdAt;
            endTime = new Date(order.createdAt.getTime() + (estimatedTime * 60000));
          }
          break;
      }

      if (wasProcessedByStaff) {
        ordersProcessed++;

        // Calculate processing time
        if (startTime && endTime) {
          const processingTime = (endTime.getTime() - startTime.getTime()) / (1000 * 60);
          if (processingTime >= 1 && processingTime <= 1440) {
            processingTimes.push(processingTime);
          } else if (processingTime > 0 && processingTime < 1) {
            processingTimes.push(1);
          }
        } else {
          const defaultTime = this.getDefaultProcessingTime(department);
          if (defaultTime > 0) {
            processingTimes.push(defaultTime);
          }
        }

        if (order.isSentBack) {
          ordersSentBack++;
        }
      }
    }

    // Calculate metrics
    const averageProcessingTime = processingTimes.length > 0
      ? processingTimes.reduce((sum, time) => sum + time, 0) / processingTimes.length
      : (ordersProcessed > 0 ? this.getDefaultProcessingTime(department) : 0);

    const qualityScore = ordersProcessed > 0
      ? ((ordersProcessed - ordersSentBack) / ordersProcessed) * 100
      : 100;

    const totalWorkingTime = processingTimes.reduce((sum, time) => sum + time, 0);
    const ordersPerHour = totalWorkingTime > 0
      ? (ordersProcessed / (totalWorkingTime / 60))
      : (ordersProcessed > 0 ? ordersProcessed / (averageProcessingTime / 60) : 0);

    const productivityScore = this.calculateProductivityScore(ordersProcessed, averageProcessingTime, qualityScore);
    const allDepartments = this.getStaffDepartments(staff);
    const isMultiRole = allDepartments.length > 1;

    // Validate and sanitize all metrics
    const sanitizedMetrics = {
      staffId: staff.id,
      staffName,
      department: department as any,
      departments: allDepartments,
      ordersCompleted: Math.max(0, ordersProcessed),
      averageProcessingTime: this.sanitizeNumber(averageProcessingTime),
      ordersSentBack: Math.max(0, ordersSentBack),
      qualityScore: this.sanitizePercentage(qualityScore),
      productivityScore: this.sanitizePercentage(productivityScore),
      totalWorkingTime: this.sanitizeNumber(totalWorkingTime),
      ordersPerHour: this.sanitizeNumber(ordersPerHour),
      isMultiRole,
    };

    // Log any unusual values for debugging
    if (sanitizedMetrics.averageProcessingTime === 0 && sanitizedMetrics.ordersCompleted > 0) {
      console.warn(`Staff ${staffName} has ${sanitizedMetrics.ordersCompleted} orders but 0 average time`);
    }

    return sanitizedMetrics;
  }

  /**
   * Get individual staff performance metrics
   */
  static async getStaffPerformance(
    staffId?: string,
    department?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<StaffPerformanceMetrics[]> {
    console.log('getStaffPerformance called with:', { staffId, department, startDate, endDate });

    const whereClause: any = {};

    if (startDate && endDate) {
      whereClause.createdAt = {
        gte: startDate,
        lte: endDate,
      };
    }

    // Get staff members with proper filtering
    const staffQuery: any = {
      where: {
        isActive: true,
        isDeleted: false,
        // Only include users who have at least one staff flag set to true
        OR: [
          { isKitchenStaff: true },
          { isDesignStaff: true },
          { isFinalCheckStaff: true },
          { isCashierStaff: true }
        ]
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        role: true,
        isKitchenStaff: true,
        isDesignStaff: true,
        isFinalCheckStaff: true,
        isCashierStaff: true,
      },
    };

    // Apply department filter if specified (for multi-role staff)
    if (department && department !== 'all') {
      console.log(`DEBUG: Filtering by department: ${department}`);

      // For department filtering, we want staff who work in that department
      // but we'll calculate metrics only for that specific role
      switch (department) {
        case 'kitchen':
          staffQuery.where.isKitchenStaff = true;
          break;
        case 'design':
          staffQuery.where.isDesignStaff = true;
          break;
        case 'finalCheck':
          staffQuery.where.isFinalCheckStaff = true;
          break;
        case 'cashier':
          staffQuery.where.isCashierStaff = true;
          break;
      }
    }

    if (staffId) {
      staffQuery.where.id = staffId;
    }

    console.log('DEBUG: Final staff query:', JSON.stringify(staffQuery, null, 2));

    const staff = await prisma.user.findMany(staffQuery);
    console.log('DEBUG: Found staff members:', staff.length);
    console.log('DEBUG: Staff details:', staff.map(s => ({
      id: s.id,
      name: `${s.firstName} ${s.lastName}`,
      role: s.role,
      kitchen: s.isKitchenStaff,
      design: s.isDesignStaff,
      finalCheck: s.isFinalCheckStaff,
      cashier: s.isCashierStaff
    })));
    console.log('DEBUG: Staff query used:', JSON.stringify(staffQuery, null, 2));

    // Debug: Check if we have any final check staff
    if (department === 'finalCheck') {
      const finalCheckStaffCount = await prisma.user.count({
        where: { isFinalCheckStaff: true, isDeleted: false }
      });
      console.log(`DEBUG: Found ${finalCheckStaffCount} final check staff members in database`);

      const finalCheckStaffList = await prisma.user.findMany({
        where: { isFinalCheckStaff: true, isDeleted: false },
        select: { id: true, firstName: true, lastName: true, isFinalCheckStaff: true }
      });
      console.log('DEBUG: Final check staff list:', finalCheckStaffList);
    }

    const performanceMetrics: StaffPerformanceMetrics[] = [];

    for (const staffMember of staff) {
      try {
        // Get all departments this staff member works in
        const staffDepartments = this.getStaffDepartments(staffMember);

        // If filtering by department, only process if staff works in that department
        if (department && department !== 'all') {
          if (!staffDepartments.includes(department)) {
            continue;
          }

          // Calculate metrics for the specific role/department
          const metrics = await this.calculateStaffMetrics(staffMember, department, whereClause);
          performanceMetrics.push(metrics);
        } else {
          // If no department filter, include staff in their primary department to avoid duplicates
          const primaryDepartment = this.getPrimaryDepartment(staffMember);
          const metrics = await this.calculateStaffMetrics(staffMember, primaryDepartment, whereClause);
          performanceMetrics.push(metrics);
        }
      } catch (error) {
        console.error(`Error calculating metrics for staff ${staffMember.firstName} ${staffMember.lastName}:`, error);
        // Continue with other staff members
      }
    }

    return performanceMetrics.sort((a, b) => b.productivityScore - a.productivityScore);
  }

  /**
   * Calculate metrics for individual staff member
   */
  private static async calculateStaffMetrics(
    staff: any,
    department?: string,
    whereClause?: any
  ): Promise<StaffPerformanceMetrics> {
    const staffName = `${staff.firstName} ${staff.lastName}`;
    
    // Determine primary department - prioritize based on specialization
    let staffDepartment: 'kitchen' | 'design' | 'finalCheck' | 'cashier' = 'cashier';

    // If department is specified in the query, use that for filtering
    if (department && department !== 'all') {
      staffDepartment = department as 'kitchen' | 'design' | 'finalCheck' | 'cashier';
    } else {
      // Auto-detect primary department based on staff flags
      // Priority: finalCheck > design > kitchen > cashier (most specialized first)
      if (staff.isFinalCheckStaff) staffDepartment = 'finalCheck';
      else if (staff.isDesignStaff) staffDepartment = 'design';
      else if (staff.isKitchenStaff) staffDepartment = 'kitchen';
      else if (staff.isCashierStaff) staffDepartment = 'cashier';
    }

    console.log(`Staff ${staffName} assigned to department: ${staffDepartment}`);

    // Get orders based on department with correct field mapping
    let ordersQuery: any = {
      where: {
        ...whereClause,
        // Exclude cancelled orders
        status: {
          notIn: ['CANCELLED', 'REFUNDED']
        }
      }
    };

    // Apply staff-specific filters - count ALL orders the staff member worked on
    // For the specific department context, we'll filter the processing later
    ordersQuery.where.OR = [
      { createdById: staff.id }, // Orders they created (cashier role)
      { kitchenById: staff.id }, // Orders they worked on in kitchen
      { designById: staff.id }, // Orders they worked on in design
      { finalCheckById: staff.id }, // Orders they worked on in final check
    ];

    console.log(`DEBUG: Using OR query to find ALL orders for staff ${staff.id} in any role`);

    console.log(`DEBUG: Querying orders for ${staffName} in ${staffDepartment} department`);
    console.log('DEBUG: Order query:', JSON.stringify(ordersQuery, null, 2));

    // Optimized query with minimal includes and pagination for performance
    const orders = await prisma.pOSOrder.findMany({
      ...ordersQuery,
      select: {
        id: true,
        orderNumber: true,
        status: true,
        createdAt: true,
        isSentBack: true,
        // Department-specific timing fields
        kitchenById: true,
        kitchenStartTime: true,
        kitchenEndTime: true,
        designById: true,
        designStartTime: true,
        designEndTime: true,
        finalCheckById: true,
        finalCheckStartTime: true,
        finalCheckEndTime: true,
        createdById: true,
        // Only include items count for complexity calculation
        items: {
          select: {
            id: true,
            quantity: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 1000 // Limit to last 1000 orders for performance
    });

    console.log(`DEBUG: Found ${orders.length} orders for ${staffName}`);
    if (orders.length > 0) {
      console.log(`DEBUG: Sample order for ${staffName}:`, {
        id: orders[0].id,
        orderNumber: orders[0].orderNumber,
        status: orders[0].status,
        kitchenById: orders[0].kitchenById,
        designById: orders[0].designById,
        finalCheckById: orders[0].finalCheckById,
        createdById: orders[0].createdById,
        requiresKitchen: orders[0].requiresKitchen,
        requiresDesign: orders[0].requiresDesign,
        requiresFinalCheck: orders[0].requiresFinalCheck
      });
    }

    // Debug: For final check department, check if any orders require final check
    if (staffDepartment === 'finalCheck') {
      // Get all orders that are in Final Check queue, processing, or completed
      const finalCheckStatuses = ['FINAL_CHECK_QUEUE', 'FINAL_CHECK_PROCESSING', 'COMPLETED'];
      const ordersInFinalCheck = orders.filter(o => {
        return finalCheckStatuses.includes(o.status.toString());
      });
      
      const ordersRequiringFinalCheck = orders.filter(o => o.requiresFinalCheck);
      const ordersAssignedToFinalCheck = orders.filter(o => o.finalCheckById);
      
      console.log(`DEBUG: Orders in final check statuses: ${ordersInFinalCheck.length}`);
      console.log(`DEBUG: Orders requiring final check: ${ordersRequiringFinalCheck.length}`);
      console.log(`DEBUG: Orders assigned to final check staff: ${ordersAssignedToFinalCheck.length}`);
      console.log(`DEBUG: Orders assigned to this final check staff (${staff.id}): ${orders.filter(o => o.finalCheckById === staff.id).length}`);
      
      // Add additional logging to debug status distribution
      const statusCounts = {};
      orders.forEach(o => {
        const status = o.status.toString();
        statusCounts[status] = (statusCounts[status] || 0) + 1;
      });
      console.log('DEBUG: Order status distribution:', statusCounts);
    }

    // Calculate processing times based on workflow understanding
    const processingTimes: number[] = [];
    let ordersSentBack = 0;
    let ordersProcessed = 0;

    for (const order of orders) {
      let startTime: Date | null = null;
      let endTime: Date | null = null;
      let wasProcessedByStaff = false;

      // Check if this staff member worked on this order in the requested department context
      switch (staffDepartment) {
        case 'kitchen':
          // Kitchen staff: count orders they worked on in kitchen role
          if (order.kitchenById === staff.id) {
            wasProcessedByStaff = true;
            startTime = order.kitchenStartTime;
            endTime = order.kitchenEndTime;
          }
          break;

        case 'design':
          // Design staff: count orders they worked on in design role
          if (order.designById === staff.id) {
            wasProcessedByStaff = true;
            startTime = order.designStartTime;
            endTime = order.designEndTime;
          }
          break;

        case 'finalCheck':
          // Final check staff: count orders they worked on in final check role
          // Count orders they worked on in final check role OR orders that are in final check statuses
          const finalCheckStatuses = ['FINAL_CHECK_QUEUE', 'FINAL_CHECK_PROCESSING', 'COMPLETED'];
          const isInFinalCheckStatus = finalCheckStatuses.includes(order.status.toString());
          
          if (order.finalCheckById === staff.id || (isInFinalCheckStatus && staff.isFinalCheckStaff)) {
            wasProcessedByStaff = true;
            startTime = order.finalCheckStartTime || order.createdAt; // Fallback to created time if no start time
            endTime = order.finalCheckEndTime;
            
            // If no end time but status is COMPLETED, use current time as end time
            if (!endTime && order.status.toString() === 'COMPLETED') {
              endTime = new Date();
            }
            
            console.log(`DEBUG: Final check order found for ${staffName}: ${order.orderNumber}, status: ${order.status}, start: ${startTime}, end: ${endTime}`);
          }
          break;

        case 'cashier':
          // Cashier: count orders they created
          if (order.createdById === staff.id) {
            wasProcessedByStaff = true;
            startTime = order.createdAt;
            const statusHistory = (order as any).statusHistory;
            if (statusHistory && statusHistory.length > 0) {
              endTime = statusHistory[0].createdAt;
            } else {
              // Default processing time for order creation
              endTime = new Date(order.createdAt.getTime() + 120000); // 2 minutes default
            }
          }
          break;
      }

      // Count as processed if the staff member worked on it
      if (wasProcessedByStaff) {
        ordersProcessed++;

        // Calculate processing time if we have both start and end times
        if (startTime && endTime) {
          const processingTime = (endTime.getTime() - startTime.getTime()) / (1000 * 60); // minutes
          // Only include reasonable processing times (between 1 minute and 24 hours)
          if (processingTime >= 1 && processingTime <= 1440) {
            processingTimes.push(processingTime);
          } else if (processingTime > 0 && processingTime < 1) {
            // For very short times, use minimum of 1 minute
            processingTimes.push(1);
          }
        } else {
          // If no timing data, use department-specific default estimates
          const defaultTime = this.getDefaultProcessingTime(staffDepartment);
          if (defaultTime > 0) {
            processingTimes.push(defaultTime);
          }
        }

        console.log(`DEBUG: Order ${order.orderNumber} counted for ${staffName} in ${staffDepartment} role`);
      } else {
        console.log(`DEBUG: Order ${order.orderNumber} NOT counted for ${staffName} in ${staffDepartment} role - no assignment match`);
      }

      // Check if order was sent back (quality issue)
      if (order.isSentBack) {
        ordersSentBack++;
      }
    }

    // Calculate average processing time with better handling of edge cases
    const averageProcessingTime = processingTimes.length > 0
      ? processingTimes.reduce((sum, time) => sum + time, 0) / processingTimes.length
      : (ordersProcessed > 0 ? this.getDefaultProcessingTime(staffDepartment) : 0);

    // Calculate quality score
    const qualityScore = ordersProcessed > 0
      ? ((ordersProcessed - ordersSentBack) / ordersProcessed) * 100
      : 100;

    // Calculate productivity metrics with better handling
    const totalWorkingTime = processingTimes.reduce((sum, time) => sum + time, 0);
    const ordersPerHour = totalWorkingTime > 0
      ? (ordersProcessed / (totalWorkingTime / 60))
      : (ordersProcessed > 0 ? ordersProcessed / (averageProcessingTime / 60) : 0);

    const productivityScore = this.calculateProductivityScore(ordersProcessed, averageProcessingTime, qualityScore);

    console.log(`Staff ${staffName} (${staffDepartment}): ${ordersProcessed} orders processed, ${processingTimes.length} with timing data`);

    // Get all departments this staff member works in
    const allDepartments = this.getStaffDepartments(staff);
    const isMultiRole = allDepartments.length > 1;

    // Validate and sanitize all metrics for consistency
    const sanitizedMetrics = {
      staffId: staff.id,
      staffName,
      department: staffDepartment, // Current role context
      departments: allDepartments, // All departments this staff works in
      ordersCompleted: Math.max(0, ordersProcessed), // Orders processed in THIS role
      averageProcessingTime: this.sanitizeNumber(averageProcessingTime),
      ordersSentBack: Math.max(0, ordersSentBack),
      qualityScore: this.sanitizePercentage(qualityScore),
      productivityScore: this.sanitizePercentage(productivityScore),
      totalWorkingTime: this.sanitizeNumber(totalWorkingTime),
      ordersPerHour: this.sanitizeNumber(ordersPerHour),
      isMultiRole, // Flag indicating if staff has multiple roles
    };

    // Debug logging for unusual values
    if (sanitizedMetrics.averageProcessingTime === 0 && sanitizedMetrics.ordersCompleted > 0) {
      console.warn(`Staff ${staffName} (${staffDepartment}) has ${sanitizedMetrics.ordersCompleted} orders but 0 average time`);
    }

    return sanitizedMetrics;
  }

  /**
   * Calculate productivity score based on multiple factors
   */
  private static calculateProductivityScore(
    ordersCompleted: number,
    averageProcessingTime: number,
    qualityScore: number
  ): number {
    // Normalize metrics (this can be adjusted based on business requirements)
    const orderWeight = 0.4;
    const timeWeight = 0.3;
    const qualityWeight = 0.3;

    // Normalize orders completed (assuming 50 orders per period is excellent)
    const normalizedOrders = Math.min(ordersCompleted / 50, 1) * 100;

    // Normalize processing time (assuming 30 minutes is excellent, 60+ is poor)
    const normalizedTime = averageProcessingTime > 0 
      ? Math.max(0, (60 - averageProcessingTime) / 60) * 100 
      : 0;

    return (normalizedOrders * orderWeight) + (normalizedTime * timeWeight) + (qualityScore * qualityWeight);
  }

  /**
   * Get department performance metrics (handles multi-role staff properly)
   */
  static async getDepartmentMetrics(
    startDate?: Date,
    endDate?: Date
  ): Promise<DepartmentMetrics[]> {
    const departments = ['kitchen', 'design', 'finalCheck', 'cashier'];
    const metrics: DepartmentMetrics[] = [];

    for (const dept of departments) {
      const staffPerformance = await this.getStaffPerformance(undefined, dept, startDate, endDate);

      const totalOrders = staffPerformance.reduce((sum, staff) => sum + staff.ordersCompleted, 0);
      const averageProcessingTime = staffPerformance.length > 0
        ? staffPerformance.reduce((sum, staff) => sum + staff.averageProcessingTime, 0) / staffPerformance.length
        : 0;

      // Count unique staff members (avoid double counting multi-role staff)
      const uniqueStaffIds = new Set(staffPerformance.map(staff => staff.staffId));
      const totalStaff = uniqueStaffIds.size;

      const efficiency = staffPerformance.length > 0
        ? staffPerformance.reduce((sum, staff) => sum + staff.productivityScore, 0) / staffPerformance.length
        : 0;

      const returnRate = totalOrders > 0
        ? (staffPerformance.reduce((sum, staff) => sum + staff.ordersSentBack, 0) / totalOrders) * 100
        : 0;

      metrics.push({
        department: dept,
        totalOrders,
        averageProcessingTime: Math.round(averageProcessingTime * 100) / 100,
        totalStaff,
        efficiency: Math.round(efficiency * 100) / 100,
        returnRate: Math.round(returnRate * 100) / 100,
        staffPerformance,
      });
    }

    return metrics;
  }

  /**
   * Get quality control metrics
   */
  static async getQualityMetrics(
    startDate?: Date,
    endDate?: Date
  ): Promise<QualityMetrics> {
    const whereClause: any = {
      status: { notIn: ['CANCELLED', 'REFUNDED'] }
    };

    if (startDate && endDate) {
      whereClause.createdAt = {
        gte: startDate,
        lte: endDate,
      };
    }

    const orders = await prisma.pOSOrder.findMany({
      where: whereClause,
      include: {
        statusHistory: {
          orderBy: { createdAt: 'asc' }
        }
      }
    });

    const totalOrdersProcessed = orders.length;
    const ordersSentBack = orders.filter(order => order.isSentBack).length;
    const returnRate = totalOrdersProcessed > 0 ? (ordersSentBack / totalOrdersProcessed) * 100 : 0;

    console.log(`DEBUG Quality Metrics: ${totalOrdersProcessed} total orders, ${ordersSentBack} sent back, ${returnRate.toFixed(2)}% return rate`);

    // Calculate returns by department based on status history
    // Final check is the one that sends orders back, so we track which department the order was sent back FROM
    const returnsByDepartment = {
      kitchen: 0,
      design: 0,
    };

    // Also calculate total orders processed by each department for accurate return rates
    const departmentOrderCounts = {
      kitchen: 0,
      design: 0,
    };

    // Count orders processed by each department
    for (const order of orders) {
      if (order.requiresKitchen) {
        departmentOrderCounts.kitchen++;
      }
      if (order.requiresDesign) {
        departmentOrderCounts.design++;
      }
    }

    // Analyze sent back orders to determine which department they were returned from
    const sentBackOrders = orders.filter(order => order.isSentBack);

    for (const order of sentBackOrders) {
      // Look at status history to determine where the order was sent back from
      const statusHistory = order.statusHistory || [];

      // Find the last department status before being sent back
      let lastDepartment = null;
      for (let i = statusHistory.length - 1; i >= 0; i--) {
        const status = statusHistory[i].status;
        if (status === 'KITCHEN_PROCESSING' || status === 'KITCHEN_READY') {
          lastDepartment = 'kitchen';
          break;
        } else if (status === 'DESIGN_PROCESSING' || status === 'DESIGN_READY') {
          lastDepartment = 'design';
          break;
        }
      }

      // If we can't determine from status history, check the order requirements
      if (!lastDepartment) {
        if (order.requiresDesign) {
          lastDepartment = 'design'; // Most likely sent back from design
        } else if (order.requiresKitchen) {
          lastDepartment = 'kitchen'; // Most likely sent back from kitchen
        }
      }

      if (lastDepartment && returnsByDepartment[lastDepartment] !== undefined) {
        returnsByDepartment[lastDepartment]++;
      }
    }

    console.log(`DEBUG Returns by department:`, returnsByDepartment);
    console.log(`DEBUG Orders by department:`, departmentOrderCounts);
    console.log(`DEBUG Kitchen return rate: ${departmentOrderCounts.kitchen > 0 ? (returnsByDepartment.kitchen / departmentOrderCounts.kitchen * 100).toFixed(2) : 0}%`);
    console.log(`DEBUG Design return rate: ${departmentOrderCounts.design > 0 ? (returnsByDepartment.design / departmentOrderCounts.design * 100).toFixed(2) : 0}%`);

    // Get common issues from status history notes
    const commonIssues = await this.getCommonIssues(startDate, endDate);

    return {
      totalOrdersProcessed,
      ordersSentBack,
      returnRate: Math.round(returnRate * 100) / 100,
      returnsByDepartment,
      departmentOrderCounts,
      commonIssues,
    };
  }

  /**
   * Extract common issues from order notes
   */
  private static async getCommonIssues(startDate?: Date, endDate?: Date) {
    const whereClause: any = {};
    
    if (startDate && endDate) {
      whereClause.createdAt = {
        gte: startDate,
        lte: endDate,
      };
    }

    const statusHistory = await prisma.pOSOrderStatusHistory.findMany({
      where: {
        ...whereClause,
        notes: {
          not: null,
        },
      },
      select: {
        notes: true,
        status: true,
      },
    });

    // Simple keyword extraction (can be enhanced with NLP)
    const issueKeywords = [
      'wrong', 'incorrect', 'missing', 'damaged', 'quality', 'redo', 'fix',
      'error', 'mistake', 'problem', 'issue', 'defect', 'poor'
    ];

    const issues: { [key: string]: { count: number; department: string } } = {};

    statusHistory.forEach(entry => {
      if (entry.notes) {
        const notes = entry.notes.toLowerCase();
        issueKeywords.forEach(keyword => {
          if (notes.includes(keyword)) {
            const department = this.getDepartmentFromStatus(entry.status);
            const key = keyword;
            if (!issues[key]) {
              issues[key] = { count: 0, department };
            }
            issues[key].count++;
          }
        });
      }
    });

    return Object.entries(issues)
      .map(([issue, data]) => ({
        issue,
        count: data.count,
        department: data.department,
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10); // Top 10 issues
  }

  /**
   * Determine department from order status
   */
  private static getDepartmentFromStatus(status: POSOrderStatus): string {
    const statusStr = status.toString();
    if (statusStr.includes('KITCHEN')) return 'kitchen';
    if (statusStr.includes('DESIGN')) return 'design';
    if (statusStr.includes('FINAL_CHECK')) return 'finalCheck';
    // Also count COMPLETED orders as finalCheck if they've gone through final check
    if (statusStr === 'COMPLETED') return 'finalCheck';
    return 'general';
  }

  /**
   * Aggregate metrics across multiple departments for multi-role staff
   */
  private static aggregateMultiDepartmentMetrics(
    allMetrics: StaffPerformanceMetrics[],
    primaryDepartment: string
  ): StaffPerformanceMetrics {
    if (allMetrics.length === 0) {
      throw new Error('No metrics to aggregate');
    }

    if (allMetrics.length === 1) {
      return allMetrics[0];
    }

    // Use primary department as base, but aggregate numbers from all departments
    const primaryMetrics = allMetrics.find(m => m.department === primaryDepartment) || allMetrics[0];

    const totalOrdersCompleted = allMetrics.reduce((sum, m) => sum + m.ordersCompleted, 0);
    const totalOrdersSentBack = allMetrics.reduce((sum, m) => sum + m.ordersSentBack, 0);
    const totalWorkingTime = allMetrics.reduce((sum, m) => sum + m.totalWorkingTime, 0);

    // Calculate weighted averages
    const weightedAvgProcessingTime = allMetrics.reduce((sum, m) =>
      sum + (m.averageProcessingTime * m.ordersCompleted), 0) / totalOrdersCompleted;

    const overallQualityScore = totalOrdersCompleted > 0
      ? ((totalOrdersCompleted - totalOrdersSentBack) / totalOrdersCompleted) * 100
      : 100;

    const overallOrdersPerHour = totalWorkingTime > 0
      ? (totalOrdersCompleted / (totalWorkingTime / 60))
      : 0;

    const overallProductivityScore = this.calculateProductivityScore(
      totalOrdersCompleted,
      weightedAvgProcessingTime,
      overallQualityScore
    );

    console.log(`DEBUG: Aggregated metrics - Orders: ${totalOrdersCompleted}, Quality: ${overallQualityScore.toFixed(1)}%, Productivity: ${overallProductivityScore.toFixed(1)}%`);

    return {
      ...primaryMetrics,
      ordersCompleted: totalOrdersCompleted,
      ordersSentBack: totalOrdersSentBack,
      averageProcessingTime: this.sanitizeNumber(weightedAvgProcessingTime),
      qualityScore: this.sanitizePercentage(overallQualityScore),
      productivityScore: this.sanitizePercentage(overallProductivityScore),
      totalWorkingTime: this.sanitizeNumber(totalWorkingTime),
      ordersPerHour: this.sanitizeNumber(overallOrdersPerHour),
      departments: allMetrics.map(m => m.department),
      isMultiRole: true
    };
  }

  /**
   * Get detailed individual staff analytics with comprehensive order tracking
   */
  static async getIndividualStaffDetails(
    staffId: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<IndividualStaffDetails> {
    console.log(`DEBUG: Getting individual staff details for staffId: ${staffId}`);

    // Get staff information
    const staff = await prisma.user.findUnique({
      where: { id: staffId },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        role: true,
        isKitchenStaff: true,
        isDesignStaff: true,
        isFinalCheckStaff: true,
        isCashierStaff: true,
        isActive: true,
        createdAt: true,
      },
    });

    console.log(`DEBUG: Found staff:`, staff ? {
      id: staff.id,
      name: `${staff.firstName} ${staff.lastName}`,
      kitchen: staff.isKitchenStaff,
      design: staff.isDesignStaff,
      finalCheck: staff.isFinalCheckStaff,
      cashier: staff.isCashierStaff
    } : 'null');

    if (!staff) {
      throw new Error('Staff member not found');
    }

    // Get all departments and primary department
    const departments = this.getStaffDepartments(staff);
    const primaryDepartment = this.getPrimaryDepartment(staff);

    console.log(`DEBUG: Staff ${staff.firstName} ${staff.lastName} works in departments:`, departments);
    console.log(`DEBUG: Primary department:`, primaryDepartment);

    // Get performance metrics for ALL departments the staff works in
    const allPerformanceMetrics: StaffPerformanceMetrics[] = [];
    for (const dept of departments) {
      const deptMetrics = await this.getStaffPerformance(staffId, dept, startDate, endDate);
      if (deptMetrics.length > 0) {
        allPerformanceMetrics.push(...deptMetrics);
      }
    }

    console.log(`DEBUG: Found performance metrics for ${allPerformanceMetrics.length} department roles`);

    // Use primary department metrics for main display, but we have all metrics available
    const primaryMetrics = allPerformanceMetrics.find(m => m.department === primaryDepartment);
    const staffMetrics = primaryMetrics || allPerformanceMetrics[0];

    if (!staffMetrics) {
      throw new Error('Unable to calculate staff metrics');
    }

    // Aggregate metrics across all departments for comprehensive view
    const aggregatedMetrics = this.aggregateMultiDepartmentMetrics(allPerformanceMetrics, primaryDepartment);

    // Get comprehensive order history for all roles
    const orderHistory = await this.getDetailedStaffOrderHistory(staffId, departments, startDate, endDate);

    // Get advanced analytics
    const advancedAnalytics = await this.getStaffAdvancedAnalytics(staffId, departments, orderHistory, startDate, endDate);

    // Get time analytics
    const timeAnalytics = await this.getStaffTimeAnalytics(staffId, primaryDepartment, startDate, endDate);

    // Get quality analytics
    const qualityAnalytics = await this.getStaffQualityAnalytics(staffId, primaryDepartment, startDate, endDate);

    // Get performance comparison
    const performanceComparison = await this.getStaffPerformanceComparison(staffId, primaryDepartment, startDate, endDate);

    return {
      staffInfo: {
        id: staff.id,
        name: `${staff.firstName} ${staff.lastName}`,
        email: staff.email,
        department: primaryDepartment, // Keep for backward compatibility
        departments,
        primaryDepartment,
        role: staff.role,
        joinDate: staff.createdAt.toISOString(),
        isActive: staff.isActive,
        isMultiRole: departments.length > 1,
      },
      performanceMetrics: aggregatedMetrics,
      orderHistory: orderHistory.map(order => ({
        id: order.orderId,
        orderNumber: order.orderNumber,
        status: order.status,
        createdAt: order.createdAt.toISOString(),
        completedAt: order.endTime?.toISOString(),
        processingTime: order.processingTime,
        totalAmount: order.orderTotal,
        itemCount: order.items?.length || 0,
        customerName: order.customerName,
        wasSentBack: order.wasSentBack,
        qualityNotes: order.notes,
        complexity: order.complexity,
      })),
      timeAnalytics: {
        dailyHours: [],
        peakHours: [],
        weeklyTrends: []
      },
      qualityAnalytics: {
        qualityTrend: [],
        commonIssues: [],
        improvementAreas: []
      },
      performanceComparison
    };
  }

  /**
   * Get detailed order history for all roles a staff member works in
   */
  private static async getDetailedStaffOrderHistory(
    staffId: string,
    departments: string[],
    startDate?: Date,
    endDate?: Date
  ): Promise<StaffOrderDetail[]> {
    const whereClause: any = {
      status: { notIn: ['CANCELLED'] }
    };

    if (startDate && endDate) {
      whereClause.createdAt = {
        gte: startDate,
        lte: endDate,
      };
    }

    // Get ALL orders this staff member worked on in ANY role
    whereClause.OR = [
      { createdById: staffId }, // Orders they created (cashier role)
      { kitchenById: staffId }, // Orders they worked on in kitchen
      { designById: staffId }, // Orders they worked on in design
      { finalCheckById: staffId }, // Orders they worked on in final check
    ];

    console.log(`DEBUG: Querying orders for staff ${staffId} with whereClause:`, JSON.stringify(whereClause, null, 2));

    const orders = await prisma.pOSOrder.findMany({
      where: whereClause,
      select: {
        id: true,
        orderNumber: true,
        status: true,
        createdAt: true,
        isSentBack: true,
        total: true, // Explicitly select total
        subtotal: true, // Also select subtotal for debugging
        customerName: true,
        customerPhone: true,
        deliveryMethod: true,
        requiresKitchen: true,
        requiresDesign: true,
        requiresFinalCheck: true,
        // Staff assignment fields
        createdById: true,
        kitchenById: true,
        kitchenStartTime: true,
        kitchenEndTime: true,
        kitchenNotes: true,
        designById: true,
        designStartTime: true,
        designEndTime: true,
        designNotes: true,
        finalCheckById: true,
        finalCheckStartTime: true,
        finalCheckEndTime: true,
        finalCheckNotes: true,
        items: {
          select: {
            id: true,
            productName: true,
            quantity: true,
            totalPrice: true,
            variations: true,
            notes: true,
            customImages: true,
          }
        },
        statusHistory: {
          select: {
            id: true,
            status: true,
            createdAt: true,
            notes: true,
          },
          orderBy: { createdAt: 'asc' }
        },
        parallelProcessing: true,
      },
      orderBy: { createdAt: 'desc' },
      take: 500 // Limit to last 500 orders
    });

    console.log(`DEBUG: Found ${orders.length} orders for staff ${staffId}`);

    // Debug cashier orders specifically
    const cashierOrders = orders.filter(o => o.createdById === staffId);
    const kitchenOrders = orders.filter(o => o.kitchenById === staffId);
    const designOrders = orders.filter(o => o.designById === staffId);
    const finalCheckOrders = orders.filter(o => o.finalCheckById === staffId);

    console.log(`DEBUG: Order breakdown for staff ${staffId}:`);
    console.log(`  - Cashier orders: ${cashierOrders.length}`);
    console.log(`  - Kitchen orders: ${kitchenOrders.length}`);
    console.log(`  - Design orders: ${designOrders.length}`);
    console.log(`  - Final check orders: ${finalCheckOrders.length}`);

    if (cashierOrders.length > 0) {
      console.log(`DEBUG: Sample cashier orders:`, cashierOrders.slice(0, 3).map(o => ({
        orderNumber: o.orderNumber,
        status: o.status,
        createdAt: o.createdAt,
        total: o.total,
        itemCount: o.items?.length || 0
      })));
    }

    // Debug kitchen orders specifically for the issue
    if (kitchenOrders.length > 0) {
      console.log(`DEBUG: Sample kitchen orders:`, kitchenOrders.slice(0, 3).map(o => ({
        orderNumber: o.orderNumber,
        status: o.status,
        total: o.total,
        subtotal: o.subtotal,
        itemCount: o.items?.length || 0,
        itemDetails: o.items?.map(item => ({ name: item.productName, qty: item.quantity, price: item.totalPrice }))
      })));
    }

    return orders.map(order => {
      // Determine which role this staff member played in this order
      let role: 'kitchen' | 'design' | 'finalCheck' | 'cashier' = 'cashier';
      let startTime: Date | null = null;
      let endTime: Date | null = null;
      let notes: string | null = null;

      if (order.kitchenById === staffId) {
        role = 'kitchen';
        startTime = order.kitchenStartTime;
        endTime = order.kitchenEndTime;
        notes = order.kitchenNotes;
      } else if (order.designById === staffId) {
        role = 'design';
        startTime = order.designStartTime;
        endTime = order.designEndTime;
        notes = order.designNotes;
      } else if (order.finalCheckById === staffId) {
        role = 'finalCheck';
        startTime = order.finalCheckStartTime;
        endTime = order.finalCheckEndTime;
        notes = order.finalCheckNotes;
      } else if (order.createdById === staffId) {
        role = 'cashier';
        startTime = order.createdAt;
        endTime = null; // Cashier doesn't have end time
        notes = null;
      }

      const processingTime = startTime && endTime
        ? (endTime.getTime() - startTime.getTime()) / (1000 * 60)
        : null;

      const complexity = this.calculateOrderComplexity(order);

      const orderDetail = {
        orderId: order.id,
        orderNumber: order.orderNumber,
        customerName: order.customerName || 'Unknown',
        customerPhone: order.customerPhone || '',
        orderTotal: order.total,
        role,
        startTime,
        endTime,
        processingTime,
        status: order.status,
        items: order.items,
        notes,
        wasSentBack: order.isSentBack,
        complexity,
        createdAt: order.createdAt,
        deliveryMethod: order.deliveryMethod,
        requiresKitchen: order.requiresKitchen,
        requiresDesign: order.requiresDesign,
        requiresFinalCheck: order.requiresFinalCheck,
      };

      // Debug specific orders to see what's happening (only for orders ending in 1 to avoid random behavior)
      if (order.orderNumber && order.orderNumber.endsWith('1')) {
        console.log(`DEBUG: Order ${order.orderNumber} mapping:`, {
          total: order.total,
          subtotal: order.subtotal,
          itemCount: order.items?.length,
          orderTotal: orderDetail.orderTotal,
          role: orderDetail.role
        });
      }

      return orderDetail;
    });
  }

  /**
   * Get advanced analytics for a staff member
   */
  private static async getStaffAdvancedAnalytics(
    staffId: string,
    departments: string[],
    orderHistory: StaffOrderDetail[],
    startDate?: Date,
    endDate?: Date
  ): Promise<StaffAdvancedAnalytics> {
    // Daily performance calculation
    const dailyPerformance = this.calculateDailyPerformance(orderHistory);

    // Order complexity breakdown
    const orderComplexityBreakdown = this.calculateComplexityBreakdown(orderHistory);

    // Performance trends
    const performanceTrends = this.calculatePerformanceTrends(orderHistory);

    // Peak performance hours
    const peakPerformanceHours = this.calculatePeakHours(orderHistory);

    // Quality metrics
    const qualityMetrics = this.calculateQualityMetrics(orderHistory);

    // Time distribution
    const timeDistribution = this.calculateTimeDistribution(orderHistory);

    return {
      dailyPerformance,
      orderComplexityBreakdown,
      performanceTrends,
      peakPerformanceHours,
      qualityMetrics,
      timeDistribution,
    };
  }

  /**
   * Calculate daily performance metrics
   */
  private static calculateDailyPerformance(orderHistory: StaffOrderDetail[]) {
    const dailyData: { [date: string]: { orders: StaffOrderDetail[]; totalTime: number } } = {};

    orderHistory.forEach(order => {
      const date = order.createdAt.toISOString().split('T')[0];
      if (!dailyData[date]) {
        dailyData[date] = { orders: [], totalTime: 0 };
      }
      dailyData[date].orders.push(order);
      if (order.processingTime) {
        dailyData[date].totalTime += order.processingTime;
      }
    });

    return Object.entries(dailyData).map(([date, data]) => {
      const ordersProcessed = data.orders.length;
      const totalTime = data.totalTime;
      const avgTimePerOrder = ordersProcessed > 0 ? totalTime / ordersProcessed : 0;
      const ordersSentBack = data.orders.filter(o => o.wasSentBack).length;
      const qualityScore = ordersProcessed > 0 ? ((ordersProcessed - ordersSentBack) / ordersProcessed) * 100 : 100;
      const efficiency = avgTimePerOrder > 0 ? Math.max(0, (60 - avgTimePerOrder) / 60) * 100 : 0;

      return {
        date,
        ordersProcessed,
        totalTime,
        avgTimePerOrder: Math.round(avgTimePerOrder * 100) / 100,
        qualityScore: Math.round(qualityScore * 100) / 100,
        efficiency: Math.round(efficiency * 100) / 100,
      };
    }).sort((a, b) => a.date.localeCompare(b.date));
  }

  /**
   * Calculate order complexity breakdown
   */
  private static calculateComplexityBreakdown(orderHistory: StaffOrderDetail[]) {
    const breakdown = {
      low: { orders: [] as StaffOrderDetail[], totalTime: 0 },
      medium: { orders: [] as StaffOrderDetail[], totalTime: 0 },
      high: { orders: [] as StaffOrderDetail[], totalTime: 0 },
    };

    orderHistory.forEach(order => {
      breakdown[order.complexity].orders.push(order);
      if (order.processingTime) {
        breakdown[order.complexity].totalTime += order.processingTime;
      }
    });

    return {
      low: {
        count: breakdown.low.orders.length,
        avgTime: breakdown.low.orders.length > 0 ? breakdown.low.totalTime / breakdown.low.orders.length : 0,
        qualityRate: breakdown.low.orders.length > 0
          ? ((breakdown.low.orders.length - breakdown.low.orders.filter(o => o.wasSentBack).length) / breakdown.low.orders.length) * 100
          : 100,
      },
      medium: {
        count: breakdown.medium.orders.length,
        avgTime: breakdown.medium.orders.length > 0 ? breakdown.medium.totalTime / breakdown.medium.orders.length : 0,
        qualityRate: breakdown.medium.orders.length > 0
          ? ((breakdown.medium.orders.length - breakdown.medium.orders.filter(o => o.wasSentBack).length) / breakdown.medium.orders.length) * 100
          : 100,
      },
      high: {
        count: breakdown.high.orders.length,
        avgTime: breakdown.high.orders.length > 0 ? breakdown.high.totalTime / breakdown.high.orders.length : 0,
        qualityRate: breakdown.high.orders.length > 0
          ? ((breakdown.high.orders.length - breakdown.high.orders.filter(o => o.wasSentBack).length) / breakdown.high.orders.length) * 100
          : 100,
      },
    };
  }

  /**
   * Calculate performance trends
   */
  private static calculatePerformanceTrends(orderHistory: StaffOrderDetail[]) {
    // Split orders into two halves to compare trends
    const midPoint = Math.floor(orderHistory.length / 2);
    const firstHalf = orderHistory.slice(0, midPoint);
    const secondHalf = orderHistory.slice(midPoint);

    const firstHalfAvgTime = firstHalf.length > 0
      ? firstHalf.reduce((sum, o) => sum + (o.processingTime || 0), 0) / firstHalf.length
      : 0;
    const secondHalfAvgTime = secondHalf.length > 0
      ? secondHalf.reduce((sum, o) => sum + (o.processingTime || 0), 0) / secondHalf.length
      : 0;

    const firstHalfQuality = firstHalf.length > 0
      ? ((firstHalf.length - firstHalf.filter(o => o.wasSentBack).length) / firstHalf.length) * 100
      : 100;
    const secondHalfQuality = secondHalf.length > 0
      ? ((secondHalf.length - secondHalf.filter(o => o.wasSentBack).length) / secondHalf.length) * 100
      : 100;

    const speedTrend = firstHalfAvgTime > secondHalfAvgTime ? 'improving' :
                      firstHalfAvgTime < secondHalfAvgTime ? 'declining' : 'stable';
    const qualityTrend = firstHalfQuality < secondHalfQuality ? 'improving' :
                        firstHalfQuality > secondHalfQuality ? 'declining' : 'stable';

    const trendPercentage = Math.abs(((secondHalfAvgTime - firstHalfAvgTime) / firstHalfAvgTime) * 100) || 0;

    return {
      speedTrend: speedTrend as 'improving' | 'declining' | 'stable',
      qualityTrend: qualityTrend as 'improving' | 'declining' | 'stable',
      productivityTrend: speedTrend as 'improving' | 'declining' | 'stable', // Simplified
      trendPercentage: Math.round(trendPercentage * 100) / 100,
    };
  }

  /**
   * Calculate peak performance hours
   */
  private static calculatePeakHours(orderHistory: StaffOrderDetail[]) {
    const hourlyData: { [hour: number]: { orders: StaffOrderDetail[]; totalTime: number } } = {};

    orderHistory.forEach(order => {
      if (order.startTime) {
        const hour = order.startTime.getHours();
        if (!hourlyData[hour]) {
          hourlyData[hour] = { orders: [], totalTime: 0 };
        }
        hourlyData[hour].orders.push(order);
        if (order.processingTime) {
          hourlyData[hour].totalTime += order.processingTime;
        }
      }
    });

    return Object.entries(hourlyData).map(([hour, data]) => {
      const orderCount = data.orders.length;
      const avgProcessingTime = orderCount > 0 ? data.totalTime / orderCount : 0;
      const efficiency = avgProcessingTime > 0 ? Math.max(0, (60 - avgProcessingTime) / 60) * 100 : 0;

      return {
        hour: parseInt(hour),
        avgProcessingTime: Math.round(avgProcessingTime * 100) / 100,
        orderCount,
        efficiency: Math.round(efficiency * 100) / 100,
      };
    }).sort((a, b) => b.efficiency - a.efficiency);
  }

  /**
   * Calculate quality metrics
   */
  private static calculateQualityMetrics(orderHistory: StaffOrderDetail[]) {
    const totalOrdersProcessed = orderHistory.length;
    const ordersSentBack = orderHistory.filter(o => o.wasSentBack).length;
    const qualityRate = totalOrdersProcessed > 0 ? ((totalOrdersProcessed - ordersSentBack) / totalOrdersProcessed) * 100 : 100;

    // Analyze common issues from notes
    const issueKeywords = ['wrong', 'incorrect', 'missing', 'damaged', 'redo', 'fix', 'error'];
    const issues: { [key: string]: number } = {};

    orderHistory.forEach(order => {
      if (order.notes && order.wasSentBack) {
        const notes = order.notes.toLowerCase();
        issueKeywords.forEach(keyword => {
          if (notes.includes(keyword)) {
            issues[keyword] = (issues[keyword] || 0) + 1;
          }
        });
      }
    });

    const commonIssues = Object.entries(issues)
      .map(([issue, count]) => ({
        issue,
        count,
        percentage: Math.round((count / ordersSentBack) * 100) || 0,
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    const improvementSuggestions = [];
    if (qualityRate < 95) improvementSuggestions.push('Focus on quality control procedures');
    if (commonIssues.length > 0) improvementSuggestions.push(`Address common issue: ${commonIssues[0].issue}`);

    return {
      totalOrdersProcessed,
      ordersSentBack,
      qualityRate: Math.round(qualityRate * 100) / 100,
      commonIssues,
      improvementSuggestions,
    };
  }

  /**
   * Calculate time distribution
   */
  private static calculateTimeDistribution(orderHistory: StaffOrderDetail[]) {
    let fastOrders = 0;
    let normalOrders = 0;
    let slowOrders = 0;

    const complexityTimes = { low: [], medium: [], high: [] } as { [key: string]: number[] };

    orderHistory.forEach(order => {
      if (order.processingTime) {
        if (order.processingTime < 15) fastOrders++;
        else if (order.processingTime <= 45) normalOrders++;
        else slowOrders++;

        complexityTimes[order.complexity].push(order.processingTime);
      }
    });

    const averageByComplexity = {
      low: complexityTimes.low.length > 0
        ? complexityTimes.low.reduce((sum, time) => sum + time, 0) / complexityTimes.low.length
        : 0,
      medium: complexityTimes.medium.length > 0
        ? complexityTimes.medium.reduce((sum, time) => sum + time, 0) / complexityTimes.medium.length
        : 0,
      high: complexityTimes.high.length > 0
        ? complexityTimes.high.reduce((sum, time) => sum + time, 0) / complexityTimes.high.length
        : 0,
    };

    return {
      fastOrders,
      normalOrders,
      slowOrders,
      averageByComplexity: {
        low: Math.round(averageByComplexity.low * 100) / 100,
        medium: Math.round(averageByComplexity.medium * 100) / 100,
        high: Math.round(averageByComplexity.high * 100) / 100,
      },
    };
  }

  /**
   * Get detailed order history for a staff member
   */
  private static async getStaffOrderHistory(
    staffId: string,
    department: string,
    startDate?: Date,
    endDate?: Date
  ) {
    const whereClause: any = {};

    if (startDate && endDate) {
      whereClause.createdAt = {
        gte: startDate,
        lte: endDate,
      };
    }

    // Build query based on department
    let ordersQuery: any = {
      where: {
        ...whereClause,
        status: { notIn: ['CANCELLED'] }
      }
    };

    switch (department) {
      case 'kitchen':
        ordersQuery.where.kitchenById = staffId;
        break;
      case 'design':
        ordersQuery.where.designById = staffId;
        break;
      case 'finalCheck':
        ordersQuery.where.finalCheckById = staffId;
        break;
      case 'cashier':
        ordersQuery.where.createdById = staffId;
        break;
    }

    const orders = await prisma.pOSOrder.findMany({
      ...ordersQuery,
      include: {
        items: {
          select: {
            id: true,
            quantity: true,
            totalPrice: true,
            productName: true,
          }
        },
        statusHistory: {
          orderBy: { createdAt: 'asc' }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 300 // Limit to last 300 orders
    });

    return orders.map(order => {
      // Calculate processing time based on department
      let processingTime: number | undefined;
      let completedAt: string | undefined;

      switch (department) {
        case 'kitchen':
          if (order.kitchenStartTime && order.kitchenEndTime) {
            processingTime = Math.round((order.kitchenEndTime.getTime() - order.kitchenStartTime.getTime()) / (1000 * 60));
            completedAt = order.kitchenEndTime.toISOString();
          }
          break;
        case 'design':
          if (order.designStartTime && order.designEndTime) {
            processingTime = Math.round((order.designEndTime.getTime() - order.designStartTime.getTime()) / (1000 * 60));
            completedAt = order.designEndTime.toISOString();
          }
          break;
        case 'finalCheck':
          if (order.finalCheckStartTime && order.finalCheckEndTime) {
            processingTime = Math.round((order.finalCheckEndTime.getTime() - order.finalCheckStartTime.getTime()) / (1000 * 60));
            completedAt = order.finalCheckEndTime.toISOString();
          }
          break;
        case 'cashier':
          // For cashier, use creation to first status change
          const statusHistory = (order as any).statusHistory;
          if (statusHistory && statusHistory.length > 0) {
            const firstStatusChange = statusHistory[0];
            if (firstStatusChange) {
              processingTime = Math.round((firstStatusChange.createdAt.getTime() - order.createdAt.getTime()) / (1000 * 60));
              completedAt = firstStatusChange.createdAt.toISOString();
            }
          }
          break;
      }

      // Determine complexity based on items and custom work
      const items = (order as any).items;
      const itemCount = items ? items.reduce((sum: number, item: any) => sum + item.quantity, 0) : 0;
      let complexity: 'low' | 'medium' | 'high' = 'low';
      if (itemCount > 5 || order.requiresDesign) complexity = 'medium';
      if (itemCount > 10 || (order.requiresDesign && order.requiresKitchen)) complexity = 'high';

      return {
        id: order.id,
        orderNumber: order.orderNumber,
        status: order.status,
        createdAt: order.createdAt.toISOString(),
        completedAt,
        processingTime,
        totalAmount: order.total,
        itemCount,
        customerName: order.customerName || 'Walk-in Customer',
        wasSentBack: order.isSentBack,
        qualityNotes: department === 'finalCheck' ? order.finalCheckNotes :
                     department === 'design' ? order.designNotes :
                     department === 'kitchen' ? order.kitchenNotes : undefined,
        complexity,
      };
    });
  }

  /**
   * Get time analytics for a staff member (simplified version)
   */
  private static async getStaffTimeAnalytics(
    staffId: string,
    department: string,
    startDate?: Date,
    endDate?: Date
  ) {
    // For now, return mock data structure
    // This would be enhanced with actual time tracking data
    return {
      dailyHours: [],
      peakHours: [],
      weeklyTrends: [],
    };
  }

  /**
   * Get quality analytics for a staff member (simplified version)
   */
  private static async getStaffQualityAnalytics(
    staffId: string,
    department: string,
    startDate?: Date,
    endDate?: Date
  ) {
    // For now, return mock data structure
    // This would be enhanced with actual quality tracking
    return {
      qualityTrend: [],
      commonIssues: [],
      improvementAreas: [],
    };
  }

  /**
   * Get performance comparison for a staff member (simplified version)
   */
  private static async getStaffPerformanceComparison(
    staffId: string,
    department: string,
    startDate?: Date,
    endDate?: Date
  ) {
    // Get all staff in the same department for comparison
    const departmentStaff = await this.getStaffPerformance(undefined, department, startDate, endDate);
    const currentStaff = departmentStaff.find(staff => staff.staffId === staffId);

    if (!currentStaff) {
      return {
        departmentRank: 0,
        departmentTotal: departmentStaff.length,
        percentile: 0,
        comparedToAverage: {
          productivity: 0,
          quality: 0,
          speed: 0,
        },
      };
    }

    // Sort by productivity score to get ranking
    const sortedStaff = departmentStaff.sort((a, b) => b.productivityScore - a.productivityScore);
    const rank = sortedStaff.findIndex(staff => staff.staffId === staffId) + 1;
    const percentile = ((departmentStaff.length - rank + 1) / departmentStaff.length) * 100;

    // Calculate averages
    const avgProductivity = departmentStaff.reduce((sum, staff) => sum + staff.productivityScore, 0) / departmentStaff.length;
    const avgQuality = departmentStaff.reduce((sum, staff) => sum + staff.qualityScore, 0) / departmentStaff.length;
    const avgSpeed = departmentStaff.reduce((sum, staff) => sum + staff.averageProcessingTime, 0) / departmentStaff.length;

    return {
      departmentRank: rank,
      departmentTotal: departmentStaff.length,
      percentile: Math.round(percentile),
      comparedToAverage: {
        productivity: Math.round(((currentStaff.productivityScore - avgProductivity) / avgProductivity) * 100),
        quality: Math.round(((currentStaff.qualityScore - avgQuality) / avgQuality) * 100),
        speed: Math.round(((avgSpeed - currentStaff.averageProcessingTime) / avgSpeed) * 100), // Negative because lower time is better
      },
    };
  }
}
