export interface BlogPost {
  id: string;
  title: string;
  content: string;
  excerpt: string;
  slug: string;
  metaTitle?: string;
  metaDescription?: string;
  featuredImage: string;
  publishedAt: string;
  categories: {
    id: string;
    name: string;
    slug: string;
    description: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
  }[];
  author: string;
}

export interface BlogCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
  success: boolean;
}
