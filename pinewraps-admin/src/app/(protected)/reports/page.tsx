"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Heading } from "@/components/ui/heading";
import {
  Download,
  Calendar,
  RefreshCw
} from "lucide-react";
import { format, subDays } from "date-fns";
import { Skeleton } from "@/components/ui/skeleton";
import { Area, AreaChart, Bar, BarChart, CartesianGrid, Cell, Legend, Pie, PieChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from "recharts";
import { reportsService } from "@/services/reports.service";
import { cn } from "@/lib/utils";
import { toast } from 'sonner';

// Types for the reports data
interface SalesSummary {
  totalSales: number;
  totalOrders: number;
  averageOrderValue: number;
  onlineSales: number;
  posSales: number;
  previousPeriodSales: number;
  previousPeriodOrders: number;
  totalCustomers: number;
  refundAmount: number;
  deliveryFees: number;
  totalDiscounts: number;
  paymentMethods: PaymentMethodData[];
}

interface PaymentMethodData {
  method: string;
  amount: number;
  count: number;
  percentage: number;
}

interface DailySales {
  date: string;
  sales: number;
  orders: number;
  onlineSales: number;
  onlineOrders: number;
  posSales: number;
  posOrders: number;
}

interface TopProduct {
  id: string;
  name: string;
  quantity: number;
  revenue: number;
  percentage: number;
}

interface CategorySales {
  id: string;
  category: string;
  sales: number;
  percentage: number;
}

export default function ReportsPage() {

  const [date, setDate] = useState<{
    from: Date;
    to: Date;
  }>({
    from: subDays(new Date(), 30),
    to: new Date(),
  });

  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState("overview");

  // State for report data
  const [salesSummary, setSalesSummary] = useState<SalesSummary | null>(null);
  const [dailySales, setDailySales] = useState<DailySales[]>([]);
  const [topProducts, setTopProducts] = useState<TopProduct[]>([]);
  const [categorySales, setCategorySales] = useState<CategorySales[]>([]);

  const handleDateChange = (range: { from?: Date; to?: Date } | undefined) => {
    if (range?.from && range?.to) {
      console.log('Date range changed:', { from: range.from, to: range.to });
      setDate({ from: range.from, to: range.to });
    } else if (!range) {
      // Reset to default date range (last 30 days)
      const defaultRange = { from: subDays(new Date(), 30), to: new Date() };
      console.log('Resetting to default date range:', defaultRange);
      setDate(defaultRange);
    }
  };

  const handleExport = async (reportType?: string) => {
    try {
      const reportToExport = reportType || activeTab;
      const blob = await reportsService.exportReport(
        reportToExport,
        date.from,
        date.to
      );

      // Create a download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${reportToExport}-report-${format(date.from, 'yyyy-MM-dd')}-to-${format(date.to, 'yyyy-MM-dd')}.csv`;
      document.body.appendChild(a);
      a.click();

      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error exporting report:', error);
    }
  };

  const fetchReportData = async () => {
    setIsRefreshing(true);
    try {
      console.log('Fetching report data for date range:', {
        from: format(date.from, 'yyyy-MM-dd'),
        to: format(date.to, 'yyyy-MM-dd')
      });

      // Fetch all report data in parallel with individual error handling
      // This ensures that if one API call fails, the others can still succeed
      const [summary, daily, products, categories] = await Promise.all([
        reportsService.getSalesSummary(date.from, date.to).catch(err => {
          console.error('Error fetching sales summary:', err);
          // The service will return mock data on error
          return reportsService.getSalesSummary(date.from, date.to);
        }),
        reportsService.getDailySales(date.from, date.to).catch(err => {
          console.error('Error fetching daily sales:', err);
          // The service will return mock data on error
          return reportsService.getDailySales(date.from, date.to);
        }),
        reportsService.getTopProducts(date.from, date.to, 10).catch(err => {
          console.error('Error fetching top products:', err);
          // The service will return mock data on error
          return reportsService.getTopProducts(date.from, date.to, 10);
        }),
        reportsService.getTopCategories(date.from, date.to, 10).catch(err => {
          console.error('Error fetching top categories:', err);
          // The service will return mock data on error
          return reportsService.getTopCategories(date.from, date.to, 10);
        })
      ]);

      console.log('Report data processed successfully');

      // Update state with fetched data
      setSalesSummary(summary);
      setDailySales(daily);
      setTopProducts(products);
      setCategorySales(categories);

      // Show toast notification
      toast.success("Reports Updated", {
        description: `Data loaded for ${format(date.from, 'MMM d, yyyy')} to ${format(date.to, 'MMM d, yyyy')}`,
      });
    } catch (error) {
      console.error('Error fetching report data:', error);

      // Show error toast but still ensure we have data to display
      toast.error("Error Loading Reports", {
        description: "Using sample data. Backend error: column o.payment_method does not exist.",
      });

      // Set mock data as fallback
      setSalesSummary({
        totalSales: 12500,
        totalOrders: 85,
        averageOrderValue: 147.06,
        onlineSales: 8750,
        posSales: 3750,
        previousPeriodSales: 10000,
        previousPeriodOrders: 70,
        totalCustomers: 65,
        refundAmount: 250,
        deliveryFees: 500,
        totalDiscounts: 750,
        paymentMethods: [
          { method: 'Credit Card', amount: 7500, count: 50, percentage: 60 },
          { method: 'Cash', amount: 3750, count: 25, percentage: 30 },
          { method: 'Digital Wallet', amount: 1250, count: 10, percentage: 10 }
        ]
      });
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Fetch data when date range changes
  useEffect(() => {
    fetchReportData();
  }, [date]);

  // Preset date ranges
  const presetRanges = [
    {
      label: "Today",
      onClick: () => setDate({ from: new Date(), to: new Date() }),
    },
    {
      label: "Yesterday",
      onClick: () => {
        const yesterday = subDays(new Date(), 1);
        setDate({ from: yesterday, to: yesterday });
      },
    },
    {
      label: "Last 7 days",
      onClick: () => setDate({ from: subDays(new Date(), 7), to: new Date() }),
    },
    {
      label: "Last 30 days",
      onClick: () => setDate({ from: subDays(new Date(), 30), to: new Date() }),
    },
    {
      label: "This month",
      onClick: () => {
        const now = new Date();
        const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        setDate({ from: firstDayOfMonth, to: now });
      },
    },
  ];

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AE', {
      style: 'currency',
      currency: 'AED',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(Math.floor(amount));
  };

  // Format percentage
  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  // Colors for charts
  const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff8042', '#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

  // Format date for tooltips
  const formatTooltipDate = (label: string | number) => {
    if (typeof label === 'string') {
      // Parse the date string in format 'yyyy-MM-dd'
      if (label.match(/^\d{4}-\d{2}-\d{2}$/)) {
        try {
          const [year, month, day] = label.split('-').map(Number);
          const date = new Date(year, month - 1, day); // JS months are 0-indexed
          if (!isNaN(date.getTime())) {
            return format(date, 'MMM dd, yyyy');
          }
        } catch (error) {
          console.error('Error parsing date string:', error, label);
        }
      }
      return label;
    }

    // Handle timestamp (number) format
    try {
      const date = new Date(label);
      if (!isNaN(date.getTime())) {
        return format(date, 'MMM dd, yyyy');
      }
      return String(label);
    } catch (error) {
      console.error('Error formatting date:', error, label);
      return String(label);
    }
  };

  return (
    <div className="flex-1 space-y-4">
      <div className="flex items-center justify-between">
        <Heading
          title="Sales Reports"
          description="View and analyze your sales performance across all channels"
        />
        <div className="flex items-center space-x-2">
          <DateRangePicker
            value={date}
            onChange={handleDateChange}
            placeholder="Select date range"
          />
          <Button
            variant="outline"
            size="sm"
            onClick={fetchReportData}
            disabled={isRefreshing}
            title="Refresh data"
            className="h-9"
          >
            <RefreshCw className={cn("h-4 w-4 mr-2", isRefreshing && "animate-spin")} />
            Refresh
          </Button>
          <Button
            onClick={() => handleExport()}
            variant="outline"
            size="sm"
            className="h-9"
            disabled={isLoading || isRefreshing}
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>
      <Separator />
      
      <div className="flex flex-wrap gap-2">
        {presetRanges.map((range) => {
          // Check if this preset is currently active
          const isActive = (() => {
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            if (range.label === "Today") {
              return date.from.toDateString() === today.toDateString() &&
                     date.to.toDateString() === today.toDateString();
            }

            if (range.label === "Yesterday") {
              const yesterday = subDays(today, 1);
              return date.from.toDateString() === yesterday.toDateString() &&
                     date.to.toDateString() === yesterday.toDateString();
            }

            if (range.label === "Last 7 days") {
              const sevenDaysAgo = subDays(today, 7);
              return date.from.toDateString() === sevenDaysAgo.toDateString() &&
                     date.to.toDateString() === today.toDateString();
            }

            if (range.label === "Last 30 days") {
              const thirtyDaysAgo = subDays(today, 30);
              return date.from.toDateString() === thirtyDaysAgo.toDateString() &&
                     date.to.toDateString() === today.toDateString();
            }

            if (range.label === "This month") {
              const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
              return date.from.toDateString() === firstDayOfMonth.toDateString() &&
                     date.to.toDateString() === today.toDateString();
            }

            return false;
          })();

          return (
            <Button
              key={range.label}
              variant={isActive ? "default" : "outline"}
              size="sm"
              onClick={range.onClick}
              className={cn(
                "flex items-center gap-1",
                isActive && "bg-primary text-primary-foreground"
              )}
              disabled={isRefreshing}
            >
              <Calendar className="h-3.5 w-3.5" />
              {range.label}
            </Button>
          );
        })}
      </div>

      {/* Export Buttons */}
      <div className="flex flex-wrap gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleExport('orders')}
          disabled={isRefreshing}
          className="flex items-center gap-1"
        >
          <Download className="h-3.5 w-3.5" />
          Export Orders
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={() => handleExport('pos-orders')}
          disabled={isRefreshing}
          className="flex items-center gap-1"
        >
          <Download className="h-3.5 w-3.5" />
          Export POS Orders
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={() => handleExport('purchase-orders')}
          disabled={isRefreshing}
          className="flex items-center gap-1"
        >
          <Download className="h-3.5 w-3.5" />
          Export Purchase Orders
        </Button>
      </div>

      <Separator />

      <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="products">Products</TabsTrigger>
          <TabsTrigger value="categories">Categories</TabsTrigger>
          <TabsTrigger value="payments">Payment Methods</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics */}
          <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
            <Card className="col-span-1">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Sales</CardTitle>
                <div className="flex items-baseline justify-between">
                  <div className="text-2xl font-bold">
                    {isLoading ? (
                      <Skeleton className="h-8 w-24" />
                    ) : (
                      formatCurrency(salesSummary?.totalSales || 0)
                    )}
                  </div>
                  {!isLoading && salesSummary && (
                    <div className={cn(
                      "text-xs font-medium",
                      salesSummary.totalSales >= salesSummary.previousPeriodSales ? "text-green-600" : "text-red-600"
                    )}>
                      {salesSummary.totalSales >= salesSummary.previousPeriodSales ? "+" : "-"}
                      {Math.abs(Math.round(((salesSummary.totalSales - salesSummary.previousPeriodSales) /
                        (salesSummary.previousPeriodSales || 1)) * 100 * 10) / 10).toFixed(1)}%
                    </div>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <div className="h-[100px]">
                  {isLoading ? (
                    <Skeleton className="h-full w-full" />
                  ) : (
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart
                        data={dailySales}
                        margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
                      >
                        <defs>
                          <linearGradient id="colorTotalSales" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#8884d8" stopOpacity={0.8}/>
                            <stop offset="95%" stopColor="#8884d8" stopOpacity={0}/>
                          </linearGradient>
                        </defs>
                        <XAxis
                          dataKey="date"
                          hide={true}
                        />
                        <Tooltip
                          formatter={(value: number) => [formatCurrency(value), 'Total Sales']}
                          labelFormatter={formatTooltipDate}
                        />
                        <Area
                          type="monotone"
                          dataKey="sales"
                          stroke="#8884d8"
                          fillOpacity={1}
                          fill="url(#colorTotalSales)"
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card className="col-span-1">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Pos Order Count</CardTitle>
                <div className="flex items-baseline justify-between">
                  <div className="text-2xl font-bold">
                    {isLoading ? (
                      <Skeleton className="h-8 w-24" />
                    ) : (
                      salesSummary?.totalOrders || 0
                    )}
                  </div>
                  {!isLoading && salesSummary && (
                    <div className={cn(
                      "text-xs font-medium",
                      salesSummary.totalOrders >= salesSummary.previousPeriodOrders ? "text-green-600" : "text-red-600"
                    )}>
                      {salesSummary.totalOrders >= salesSummary.previousPeriodOrders ? "+" : "-"}
                      {Math.abs(Math.round(((salesSummary.totalOrders - salesSummary.previousPeriodOrders) /
                        (salesSummary.previousPeriodOrders || 1)) * 100 * 10) / 10).toFixed(1)}%
                    </div>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <div className="h-[100px]">
                  {isLoading ? (
                    <Skeleton className="h-full w-full" />
                  ) : (
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart
                        data={dailySales}
                        margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
                      >
                        <defs>
                          <linearGradient id="colorOrders" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#ff6b6b" stopOpacity={0.8}/>
                            <stop offset="95%" stopColor="#ff6b6b" stopOpacity={0}/>
                          </linearGradient>
                        </defs>
                        <XAxis
                          dataKey="date"
                          hide={true}
                        />
                        <Tooltip
                          formatter={(value: number) => [value, 'Orders']}
                          labelFormatter={formatTooltipDate}
                        />
                        <Area
                          type="monotone"
                          dataKey="orders"
                          stroke="#ff6b6b"
                          fillOpacity={1}
                          fill="url(#colorOrders)"
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card className="col-span-1">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Average Order Value</CardTitle>
                <div className="flex items-baseline justify-between">
                  <div className="text-2xl font-bold">
                    {isLoading ? (
                      <Skeleton className="h-8 w-24" />
                    ) : (
                      formatCurrency(salesSummary?.averageOrderValue || 0)
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="h-[100px]">
                  {isLoading ? (
                    <Skeleton className="h-full w-full" />
                  ) : (
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart
                        data={dailySales.map(day => ({
                          ...day,
                          avgOrderValue: day.orders > 0 ? day.sales / day.orders : 0
                        }))}
                        margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
                      >
                        <defs>
                          <linearGradient id="colorAvg" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#4bc0c0" stopOpacity={0.8}/>
                            <stop offset="95%" stopColor="#4bc0c0" stopOpacity={0}/>
                          </linearGradient>
                        </defs>
                        <XAxis
                          dataKey="date"
                          hide={true}
                        />
                        <Tooltip
                          formatter={(value: number) => [formatCurrency(value), 'Average Order']}
                          labelFormatter={formatTooltipDate}
                        />
                        <Area
                          type="monotone"
                          dataKey="avgOrderValue"
                          stroke="#4bc0c0"
                          fillOpacity={1}
                          fill="url(#colorAvg)"
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card className="col-span-1">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Customers</CardTitle>
                <div className="flex items-baseline justify-between">
                  <div className="text-2xl font-bold">
                    {isLoading ? (
                      <Skeleton className="h-8 w-24" />
                    ) : (
                      salesSummary?.totalCustomers || 0
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="h-[100px]">
                  {isLoading ? (
                    <Skeleton className="h-full w-full" />
                  ) : (
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={dailySales}
                        margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
                      >
                        <XAxis
                          dataKey="date"
                          hide={true}
                        />
                        <Tooltip
                          formatter={(value: number) => [value, 'Customers']}
                          labelFormatter={formatTooltipDate}
                        />
                        <Bar
                          dataKey="customers"
                          fill="#9966ff"
                          radius={[4, 4, 0, 0]}
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card className="col-span-1">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Refund Amount</CardTitle>
                <div className="flex items-baseline justify-between">
                  <div className="text-2xl font-bold">
                    {isLoading ? (
                      <Skeleton className="h-8 w-24" />
                    ) : (
                      formatCurrency(salesSummary?.refundAmount || 0)
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="h-[100px]">
                  {isLoading ? (
                    <Skeleton className="h-full w-full" />
                  ) : (
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart
                        data={dailySales}
                        margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
                      >
                        <defs>
                          <linearGradient id="colorRefunds" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#ff9f43" stopOpacity={0.8}/>
                            <stop offset="95%" stopColor="#ff9f43" stopOpacity={0}/>
                          </linearGradient>
                        </defs>
                        <XAxis
                          dataKey="date"
                          hide={true}
                        />
                        <Tooltip
                          formatter={(value: number) => [formatCurrency(value), 'Refunds']}
                          labelFormatter={formatTooltipDate}
                        />
                        <Area
                          type="monotone"
                          dataKey="refunds"
                          stroke="#ff9f43"
                          fillOpacity={1}
                          fill="url(#colorRefunds)"
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card className="col-span-1">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Delivery Fees</CardTitle>
                <div className="flex items-baseline justify-between">
                  <div className="text-2xl font-bold">
                    {isLoading ? (
                      <Skeleton className="h-8 w-24" />
                    ) : (
                      formatCurrency(salesSummary?.deliveryFees || 0)
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="h-[100px]">
                  {isLoading ? (
                    <Skeleton className="h-full w-full" />
                  ) : (
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart
                        data={dailySales}
                        margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
                      >
                        <defs>
                          <linearGradient id="colorDelivery" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#26de81" stopOpacity={0.8}/>
                            <stop offset="95%" stopColor="#26de81" stopOpacity={0}/>
                          </linearGradient>
                        </defs>
                        <XAxis
                          dataKey="date"
                          hide={true}
                        />
                        <Tooltip
                          formatter={(value: number) => [formatCurrency(value), 'Delivery Fees']}
                          labelFormatter={formatTooltipDate}
                        />
                        <Area
                          type="monotone"
                          dataKey="deliveryFees"
                          stroke="#26de81"
                          fillOpacity={1}
                          fill="url(#colorDelivery)"
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card className="col-span-1">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Online Sales</CardTitle>
                <div className="flex items-baseline justify-between">
                  <div className="text-2xl font-bold">
                    {isLoading ? (
                      <Skeleton className="h-8 w-24" />
                    ) : (
                      formatCurrency(salesSummary?.onlineSales || 0)
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="h-[100px]">
                  {isLoading ? (
                    <Skeleton className="h-full w-full" />
                  ) : (
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart
                        data={dailySales}
                        margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
                      >
                        <defs>
                          <linearGradient id="colorOnlineSales" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#82ca9d" stopOpacity={0.8}/>
                            <stop offset="95%" stopColor="#82ca9d" stopOpacity={0}/>
                          </linearGradient>
                        </defs>
                        <XAxis
                          dataKey="date"
                          hide={true}
                        />
                        <Tooltip
                          formatter={(value: number) => [formatCurrency(value), 'Online Sales']}
                          labelFormatter={formatTooltipDate}
                        />
                        <Area
                          type="monotone"
                          dataKey="onlineSales"
                          stroke="#82ca9d"
                          fillOpacity={1}
                          fill="url(#colorOnlineSales)"
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card className="col-span-1">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">POS Sales</CardTitle>
                <div className="flex items-baseline justify-between">
                  <div className="text-2xl font-bold">
                    {isLoading ? (
                      <Skeleton className="h-8 w-24" />
                    ) : (
                      formatCurrency(salesSummary?.posSales || 0)
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="h-[100px]">
                  {isLoading ? (
                    <Skeleton className="h-full w-full" />
                  ) : (
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart
                        data={dailySales}
                        margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
                      >
                        <defs>
                          <linearGradient id="colorPosSales" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#ffc658" stopOpacity={0.8}/>
                            <stop offset="95%" stopColor="#ffc658" stopOpacity={0}/>
                          </linearGradient>
                        </defs>
                        <XAxis
                          dataKey="date"
                          hide={true}
                        />
                        <Tooltip
                          formatter={(value: number) => [formatCurrency(value), 'POS Sales']}
                          labelFormatter={formatTooltipDate}
                        />
                        <Area
                          type="monotone"
                          dataKey="posSales"
                          stroke="#ffc658"
                          fillOpacity={1}
                          fill="url(#colorPosSales)"
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Daily Sales Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Daily Sales</CardTitle>
              <CardDescription>Sales and order trends over time</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                {isLoading ? (
                  <div className="flex items-center justify-center h-full">
                    <Skeleton className="h-full w-full" />
                  </div>
                ) : (
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart
                      data={dailySales.map(day => ({
                        ...day,
                        // Ensure total sales is always the sum of online sales and POS sales
                        sales: day.onlineSales + day.posSales
                      }))}
                      margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                    >
                      <defs>
                        <linearGradient id="colorSales" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#8884d8" stopOpacity={0.8}/>
                          <stop offset="95%" stopColor="#8884d8" stopOpacity={0}/>
                        </linearGradient>
                        <linearGradient id="colorOnline" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#82ca9d" stopOpacity={0.8}/>
                          <stop offset="95%" stopColor="#82ca9d" stopOpacity={0}/>
                        </linearGradient>
                        <linearGradient id="colorPos" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#ffc658" stopOpacity={0.8}/>
                          <stop offset="95%" stopColor="#ffc658" stopOpacity={0}/>
                        </linearGradient>
                      </defs>
                      <XAxis
                        dataKey="date"
                        tickFormatter={(value) => {
                          // Parse the date string in format 'yyyy-MM-dd'
                          if (typeof value === 'string') {
                            const parts = value.split('-');
                            if (parts.length === 3) {
                              const year = parseInt(parts[0]);
                              const month = parseInt(parts[1]) - 1; // JS months are 0-indexed
                              const day = parseInt(parts[2]);
                              return format(new Date(year, month, day), 'MMM dd');
                            }
                          }
                          return format(new Date(value), 'MMM dd');
                        }}
                      />
                      <YAxis
                        tickFormatter={(value) => {
                          return new Intl.NumberFormat('en-AE', {
                            style: 'currency',
                            currency: 'AED',
                            notation: 'compact',
                            compactDisplay: 'short',
                          }).format(value);
                        }}
                      />
                      <CartesianGrid strokeDasharray="3 3" />
                      <Tooltip
                        formatter={(value: number) => [
                          formatCurrency(value),
                          ''
                        ]}
                        labelFormatter={formatTooltipDate}
                      />
                      <Legend />
                      <Area
                        type="monotone"
                        dataKey="sales"
                        name="Total Sales"
                        stroke="#8884d8"
                        fillOpacity={1}
                        fill="url(#colorSales)"
                      />
                      <Area
                        type="monotone"
                        dataKey="onlineSales"
                        name="Online Sales"
                        stroke="#82ca9d"
                        fillOpacity={1}
                        fill="url(#colorOnline)"
                      />
                      <Area
                        type="monotone"
                        dataKey="posSales"
                        name="POS Sales"
                        stroke="#ffc658"
                        fillOpacity={1}
                        fill="url(#colorPos)"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="products" className="space-y-6">
          {/* Product Performance Summary Cards */}
          <div className="grid gap-4 grid-cols-1 md:grid-cols-3">
            <Card className="col-span-1 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Top Product</CardTitle>
                <div className="text-2xl font-bold">
                  {isLoading ? (
                    <Skeleton className="h-8 w-24" />
                  ) : (
                    topProducts[0]?.name || "No data"
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex justify-between items-center">
                  <div>
                    <p className="text-sm text-muted-foreground">Revenue</p>
                    <p className="text-lg font-semibold">
                      {isLoading ? (
                        <Skeleton className="h-6 w-16" />
                      ) : (
                        formatCurrency(topProducts[0]?.revenue || 0)
                      )}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Quantity</p>
                    <p className="text-lg font-semibold">
                      {isLoading ? (
                        <Skeleton className="h-6 w-16" />
                      ) : (
                        topProducts[0]?.quantity || 0
                      )}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">% of Sales</p>
                    <p className="text-lg font-semibold">
                      {isLoading ? (
                        <Skeleton className="h-6 w-16" />
                      ) : (
                        formatPercentage(topProducts[0]?.percentage || 0)
                      )}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="col-span-1 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Products Sold</CardTitle>
                <div className="text-2xl font-bold">
                  {isLoading ? (
                    <Skeleton className="h-8 w-24" />
                  ) : (
                    topProducts.reduce((sum, product) => sum + product.quantity, 0)
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex justify-between items-center">
                  <div>
                    <p className="text-sm text-muted-foreground">Unique Products</p>
                    <p className="text-lg font-semibold">
                      {isLoading ? (
                        <Skeleton className="h-6 w-16" />
                      ) : (
                        topProducts.length
                      )}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Avg. Per Product</p>
                    <p className="text-lg font-semibold">
                      {isLoading ? (
                        <Skeleton className="h-6 w-16" />
                      ) : (
                        Math.round(topProducts.reduce((sum, product) => sum + product.quantity, 0) / (topProducts.length || 1))
                      )}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="col-span-1 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Product Revenue</CardTitle>
                <div className="text-2xl font-bold">
                  {isLoading ? (
                    <Skeleton className="h-8 w-24" />
                  ) : (
                    formatCurrency(topProducts.reduce((sum, product) => sum + product.revenue, 0))
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex justify-between items-center">
                  <div>
                    <p className="text-sm text-muted-foreground">Avg. Revenue</p>
                    <p className="text-lg font-semibold">
                      {isLoading ? (
                        <Skeleton className="h-6 w-16" />
                      ) : (
                        formatCurrency(topProducts.reduce((sum, product) => sum + product.revenue, 0) / (topProducts.length || 1))
                      )}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Avg. Price</p>
                    <p className="text-lg font-semibold">
                      {isLoading ? (
                        <Skeleton className="h-6 w-16" />
                      ) : (
                        formatCurrency(topProducts.reduce((sum, product) => sum + product.revenue, 0) /
                          topProducts.reduce((sum, product) => sum + product.quantity, 0) || 0)
                      )}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Top Products Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Top Selling Products</CardTitle>
              <CardDescription>Products with the highest revenue</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                {isLoading ? (
                  <div className="flex items-center justify-center h-full">
                    <Skeleton className="h-full w-full" />
                  </div>
                ) : (
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={topProducts}
                      layout="vertical"
                      margin={{ top: 5, right: 30, left: 180, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis
                        type="number"
                        tickFormatter={(value) => {
                          return new Intl.NumberFormat('en-AE', {
                            style: 'currency',
                            currency: 'AED',
                            notation: 'compact',
                            compactDisplay: 'short',
                          }).format(value);
                        }}
                      />
                      <YAxis
                        type="category"
                        dataKey="name"
                        width={180}
                        tickFormatter={(value) => {
                          return value.length > 25 ? value.substring(0, 25) + '...' : value;
                        }}
                      />
                      <Tooltip
                        formatter={(value: number, name) => {
                          if (name === 'revenue') {
                            return [formatCurrency(value), 'Revenue'];
                          }
                          if (name === 'percentage') {
                            return [formatPercentage(value), 'Percentage'];
                          }
                          return [value, name];
                        }}
                        labelFormatter={(label) => `Product: ${label}`}
                      />
                      <Legend />
                      <Bar
                        dataKey="revenue"
                        name="Revenue"
                        fill="#8884d8"
                        radius={[0, 4, 4, 0]}
                      >
                        {topProducts.map((_, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Bar>
                    </BarChart>
                  </ResponsiveContainer>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Products Table */}
          <Card>
            <CardHeader>
              <CardTitle>Product Sales Details</CardTitle>
              <CardDescription>Complete list of top selling products</CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-2">
                  <Skeleton className="h-6 w-full" />
                  <Skeleton className="h-6 w-full" />
                  <Skeleton className="h-6 w-full" />
                  <Skeleton className="h-6 w-full" />
                  <Skeleton className="h-6 w-full" />
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b">
                        <th className="py-2 px-4 text-left font-medium">Product Name</th>
                        <th className="py-2 px-4 text-right font-medium">Quantity</th>
                        <th className="py-2 px-4 text-right font-medium">Revenue</th>
                        <th className="py-2 px-4 text-right font-medium">% of Sales</th>
                      </tr>
                    </thead>
                    <tbody>
                      {topProducts.map((product, index) => (
                        <tr key={index} className={index % 2 === 0 ? 'bg-muted/50' : ''}>
                          <td className="py-2 px-4">{product.name}</td>
                          <td className="py-2 px-4 text-right">{product.quantity}</td>
                          <td className="py-2 px-4 text-right">{formatCurrency(product.revenue)}</td>
                          <td className="py-2 px-4 text-right">{formatPercentage(product.percentage)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="categories" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Sales by Category</CardTitle>
              <CardDescription>Revenue breakdown by product category</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                {isLoading ? (
                  <div className="flex items-center justify-center h-full">
                    <Skeleton className="h-full w-full" />
                  </div>
                ) : (
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={categorySales}
                      layout="vertical"
                      margin={{ top: 5, right: 30, left: 100, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis
                        type="number"
                        tickFormatter={(value) => {
                          return new Intl.NumberFormat('en-AE', {
                            style: 'currency',
                            currency: 'AED',
                            notation: 'compact',
                            compactDisplay: 'short',
                          }).format(value);
                        }}
                      />
                      <YAxis
                        type="category"
                        dataKey="category"
                        width={100}
                        tickFormatter={(value) => {
                          return value.length > 15 ? value.substring(0, 15) + '...' : value;
                        }}
                      />
                      <Tooltip
                        formatter={(value: number, name) => {
                          if (name === 'sales') {
                            return [formatCurrency(value), 'Sales'];
                          }
                          if (name === 'percentage') {
                            return [formatPercentage(value), 'Percentage'];
                          }
                          return [value, name];
                        }}
                        labelFormatter={formatTooltipDate}
                      />
                      <Legend />
                      <Bar
                        dataKey="sales"
                        name="Sales"
                        fill="#82ca9d"
                        radius={[0, 4, 4, 0]}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="payments" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Sales by Payment Method</CardTitle>
              <CardDescription>Revenue breakdown by payment method</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                {isLoading ? (
                  <div className="flex items-center justify-center h-full">
                    <Skeleton className="h-full w-full" />
                  </div>
                ) : (
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={salesSummary?.paymentMethods || []}
                      layout="vertical"
                      margin={{ top: 5, right: 30, left: 100, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis
                        type="number"
                        tickFormatter={(value) => {
                          return new Intl.NumberFormat('en-AE', {
                            style: 'currency',
                            currency: 'AED',
                            notation: 'compact',
                            compactDisplay: 'short',
                          }).format(value);
                        }}
                      />
                      <YAxis
                        type="category"
                        dataKey="method"
                        width={100}
                      />
                      <Tooltip
                        formatter={(value: number, name) => {
                          if (name === 'amount') {
                            return [formatCurrency(value), 'Amount'];
                          }
                          if (name === 'percentage') {
                            return [formatPercentage(value), 'Percentage'];
                          }
                          if (name === 'count') {
                            return [value, 'Orders'];
                          }
                          return [value, name];
                        }}
                        labelFormatter={formatTooltipDate}
                      />
                      <Legend />
                      <Bar
                        dataKey="amount"
                        name="Amount"
                        fill="#ffc658"
                        radius={[0, 4, 4, 0]}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>


      </Tabs>
    </div>
  );
}
