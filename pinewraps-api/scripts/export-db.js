/**
 * Database Export Script
 * 
 * This script helps export your Supabase database for local import into pgAdmin4
 * Run with: node scripts/export-db.js
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Get database URL from environment
const databaseUrl = process.env.DATABASE_URL || process.env.DIRECT_URL;

if (!databaseUrl) {
  console.error('Error: DATABASE_URL or DIRECT_URL not found in environment variables');
  process.exit(1);
}

// Parse the database URL to get connection details
const dbUrlRegex = /postgresql:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/([^?]+)/;
const matches = databaseUrl.match(dbUrlRegex);

if (!matches) {
  console.error('Error: Could not parse database URL. Make sure it follows the format: postgresql://user:password@host:port/dbname');
  process.exit(1);
}

const [, user, password, host, port, dbname] = matches;

// Create export directory if it doesn't exist
const exportDir = path.join(__dirname, '../db-export');
if (!fs.existsSync(exportDir)) {
  fs.mkdirSync(exportDir);
}

const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
const exportFilePath = path.join(exportDir, `${dbname}-${timestamp}.sql`);

console.log('Exporting database...');
console.log(`Host: ${host}`);
console.log(`Database: ${dbname}`);
console.log(`Output file: ${exportFilePath}`);

try {
  // Set environment variables for pg_dump
  const env = {
    ...process.env,
    PGPASSWORD: password
  };

  // Run pg_dump command
  execSync(
    `pg_dump -h ${host} -p ${port} -U ${user} -d ${dbname} -f "${exportFilePath}" --verbose --format=c`,
    { 
      env,
      stdio: 'inherit'
    }
  );

  console.log('\nDatabase export completed successfully!');
  console.log(`\nTo import into pgAdmin4:`);
  console.log('1. Open pgAdmin4');
  console.log('2. Create a new database or select an existing one');
  console.log('3. Right-click on the database and select "Restore..."');
  console.log('4. In the "Restore" dialog:');
  console.log(`   - Select the exported file: ${exportFilePath}`);
  console.log('   - Set Format to "Custom or tar"');
  console.log('   - Click "Restore"');
} catch (error) {
  console.error('Error exporting database:', error.message);
  console.error('\nTroubleshooting:');
  console.error('1. Make sure pg_dump is installed and in your PATH');
  console.error('2. Check your database credentials');
  console.error('3. Ensure you have proper permissions to access the database');
  process.exit(1);
}
