/**
 * Utility functions for exporting data to CSV format
 */

/**
 * Convert an array of objects to CSV format
 * @param data Array of objects to convert
 * @param headers Optional custom headers (if not provided, will use object keys)
 * @returns CSV formatted string
 */
export function objectsToCsv(data: any[], headers?: string[]): string {
  if (!data || data.length === 0) {
    return '';
  }

  // Use provided headers or extract from first object
  const csvHeaders = headers || Object.keys(data[0]);
  
  // Create header row
  let csv = csvHeaders.join(',') + '\n';
  
  // Add data rows
  data.forEach(item => {
    const row = csvHeaders.map(header => {
      // Handle values that need quotes (commas, quotes, newlines)
      const value = item[header] !== undefined ? item[header] : '';
      const valueStr = String(value);
      
      if (valueStr.includes(',') || valueStr.includes('"') || valueStr.includes('\n')) {
        // Escape quotes and wrap in quotes
        return `"${valueStr.replace(/"/g, '""')}"`;
      }
      return valueStr;
    });
    
    csv += row.join(',') + '\n';
  });
  
  return csv;
}

/**
 * Format a value for CSV export
 * @param value The value to format
 * @returns Formatted value as string
 */
export function formatCsvValue(value: any): string {
  if (value === null || value === undefined) {
    return '';
  }
  
  // Format numbers with 2 decimal places
  if (typeof value === 'number') {
    return value.toFixed(2);
  }
  
  // Format dates
  if (value instanceof Date) {
    return value.toISOString().split('T')[0];
  }
  
  return String(value);
}

/**
 * Download data as a CSV file
 * @param data The data to export (array of objects)
 * @param filename The name of the file to download
 * @param headers Optional custom headers
 */
export function downloadCsv(data: any[], filename: string, headers?: string[]): void {
  const csv = objectsToCsv(data, headers);
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.setAttribute('href', url);
  link.setAttribute('download', filename);
  link.style.visibility = 'hidden';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

/**
 * Format an object for CSV export
 * @param obj The object to format
 * @returns A new object with formatted values
 */
export function formatObjectForCsv(obj: Record<string, any>): Record<string, string> {
  const result: Record<string, string> = {};
  
  for (const [key, value] of Object.entries(obj)) {
    result[key] = formatCsvValue(value);
  }
  
  return result;
}

/**
 * Create a multi-section CSV file
 * @param sections An array of section objects with title and data
 * @param filename The name of the file to download
 */
export function createMultiSectionCsv(
  sections: Array<{ title: string; data: any[]; headers?: string[] }>,
  filename: string
): void {
  let csvContent = '';
  
  sections.forEach((section, index) => {
    // Add a section separator if not the first section
    if (index > 0) {
      csvContent += '\n\n';
    }
    
    // Add section title
    csvContent += `${section.title}\n`;
    
    // Add section data
    csvContent += objectsToCsv(
      section.data.map(item => formatObjectForCsv(item)),
      section.headers
    );
  });
  
  // Create and download the file
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.setAttribute('href', url);
  link.setAttribute('download', filename);
  link.style.visibility = 'hidden';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}
