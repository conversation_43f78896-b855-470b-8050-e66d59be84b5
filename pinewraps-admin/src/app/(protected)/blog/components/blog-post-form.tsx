'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Heading } from '@/components/ui/heading';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import LinkExtension from '@tiptap/extension-link';
import ImageExtension from '@tiptap/extension-image';
import UnderlineExtension from '@tiptap/extension-underline';
import TextAlign from '@tiptap/extension-text-align';
import Highlight from '@tiptap/extension-highlight';
import {
  Bold, Italic, Underline, Strikethrough,
  Heading1, Heading2, Heading3, Heading4, Heading5, Heading6,
  List, ListOrdered, Quote, <PERSON>, Link as LinkIcon, Image as ImageIcon,
  AlignLeft, AlignCenter, AlignRight, AlignJustify,
  Undo, Redo, Link2 as UnlinkIcon,
  PilcrowSquare, Indent, Outdent,
  Type, Trash2, FileCode, Eye,
  RotateCcw, RotateCw, Maximize, Minimize,
  Highlighter, Table, CheckSquare
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { useQuery } from '@tanstack/react-query';
import api from '@/lib/api';
import { toast } from '@/lib/toast';
import { MultiSelect } from '@/components/ui/multi-select';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface RichTextEditorProps {
  content: string;
  onChange: (content: string) => void;
}

const RichTextEditor = ({ content, onChange }: RichTextEditorProps) => {
  console.log('RichTextEditor received content:', content);
  
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: { levels: [1, 2, 3, 4, 5, 6] },
        codeBlock: { HTMLAttributes: { class: 'rounded-md bg-muted/80 p-3 font-mono text-sm' } },
        blockquote: { HTMLAttributes: { class: 'border-l-4 border-primary/30 pl-4 italic' } },
        bulletList: { HTMLAttributes: { class: 'list-disc list-outside' } },
        orderedList: { HTMLAttributes: { class: 'list-decimal list-outside' } },
        paragraph: { HTMLAttributes: { class: 'text-base leading-relaxed' } },
      }),
      UnderlineExtension,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Highlight.configure({
        HTMLAttributes: {
          class: 'bg-yellow-200 dark:bg-yellow-800 px-1 rounded',
        },
      }),
      LinkExtension.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-primary underline decoration-primary/30 underline-offset-4 hover:decoration-primary transition-all duration-200',
          rel: 'noopener noreferrer',
          target: '_blank',
        },
      }),
      ImageExtension.configure({
        allowBase64: true,
        inline: true,
        HTMLAttributes: {
          class: 'rounded-lg max-w-full shadow-md hover:shadow-lg transition-shadow duration-200',
        },
      }),
    ],
    content: content || '<p></p>', // Ensure there's always at least an empty paragraph
    autofocus: 'end',
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML());
    },
    editorProps: {
      attributes: {
        class: 'prose prose-lg prose-slate dark:prose-invert prose-headings:font-semibold prose-a:text-primary prose-p:text-base prose-img:rounded-lg focus:outline-none max-w-none min-h-[500px] p-4',
        spellcheck: 'true',
      },
      handleClick: (view, pos, event) => {
        // This ensures the editor gets focus when clicking anywhere in the editor area
        view.focus();
        return false; // Let Tiptap handle the click normally after focusing
      },
    },
  });

  // Update editor content when it changes from parent
  useEffect(() => {
    if (editor && content && editor.getHTML() !== content) {
      console.log('Updating editor content from prop:', content);
      editor.commands.setContent(content);
    }
  }, [editor, content]);

  if (!editor) {
    return <div className="flex items-center justify-center h-[400px] border rounded-md bg-muted/20">
      <div className="animate-pulse">Loading editor...</div>
    </div>;
  }

  // Helper function for toolbar buttons
  const ToolbarButton = ({ 
    onClick, 
    isActive = false, 
    title, 
    icon: Icon,
    disabled = false
  }: { 
    onClick: () => void, 
    isActive?: boolean, 
    title: string, 
    icon: React.ElementType,
    disabled?: boolean
  }) => {
    // Wrap the onClick handler to ensure the editor is focused first
    const handleClick = (e: React.MouseEvent) => {
      e.preventDefault();
      // Focus the editor first, then apply the formatting
      editor.commands.focus();
      // Use setTimeout to ensure focus is applied before the command
      setTimeout(() => {
        onClick();
      }, 0);
    };

    return (
      <button
        type="button"
        onMouseDown={handleClick}
        className={`p-1.5 rounded-md transition-all duration-200 ${isActive ? 'bg-primary text-primary-foreground shadow-sm' : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'} ${disabled ? 'opacity-40 cursor-not-allowed' : 'hover:scale-105'}`}
        title={title}
        disabled={disabled}
      >
        <Icon className="h-4 w-4" />
      </button>
    );
  };

  // Group of toolbar buttons
  const ToolbarGroup = ({ children }: { children: React.ReactNode }) => (
    <div className="flex items-center gap-1 border-r border-border/30 pr-2 mr-2 last:border-r-0 last:pr-0 last:mr-0">
      {children}
    </div>
  );

  const addImage = () => {
    // Focus the editor first
    editor.commands.focus();
    const url = window.prompt('Enter the image URL');
    if (url) {
      editor.chain().setImage({ src: url }).run();
    }
  };

  const addLink = () => {
    // Focus the editor first
    editor.commands.focus();
    const url = window.prompt('Enter the link URL');
    if (url) {
      // If text is selected, apply link to selection, otherwise insert the URL as a link
      if (editor.state.selection.empty) {
        editor.chain().insertContent(`<a href="${url}">${url}</a>`).run();
      } else {
        editor.chain().setLink({ href: url }).run();
      }
    }
  };

  const toggleFullscreen = () => {
    const editorElement = document.querySelector('.editor-container');
    if (editorElement) {
      if (document.fullscreenElement) {
        document.exitFullscreen();
      } else {
        editorElement.requestFullscreen();
      }
    }
    // Focus the editor after toggling fullscreen
    setTimeout(() => editor.commands.focus(), 100);
  };

  const clearContent = () => {
    if (window.confirm('Are you sure you want to clear all content? This cannot be undone.')) {
      editor.commands.clearContent(true);
      // Focus the editor after clearing
      editor.commands.focus();
    }
  };

  const showHtml = () => {
    const html = editor.getHTML();
    alert(html);
    // Focus the editor after showing HTML
    setTimeout(() => editor.commands.focus(), 100);
  };

  return (
    <div className="flex flex-col editor-container border rounded-lg shadow-sm bg-card relative">
      {/* Sticky Main toolbar */}
      <div className="sticky top-0 z-[100] bg-background border-b shadow-md p-3 flex flex-wrap gap-1 rounded-t-lg">


        <ToolbarGroup>
          <ToolbarButton
            onClick={() => editor.chain().focus().undo().run()}
            title="Undo"
            icon={Undo}
            disabled={!editor.can().undo()}
          />
          <ToolbarButton
            onClick={() => editor.chain().focus().redo().run()}
            title="Redo"
            icon={Redo}
            disabled={!editor.can().redo()}
          />
        </ToolbarGroup>
        
        <ToolbarGroup>
          <ToolbarButton
            onClick={() => editor.chain().focus().setParagraph().run()}
            isActive={editor.isActive('paragraph')}
            title="Paragraph"
            icon={Type}
          />
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
            isActive={editor.isActive('heading', { level: 1 })}
            title="Heading 1"
            icon={Heading1}
          />
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
            isActive={editor.isActive('heading', { level: 2 })}
            title="Heading 2"
            icon={Heading2}
          />
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
            isActive={editor.isActive('heading', { level: 3 })}
            title="Heading 3"
            icon={Heading3}
          />
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleHeading({ level: 4 }).run()}
            isActive={editor.isActive('heading', { level: 4 })}
            title="Heading 4"
            icon={Heading4}
          />
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleHeading({ level: 5 }).run()}
            isActive={editor.isActive('heading', { level: 5 })}
            title="Heading 5"
            icon={Heading5}
          />
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleHeading({ level: 6 }).run()}
            isActive={editor.isActive('heading', { level: 6 })}
            title="Heading 6"
            icon={Heading6}
          />
        </ToolbarGroup>
        
        <ToolbarGroup>
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleBold().run()}
            isActive={editor.isActive('bold')}
            title="Bold"
            icon={Bold}
          />
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleItalic().run()}
            isActive={editor.isActive('italic')}
            title="Italic"
            icon={Italic}
          />
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleUnderline().run()}
            isActive={editor.isActive('underline')}
            title="Underline"
            icon={Underline}
          />
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleStrike().run()}
            isActive={editor.isActive('strike')}
            title="Strikethrough"
            icon={Strikethrough}
          />
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleHighlight().run()}
            isActive={editor.isActive('highlight')}
            title="Highlight"
            icon={Highlighter}
          />
        </ToolbarGroup>

        <ToolbarGroup>
          <ToolbarButton
            onClick={() => editor.chain().focus().setTextAlign('left').run()}
            isActive={editor.isActive({ textAlign: 'left' })}
            title="Align Left"
            icon={AlignLeft}
          />
          <ToolbarButton
            onClick={() => editor.chain().focus().setTextAlign('center').run()}
            isActive={editor.isActive({ textAlign: 'center' })}
            title="Align Center"
            icon={AlignCenter}
          />
          <ToolbarButton
            onClick={() => editor.chain().focus().setTextAlign('right').run()}
            isActive={editor.isActive({ textAlign: 'right' })}
            title="Align Right"
            icon={AlignRight}
          />
          <ToolbarButton
            onClick={() => editor.chain().focus().setTextAlign('justify').run()}
            isActive={editor.isActive({ textAlign: 'justify' })}
            title="Justify"
            icon={AlignJustify}
          />
        </ToolbarGroup>

        <ToolbarGroup>
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleBulletList().run()}
            isActive={editor.isActive('bulletList')}
            title="Bullet List"
            icon={List}
          />
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleOrderedList().run()}
            isActive={editor.isActive('orderedList')}
            title="Numbered List"
            icon={ListOrdered}
          />
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleBlockquote().run()}
            isActive={editor.isActive('blockquote')}
            title="Quote"
            icon={Quote}
          />
        </ToolbarGroup>
        
        <ToolbarGroup>
          <ToolbarButton
            onClick={addLink}
            isActive={editor.isActive('link')}
            title="Insert Link"
            icon={LinkIcon}
          />
          <ToolbarButton
            onClick={() => editor.chain().focus().unsetLink().run()}
            title="Remove Link"
            icon={UnlinkIcon}
            disabled={!editor.isActive('link')}
          />
          <ToolbarButton
            onClick={addImage}
            title="Insert Image"
            icon={ImageIcon}
          />
        </ToolbarGroup>

        <ToolbarGroup>
          <ToolbarButton
            onClick={toggleFullscreen}
            title="Toggle Fullscreen"
            icon={Maximize}
          />
          <ToolbarButton
            onClick={showHtml}
            title="View HTML"
            icon={FileCode}
          />
          <ToolbarButton
            onClick={clearContent}
            title="Clear Content"
            icon={Trash2}
          />
        </ToolbarGroup>
      </div>
      
      {/* Content area */}
      <div className="flex-1 overflow-hidden">
        <div className="h-[600px] overflow-y-auto bg-card cursor-text p-6" onClick={() => editor.chain().focus().run()}>
          <EditorContent 
            editor={editor} 
            className="px-6 py-4 h-full w-full prose-sm sm:prose lg:prose-lg xl:prose-xl mx-auto" 
          />
        </div>
      </div>
      
      {/* Status bar */}
      <div className="bg-muted/50 backdrop-blur supports-[backdrop-filter]:bg-background/60 px-4 py-2 text-xs text-muted-foreground border-t flex justify-between items-center rounded-b-lg">
        <div className="flex items-center gap-3">
          <span>{editor.getText().split(/\s+/).filter(Boolean).length} words</span>
          <span>{editor.storage.characterCount?.characters() || editor.getText().length} characters</span>
        </div>
        <div>
          <span className="font-medium">Enhanced Editor</span>
        </div>
      </div>
    </div>
  );
};

interface BlogPostFormProps {
  postId?: string; // Optional - if provided, we're editing an existing post
  title?: string;
  defaultTitle?: string;
  defaultContent?: string;
  onCancel?: () => void;
}

export default function BlogPostForm({ 
  postId, 
  title = postId ? 'Edit Blog Post' : 'Create New Blog Post',
  defaultTitle = '',
  defaultContent = '',
  onCancel 
}: BlogPostFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    title: defaultTitle,
    slug: '',
    content: defaultContent,
    excerpt: '',
    featuredImage: '',
    metaTitle: '',
    metaDescription: '',
    status: postId ? 'PUBLISHED' : 'DRAFT' as 'DRAFT' | 'PUBLISHED' | 'ARCHIVED',
    categoryIds: [] as string[],
  });

  // Fetch post data if we're editing
  const { data: postData, isLoading: isLoadingPost } = useQuery({
    queryKey: ['blog-post', postId],
    queryFn: async () => {
      if (!postId) return null;
      const response = await api.get(`/api/blog/posts/${postId}`);
      console.log('API Response:', response.data);
      return response.data;
    },
    enabled: !!postId, // Only run this query if postId is provided
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnMount: false, // Prevent refetch on component mount
    staleTime: Infinity, // Consider data fresh forever
  });

  // Fetch categories
  const { data: categoriesData } = useQuery({
    queryKey: ['blog-categories'],
    queryFn: async () => {
      const response = await api.get('/api/blog/categories');
      return response.data;
    },
    refetchOnWindowFocus: false,
    staleTime: 300000, // Consider categories fresh for 5 minutes
  });

  // Function to generate URL-friendly slugs
  const generateSlug = (text: string): string => {
    return text
      .toString()
      .toLowerCase()
      .trim()
      .replace(/\s+/g, '-')        // Replace spaces with -
      .replace(/&/g, '-and-')      // Replace & with 'and'
      .replace(/[^\w\-]+/g, '')    // Remove all non-word characters
      .replace(/\-\-+/g, '-')      // Replace multiple - with single -
      .replace(/^-+/, '')          // Trim - from start of text
      .replace(/-+$/, '');         // Trim - from end of text
  };

  // Update form data when post data is loaded
  useEffect(() => {
    if (postData?.data) {
      const post = postData.data;
      console.log('Loaded post data:', post); // Debug log
      
      // Extract category IDs
      const categoryIds = Array.isArray(post.categories)
        ? post.categories.map((cat: any) => cat.id)
        : [];
      
      console.log('Setting form data with:', {
        title: post.title,
        content: post.content,
        status: post.status,
        categoryIds
      });
      
      // Update form data with post data
      setFormData({
        title: post.title || '',
        slug: post.slug || generateSlug(post.title),
        content: post.content || '',
        excerpt: post.excerpt || '',
        featuredImage: post.featuredImage || '',
        metaTitle: post.metaTitle || '',
        metaDescription: post.metaDescription || '',
        status: post.status || 'DRAFT',
        categoryIds: categoryIds,
      });
    }
  }, [postData, defaultTitle, defaultContent]);

  const categories = categoriesData?.data || [];
  const categoryOptions = categories.map((category: any) => ({
    label: category.name,
    value: category.id,
  }));

  const [slugManuallyEdited, setSlugManuallyEdited] = useState(!!postId); // If editing, consider slug as manually set

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => {
      // Only auto-generate slug for new posts and if slug hasn't been manually edited
      if (name === 'title' && !postId && !slugManuallyEdited && (!prev.slug || prev.slug === generateSlug(prev.title))) {
        return { ...prev, [name]: value, slug: generateSlug(value) };
      }
      return { ...prev, [name]: value };
    });
  };

  const handleSlugChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setFormData((prev) => ({ ...prev, slug: value }));
    setSlugManuallyEdited(true);
  };

  type BlogPostStatus = 'DRAFT' | 'PUBLISHED' | 'ARCHIVED';

  const handleStatusChange = (value: BlogPostStatus) => {
    setFormData((prev) => ({ ...prev, status: value }));
  };

  const handleCategoryChange = (value: string[]) => {
    setFormData((prev) => ({ ...prev, categoryIds: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const submitData = {
        ...formData,
        // Always ensure slug is set, even for new posts
        slug: formData.slug || generateSlug(formData.title)
      };

      if (postId) {
        // Update existing post
        console.log('Submitting update with data:', submitData);
        await api.put(`/api/blog/posts/${postId}`, submitData);
        toast.crud.updated('Blog Post');
      } else {
        // Create new post
        await api.post('/api/blog/posts', submitData);
        toast.crud.created('Blog Post');
      }
      router.push('/blog');
    } catch (error) {
      console.error('Error saving blog post:', error);
      toast.crud.createError('Blog Post', error?.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (postId && isLoadingPost) {
    return (
      <div className="flex-col">
        <div className="flex-1 space-y-4 p-8 pt-6">
          <div className="flex items-center justify-between">
            <Heading
              title="Loading..."
              description="Please wait while we load the blog post"
            />
          </div>
          <div className="h-[600px] flex items-center justify-center">
            <div className="animate-pulse text-muted-foreground">Loading post data...</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-col">
      <div className="flex-1 space-y-4 p-8 pt-6">
        <div className="flex items-center justify-between">
          <Heading
            title={title}
            description={postId ? "Edit an existing blog post" : "Create a new blog post for your website"}
          />
          <Button
            variant="outline"
            onClick={onCancel || (() => router.push('/blog'))}
          >
            Cancel
          </Button>
        </div>
        <Separator />

        <form onSubmit={handleSubmit} className="space-y-8 h-full">
          <div className="grid grid-cols-1 gap-8 md:grid-cols-3 h-full">
            {/* Main content - 2/3 width */}
            <div className="space-y-6 md:col-span-2 min-h-0 relative">
              <div className="sticky top-0 z-50 bg-background pt-6 -mt-6">
              <div className="space-y-2">
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  placeholder="Enter post title"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="slug">URL Slug</Label>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-muted-foreground">/blog/</span>
                  <Input
                    id="slug"
                    name="slug"
                    value={formData.slug}
                    onChange={handleSlugChange}
                    placeholder="enter-post-slug"
                    className="flex-1"
                    required
                  />
                </div>
                <p className="text-sm text-muted-foreground">The URL slug for this blog post. Leave empty to auto-generate from title.</p>
              </div>
              </div>

              <div className="space-y-2 overflow-visible relative">
                <Label htmlFor="content">Content</Label>
                <div className="overflow-visible relative z-10">
                  <RichTextEditor
                    content={formData.content}
                    onChange={(newContent) => setFormData(prev => ({ ...prev, content: newContent }))}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="excerpt">Excerpt</Label>
                <Textarea
                  id="excerpt"
                  name="excerpt"
                  value={formData.excerpt}
                  onChange={handleInputChange}
                  placeholder="Enter a short excerpt for this post"
                  rows={3}
                />
              </div>

              {/* SEO Settings */}
              <div className="space-y-4 border rounded-md p-4 bg-muted/30">
                <h3 className="text-lg font-medium">SEO Settings</h3>
                <div className="space-y-2">
                  <Label htmlFor="metaTitle">Meta Title</Label>
                  <Input
                    id="metaTitle"
                    name="metaTitle"
                    value={formData.metaTitle}
                    onChange={handleInputChange}
                    placeholder="Enter meta title"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="metaDescription">Meta Description</Label>
                  <Textarea
                    id="metaDescription"
                    name="metaDescription"
                    value={formData.metaDescription}
                    onChange={handleInputChange}
                    placeholder="Enter meta description"
                    rows={3}
                  />
                </div>
              </div>
            </div>

            {/* Sidebar - 1/3 width */}
            <div className="space-y-6">
              <Card>
                <CardContent className="pt-6">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="status">Status</Label>
                      <Select
                        value={formData.status}
                        onValueChange={handleStatusChange}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="DRAFT">Draft</SelectItem>
                          <SelectItem value="PUBLISHED">Published</SelectItem>
                          <SelectItem value="ARCHIVED">Archived</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="categories">Categories</Label>
                      <MultiSelect
                        selected={formData.categoryIds}
                        options={categoryOptions}
                        onChange={handleCategoryChange}
                        placeholder="Select categories"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="featuredImage">Featured Image URL</Label>
                      <Input
                        id="featuredImage"
                        name="featuredImage"
                        value={formData.featuredImage}
                        onChange={handleInputChange}
                        placeholder="Enter image URL"
                      />
                      {formData.featuredImage && (
                        <div className="mt-2 rounded-md overflow-hidden border">
                          <img
                            src={formData.featuredImage}
                            alt="Featured"
                            className="w-full h-auto object-cover"
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Button
                type="submit"
                className="w-full"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : postId ? 'Update Post' : 'Save Post'}
              </Button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
