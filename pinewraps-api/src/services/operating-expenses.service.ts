import { prisma } from '../lib/prisma';
import { ExpenseStatus, Prisma } from '@prisma/client';
import {
  CreateExpenseCategoryDto,
  UpdateExpenseCategoryDto,
  CreateOperatingExpenseDto,
  UpdateOperatingExpenseDto,
  ExpenseQueryParams,
  ExpenseListResponse,
  ExpenseCategoryWithStats,
  ExpenseStats
} from '../types/operating-expenses.types';

export class OperatingExpensesService {
  // Expense Categories
  async createExpenseCategory(data: CreateExpenseCategoryDto) {
    return await prisma.expenseCategory.create({
      data: {
        name: data.name,
        description: data.description,
        color: data.color,
      },
    });
  }

  async getExpenseCategories(includeStats = false): Promise<ExpenseCategoryWithStats[]> {
    const categories = await prisma.expenseCategory.findMany({
      where: { isActive: true },
      include: {
        _count: includeStats ? { select: { expenses: true } } : false,
        expenses: includeStats ? {
          select: { amount: true },
          where: { status: { not: ExpenseStatus.CANCELLED } }
        } : false,
      },
      orderBy: { name: 'asc' },
    });

    if (includeStats) {
      return categories.map(category => ({
        ...category,
        totalAmount: category.expenses?.reduce((sum, expense) => sum + Number(expense.amount), 0) || 0,
        expenses: undefined, // Remove the expenses array from response
      })) as ExpenseCategoryWithStats[];
    }

    return categories as ExpenseCategoryWithStats[];
  }

  async getExpenseCategoryById(id: string) {
    return await prisma.expenseCategory.findUnique({
      where: { id },
      include: {
        _count: { select: { expenses: true } },
      },
    });
  }

  async updateExpenseCategory(id: string, data: UpdateExpenseCategoryDto) {
    return await prisma.expenseCategory.update({
      where: { id },
      data,
    });
  }

  async deleteExpenseCategory(id: string) {
    // Check if category has expenses
    const expenseCount = await prisma.operatingExpense.count({
      where: { categoryId: id },
    });

    if (expenseCount > 0) {
      // Soft delete by setting isActive to false
      return await prisma.expenseCategory.update({
        where: { id },
        data: { isActive: false },
      });
    } else {
      // Hard delete if no expenses
      return await prisma.expenseCategory.delete({
        where: { id },
      });
    }
  }

  // Operating Expenses
  async createOperatingExpense(data: CreateOperatingExpenseDto, createdById: string) {
    return await prisma.operatingExpense.create({
      data: {
        invoiceNumber: data.invoiceNumber,
        companyId: data.companyId,
        amount: data.amount,
        currency: data.currency || 'AED',
        invoiceDate: new Date(data.invoiceDate),
        dueDate: data.dueDate ? new Date(data.dueDate) : null,
        categoryId: data.categoryId,
        description: data.description,
        bankReference: data.bankReference,
        attachments: data.attachments || [],
        createdById,
      },
      include: {
        category: { select: { id: true, name: true, color: true } },
        company: true,
        createdBy: { select: { id: true, firstName: true, lastName: true } },
      },
    });
  }

  async getOperatingExpenses(params: ExpenseQueryParams): Promise<ExpenseListResponse> {
    const {
      page = 1,
      limit = 20,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      search,
      status,
      categoryId,
      companyId,
      startDate,
      endDate,
      minAmount,
      maxAmount,
    } = params;

    const skip = (page - 1) * limit;

    // Build where clause
    const where: Prisma.OperatingExpenseWhereInput = {};

    if (search) {
      where.OR = [
        { invoiceNumber: { contains: search, mode: 'insensitive' } },
        { company: { name: { contains: search, mode: 'insensitive' } } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (status) {
      where.status = status;
    }

    if (categoryId) {
      where.categoryId = categoryId;
    }

    if (companyId) {
      where.companyId = companyId;
    }

    if (startDate || endDate) {
      where.invoiceDate = {};
      if (startDate) where.invoiceDate.gte = new Date(startDate);
      if (endDate) where.invoiceDate.lte = new Date(endDate);
    }

    if (minAmount !== undefined || maxAmount !== undefined) {
      where.amount = {};
      if (minAmount !== undefined) where.amount.gte = minAmount;
      if (maxAmount !== undefined) where.amount.lte = maxAmount;
    }

    // Execute queries
    const [expenses, total] = await Promise.all([
      prisma.operatingExpense.findMany({
        where,
        include: {
          category: { select: { id: true, name: true, color: true } },
          company: true,
          createdBy: { select: { id: true, firstName: true, lastName: true } },
        },
        orderBy: { [sortBy]: sortOrder },
        skip,
        take: limit,
      }),
      prisma.operatingExpense.count({ where }),
    ]);

    // Convert Decimal amounts to numbers for the response
    const expensesWithNumbers = expenses.map(expense => ({
      ...expense,
      amount: Number(expense.amount),
    }));

    return {
      expenses: expensesWithNumbers,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getOperatingExpenseById(id: string) {
    return await prisma.operatingExpense.findUnique({
      where: { id },
      include: {
        category: { select: { id: true, name: true, color: true } },
        createdBy: { select: { id: true, firstName: true, lastName: true } },
      },
    });
  }

  async updateOperatingExpense(id: string, data: UpdateOperatingExpenseDto) {
    const updateData: Prisma.OperatingExpenseUpdateInput = {};

    if (data.invoiceNumber) updateData.invoiceNumber = data.invoiceNumber;
    if (data.companyId) {
      updateData.company = {
        connect: { id: data.companyId }
      };
    }
    if (data.amount !== undefined) updateData.amount = data.amount;
    if (data.currency) updateData.currency = data.currency;
    if (data.invoiceDate) updateData.invoiceDate = new Date(data.invoiceDate);
    if (data.dueDate) updateData.dueDate = new Date(data.dueDate);
    if (data.paidDate) updateData.paidDate = new Date(data.paidDate);
    if (data.status) updateData.status = data.status;
    if (data.bankReference) updateData.bankReference = data.bankReference;
    if (data.paymentMethod) updateData.paymentMethod = data.paymentMethod;
    if (data.categoryId) {
      updateData.category = {
        connect: { id: data.categoryId }
      };
    }
    if (data.description !== undefined) updateData.description = data.description;
    if (data.attachments) updateData.attachments = data.attachments;

    return await prisma.operatingExpense.update({
      where: { id },
      data: updateData,
      include: {
        category: { select: { id: true, name: true, color: true } },
        createdBy: { select: { id: true, firstName: true, lastName: true } },
      },
    });
  }

  async deleteOperatingExpense(id: string) {
    return await prisma.operatingExpense.delete({
      where: { id },
    });
  }

  async markExpenseAsPaid(id: string, paymentData: {
    paidDate: string;
    bankReference?: string;
    paymentMethod?: string;
  }) {
    return await this.updateOperatingExpense(id, {
      status: ExpenseStatus.PAID,
      paidDate: paymentData.paidDate,
      bankReference: paymentData.bankReference,
      paymentMethod: paymentData.paymentMethod,
    });
  }

  async getExpenseStats(): Promise<ExpenseStats> {
    // Get basic stats
    const [totalStats, categoryStats, monthlyStats] = await Promise.all([
      // Total stats
      prisma.operatingExpense.aggregate({
        _count: true,
        _sum: { amount: true },
        where: { status: { not: ExpenseStatus.CANCELLED } },
      }),

      // Category breakdown
      prisma.operatingExpense.groupBy({
        by: ['categoryId'],
        _count: true,
        _sum: { amount: true },
        where: { status: { not: ExpenseStatus.CANCELLED } },
      }),

      // Monthly trend (last 12 months)
      prisma.$queryRaw`
        SELECT 
          DATE_TRUNC('month', "invoiceDate") as month,
          SUM("amount") as "totalAmount",
          COUNT(*) as count
        FROM "OperatingExpense"
        WHERE "invoiceDate" >= NOW() - INTERVAL '12 months'
          AND "status" != 'CANCELLED'
        GROUP BY DATE_TRUNC('month', "invoiceDate")
        ORDER BY month DESC
      `,
    ]);

    // Get status-specific amounts
    const statusStats = await prisma.operatingExpense.groupBy({
      by: ['status'],
      _sum: { amount: true },
      _count: true,
    });

    // Get category names
    const categories = await prisma.expenseCategory.findMany({
      select: { id: true, name: true },
    });

    const categoryMap = new Map(categories.map(cat => [cat.id, cat.name]));

    // Calculate amounts by status
    const dueAmount = statusStats.find(s => s.status === ExpenseStatus.DUE)?._sum.amount || 0;
    const paidAmount = statusStats.find(s => s.status === ExpenseStatus.PAID)?._sum.amount || 0;
    const overdueAmount = statusStats.find(s => s.status === ExpenseStatus.OVERDUE)?._sum.amount || 0;
    const overdueCount = statusStats.find(s => s.status === ExpenseStatus.OVERDUE)?._count || 0;

    return {
      totalExpenses: totalStats._count,
      totalAmount: Number(totalStats._sum.amount) || 0,
      dueAmount: Number(dueAmount) || 0,
      paidAmount: Number(paidAmount) || 0,
      overdueAmount: Number(overdueAmount) || 0,
      overdueCount,
      categoryBreakdown: categoryStats.map(stat => ({
        categoryId: stat.categoryId,
        categoryName: categoryMap.get(stat.categoryId) || 'Unknown',
        totalAmount: Number(stat._sum.amount) || 0,
        count: stat._count,
      })),
      monthlyTrend: (monthlyStats as any[]).map(stat => ({
        month: stat.month,
        totalAmount: Number(stat.totalAmount) || 0,
        count: Number(stat.count) || 0,
      })),
    };
  }
}

export const operatingExpensesService = new OperatingExpensesService();
