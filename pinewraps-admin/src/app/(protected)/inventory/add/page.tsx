'use client';

import { useRouter } from 'next/navigation';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Package2 } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { getToken } from '@/lib/get-token';
import { auth } from '@/lib/firebase';
import { API_URL } from '@/lib/config';
import { InventoryForm } from '@/components/inventory/InventoryForm';
import { useState, useEffect } from 'react';

export default function AddInventoryItemPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState<{ id: string; name: string; isActive: boolean }[]>([]);

  useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async () => {
    try {
      const token = await getToken();
      const response = await fetch(`${API_URL}/api/inventory-categories`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      if (!response.ok) throw new Error('Failed to load categories');
      const data = await response.json();
      
      // Ensure each category has the isActive property
      const categoriesWithIsActive = data.data.categories.map((category: any) => ({
        ...category,
        isActive: category.isActive !== undefined ? category.isActive : true,
      }));
      
      setCategories(categoriesWithIsActive);
    } catch (error) {
      console.error('Error loading categories:', error);
      toast.error('Failed to load categories');
    }
  };

  const handleSubmit = async (formData: any) => {
    setLoading(true);

    try {
      const token = await getToken(true);
      if (!token) {
        toast.error('Authentication failed. Please log in again.');
        router.push('/login');
        return;
      }

      const response = await fetch(`${API_URL}/api/inventory-items`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create inventory item');
      }

      toast.success('Inventory item created successfully');
      router.push('/inventory');
      router.refresh();
    } catch (error: any) {
      console.error('Error creating inventory item:', error);
      toast.error(error.message || 'Failed to create inventory item');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-10">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-bold flex items-center gap-2">
            <Package2 className="h-6 w-6" />
            Add Inventory Item
          </CardTitle>
        </CardHeader>
        <CardContent>
          <InventoryForm
            categories={categories}
            onSubmit={handleSubmit}
            onCancel={() => router.back()}
          />
        </CardContent>
      </Card>
    </div>
  );
}
