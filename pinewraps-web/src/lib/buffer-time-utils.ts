import { CartItem } from '@/contexts/cart-context';

/**
 * Convert buffer time to hours for calculation (legacy function)
 * Note: This is kept for backward compatibility but new logic uses calendar days
 */
export function getBufferTimeInHours(value: number, unit: 'HOURS' | 'DAYS'): number {
  return unit === 'DAYS' ? value * 24 : value;
}

/**
 * Calculate minimum date based on cart items' buffer times
 * For DAYS: Adds full calendar days (not working hours)
 * For HOURS: Adds hours to current time
 */
export function calculateMinimumDate(cartItems: CartItem[]): Date {
  if (!cartItems || cartItems.length === 0) {
    return new Date();
  }

  const now = new Date();
  let maxMinimumDate = now;

  // Find the maximum minimum date among all cart items
  cartItems.forEach(item => {
    // For ready stock items, use current time
    if (item.isReadyStock) {
      return;
    }

    // Use product buffer time if available
    const bufferValue = item.bufferTimeValue ?? 0;
    const bufferUnit = item.bufferTimeUnit ?? 'HOURS';

    if (bufferValue === 0) {
      return;
    }

    let itemMinimumDate: Date;

    if (bufferUnit === 'DAYS') {
      // For days, add full calendar days (not working hours)
      itemMinimumDate = new Date(now);
      itemMinimumDate.setDate(itemMinimumDate.getDate() + bufferValue);
    } else {
      // For hours, add hours to current time
      itemMinimumDate = new Date(now.getTime() + bufferValue * 60 * 60 * 1000);
    }

    // Keep track of the latest minimum date
    if (itemMinimumDate > maxMinimumDate) {
      maxMinimumDate = itemMinimumDate;
    }
  });

  return maxMinimumDate;
}

/**
 * Calculate minimum pickup date considering working hours (9 AM to 9 PM)
 * If the calculated time falls outside working hours, move to next available slot
 */
export function calculateMinimumPickupDate(cartItems: CartItem[]): Date {
  const baseMinDate = calculateMinimumDate(cartItems);

  console.log('Buffer time calculation (using full calendar days for DAYS unit):', {
    cartItems: cartItems.map(item => ({
      name: item.name,
      isReadyStock: item.isReadyStock,
      bufferTimeValue: item.bufferTimeValue,
      bufferTimeUnit: item.bufferTimeUnit
    })),
    baseMinDate: baseMinDate.toISOString()
  });

  // Convert to Dubai timezone
  const dubaiTime = new Date(baseMinDate.toLocaleString('en-US', { timeZone: 'Asia/Dubai' }));

  const hour = dubaiTime.getHours();

  // If the time is before 9 AM, set to 9 AM same day
  if (hour < 9) {
    dubaiTime.setHours(9, 0, 0, 0);
  }
  // If the time is after 9 PM, move to 9 AM next day
  else if (hour >= 21) {
    dubaiTime.setDate(dubaiTime.getDate() + 1);
    dubaiTime.setHours(9, 0, 0, 0);
  }

  console.log('Final minimum pickup date:', dubaiTime.toISOString());

  return dubaiTime;
}

/**
 * Calculate minimum delivery date considering working hours and delivery constraints
 * Delivery is typically next day, but buffer time might push it further
 */
export function calculateMinimumDeliveryDate(cartItems: CartItem[]): Date {
  const baseMinDate = calculateMinimumDate(cartItems);

  // For delivery, minimum is tomorrow
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  tomorrow.setHours(0, 0, 0, 0);

  // Use the later of tomorrow or buffer time minimum
  const minDeliveryDate = baseMinDate > tomorrow ? baseMinDate : tomorrow;

  // Set to start of day for delivery date selection
  minDeliveryDate.setHours(0, 0, 0, 0);

  console.log('Delivery date calculation:', {
    baseMinDate: baseMinDate.toISOString(),
    tomorrow: tomorrow.toISOString(),
    finalMinDeliveryDate: minDeliveryDate.toISOString()
  });

  return minDeliveryDate;
}

/**
 * Get formatted buffer time display text
 */
export function getBufferTimeDisplayText(value: number, unit: 'HOURS' | 'DAYS'): string {
  if (value === 0) {
    return 'Available for immediate pickup';
  }
  
  const unitText = unit === 'HOURS' ? 'hour' : 'day';
  const pluralUnit = value === 1 ? unitText : `${unitText}s`;
  
  return `Preparation time: ${value} ${pluralUnit}`;
}

/**
 * Check if cart has only ready stock items
 */
export function hasOnlyReadyStockItems(cartItems: CartItem[]): boolean {
  if (!cartItems || cartItems.length === 0) {
    return false;
  }
  
  return cartItems.every(item => item.isReadyStock);
}

/**
 * Get the maximum buffer time among cart items for display
 */
export function getMaxBufferTimeDisplay(cartItems: CartItem[]): string {
  if (!cartItems || cartItems.length === 0) {
    return '';
  }

  if (hasOnlyReadyStockItems(cartItems)) {
    return 'All items available for immediate pickup';
  }

  let maxBufferValue = 0;
  let maxBufferUnit: 'HOURS' | 'DAYS' = 'HOURS';

  cartItems.forEach(item => {
    if (!item.isReadyStock) {
      const bufferValue = item.bufferTimeValue ?? 0;
      const bufferUnit = item.bufferTimeUnit ?? 'HOURS';

      // Compare buffer times properly
      if (bufferValue > 0) {
        // If current max is in hours and new item is in days, days take priority
        if (maxBufferUnit === 'HOURS' && bufferUnit === 'DAYS') {
          maxBufferValue = bufferValue;
          maxBufferUnit = bufferUnit;
        }
        // If both are in the same unit, take the larger value
        else if (maxBufferUnit === bufferUnit && bufferValue > maxBufferValue) {
          maxBufferValue = bufferValue;
          maxBufferUnit = bufferUnit;
        }
        // If current max is in days and new item is in hours, keep days unless it's 0
        else if (maxBufferUnit === 'DAYS' && bufferUnit === 'HOURS' && maxBufferValue === 0) {
          maxBufferValue = bufferValue;
          maxBufferUnit = bufferUnit;
        }
        // If no max set yet, use this item
        else if (maxBufferValue === 0) {
          maxBufferValue = bufferValue;
          maxBufferUnit = bufferUnit;
        }
      }
    }
  });

  if (maxBufferValue === 0) {
    return 'Available for immediate pickup';
  }

  return getBufferTimeDisplayText(maxBufferValue, maxBufferUnit);
}
