'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Plus, Download, Upload, Loader2, Search, ExternalLink } from 'lucide-react';
import { DataTable } from '@/components/ui/data-table';
import { Input } from '@/components/ui/input';
import { Heading } from '@/components/ui/heading';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { toast } from '@/components/ui/use-toast';
import api from '@/lib/api';

interface Redirect {
  id: string;
  fromPath: string;
  toPath: string;
  statusCode: number;
  type: 'MANUAL' | 'AUTO_SLUG' | 'AUTO_DELETE';
  reason?: string;
  createdAt: string;
  updatedAt: string;
  product?: { id: string; name: string; slug: string };
  blogPost?: { id: string; title: string; slug: string };
  collection?: { id: string; name: string; slug: string };
}

interface RedirectFormData {
  fromPath: string;
  toPath: string;
  statusCode: number;
  reason?: string;
}

export default function RedirectsPage() {
  const router = useRouter();
  const [redirects, setRedirects] = useState<Redirect[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<RedirectFormData>({
    fromPath: '',
    toPath: '',
    statusCode: 301,
    reason: ''
  });

  const fetchRedirects = async () => {
    try {
      setLoading(true);
      const response = await api.get('/api/redirects', {
        params: {
          search: searchQuery || undefined,
          limit: 100
        }
      });
      setRedirects(response.data.data || []);
    } catch (error) {
      console.error('Error fetching redirects:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch redirects',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRedirects();
  }, [searchQuery]);

  const handleCreateRedirect = async () => {
    if (!formData.fromPath || !formData.toPath) {
      toast({
        title: 'Error',
        description: 'From path and to path are required',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);
    try {
      await api.post('/api/redirects', formData);
      toast({
        title: 'Success',
        description: 'Redirect created successfully',
      });
      setIsCreateDialogOpen(false);
      setFormData({
        fromPath: '',
        toPath: '',
        statusCode: 301,
        reason: ''
      });
      fetchRedirects();
    } catch (error: any) {
      console.error('Error creating redirect:', error);
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to create redirect',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteRedirect = async (id: string) => {
    if (!confirm('Are you sure you want to delete this redirect?')) return;

    try {
      await api.delete(`/api/redirects/${id}`);
      toast({
        title: 'Success',
        description: 'Redirect deleted successfully',
      });
      fetchRedirects();
    } catch (error: any) {
      console.error('Error deleting redirect:', error);
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to delete redirect',
        variant: 'destructive',
      });
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'MANUAL':
        return 'bg-blue-100 text-blue-800';
      case 'AUTO_SLUG':
        return 'bg-green-100 text-green-800';
      case 'AUTO_DELETE':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusCodeColor = (code: number) => {
    if (code === 301) return 'bg-green-100 text-green-800';
    if (code === 302) return 'bg-yellow-100 text-yellow-800';
    return 'bg-gray-100 text-gray-800';
  };

  const getContentInfo = (redirect: Redirect) => {
    if (redirect.product) {
      return { type: 'Product', name: redirect.product.name, slug: redirect.product.slug };
    }
    if (redirect.blogPost) {
      return { type: 'Blog Post', name: redirect.blogPost.title, slug: redirect.blogPost.slug };
    }
    if (redirect.collection) {
      return { type: 'Collection', name: redirect.collection.name, slug: redirect.collection.slug };
    }
    return null;
  };

  const columns = [
    {
      accessorKey: 'fromPath',
      header: 'From Path',
      cell: ({ row }: any) => (
        <div className="font-mono text-sm">
          {row.getValue('fromPath')}
        </div>
      ),
    },
    {
      accessorKey: 'toPath',
      header: 'To Path',
      cell: ({ row }: any) => (
        <div className="font-mono text-sm">
          {row.getValue('toPath')}
        </div>
      ),
    },
    {
      accessorKey: 'statusCode',
      header: 'Status',
      cell: ({ row }: any) => {
        const code = row.getValue('statusCode') as number;
        return (
          <Badge className={getStatusCodeColor(code)}>
            {code}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'type',
      header: 'Type',
      cell: ({ row }: any) => {
        const type = row.getValue('type') as string;
        return (
          <Badge className={getTypeColor(type)}>
            {type.replace('_', ' ')}
          </Badge>
        );
      },
    },
    {
      id: 'content',
      header: 'Related Content',
      cell: ({ row }: any) => {
        const redirect = row.original as Redirect;
        const contentInfo = getContentInfo(redirect);
        
        if (!contentInfo) return <span className="text-gray-500">-</span>;
        
        return (
          <div className="space-y-1">
            <div className="text-sm font-medium">{contentInfo.type}</div>
            <div className="text-xs text-gray-600">{contentInfo.name}</div>
          </div>
        );
      },
    },
    {
      accessorKey: 'reason',
      header: 'Reason',
      cell: ({ row }: any) => {
        const reason = row.getValue('reason') as string;
        return reason ? (
          <div className="text-sm text-gray-600 max-w-xs truncate" title={reason}>
            {reason}
          </div>
        ) : (
          <span className="text-gray-500">-</span>
        );
      },
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }: any) => {
        const redirect = row.original as Redirect;
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open(redirect.toPath, '_blank')}
            >
              <ExternalLink className="h-4 w-4" />
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={() => handleDeleteRedirect(redirect.id)}
            >
              Delete
            </Button>
          </div>
        );
      },
    },
  ];

  return (
    <div className="flex-1 space-y-4">
      <div className="flex items-center justify-between">
        <Heading 
          title="URL Redirects" 
          description="Manage URL redirects for SEO and user experience" 
        />
        <div className="flex items-center gap-4">
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add Redirect
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Redirect</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="fromPath">From Path</Label>
                  <Input
                    id="fromPath"
                    placeholder="/old-path"
                    value={formData.fromPath}
                    onChange={(e) => setFormData(prev => ({ ...prev, fromPath: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="toPath">To Path</Label>
                  <Input
                    id="toPath"
                    placeholder="/new-path"
                    value={formData.toPath}
                    onChange={(e) => setFormData(prev => ({ ...prev, toPath: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="statusCode">Status Code</Label>
                  <Select 
                    value={formData.statusCode.toString()} 
                    onValueChange={(value) => setFormData(prev => ({ ...prev, statusCode: parseInt(value) }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="301">301 - Permanent Redirect</SelectItem>
                      <SelectItem value="302">302 - Temporary Redirect</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="reason">Reason (Optional)</Label>
                  <Textarea
                    id="reason"
                    placeholder="Why is this redirect needed?"
                    value={formData.reason}
                    onChange={(e) => setFormData(prev => ({ ...prev, reason: e.target.value }))}
                  />
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreateRedirect} disabled={isSubmitting}>
                    {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Create Redirect
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>
      <Separator />
      
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search redirects..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      <DataTable
        columns={columns}
        data={redirects}
        loading={loading}
        searchKey="fromPath"
        searchPlaceholder="Search by path..."
      />
    </div>
  );
}
