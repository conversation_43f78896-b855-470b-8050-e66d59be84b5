import { Request, Response } from 'express';
import { stringify } from 'csv-stringify/sync';

export const downloadTemplate = async (req: Request, res: Response) => {
  try {
    // Define the CSV headers based on the required fields
    const headers = [
      // Purchase Order Fields
      'Order Number',                 // Will be auto-generated if not provided
      'Supplier Name',               // Required
      'Supplier Code',               // Optional
      'Supplier Email',              // Optional
      'Supplier Phone',              // Optional
      'Supplier Address',            // Optional
      'Invoice Number',              // Optional
      'Invoice Date',                // Optional (YYYY-MM-DD)
      'Invoice PDF URL',             // Optional
      'Payment Terms',               // Required (CREDIT, PAID_IN_ADVANCE, NET_15, NET_30, NET_45, NET_60, COD)
      'Status',                      // Optional (DRAFT, PENDING, APPROVED, COMPLETED, CANCELLED)
      'Subtotal',                    // Will be calculated automatically
      'Tax',                         // Optional
      'Additional Charge',           // Optional
      'Total',                       // Will be calculated automatically
      'Payment Reference',           // Optional
      'Bank Payment Reference',      // Optional
      'Bank Payment Reference Date', // Optional (YYYY-MM-DD)
      'Notes',                       // Optional
      'Created At',                  // Will be auto-generated
      'Updated At',                  // Will be auto-generated
      'Created By',                  // Optional
      'Updated By',                  // Optional
      
      // Item Fields
      'Item ID',                     // Will be auto-generated or looked up
      'Item SKU',                    // Required
      'Item Name',                   // Optional
      'Item Quantity',               // Required
      'Item Unit Price',             // Required
      'Item Total',                  // Will be calculated if not provided
      'Item Notes'                   // Optional
    ];

    // Create sample rows to show multiple items in one PO
    const sampleRows = [
      // First item of PO
      [
        'PO-********-001',      // Order number
        'ABC Suppliers',         // Supplier name
        'ABC001',                // Supplier code
        '<EMAIL>',  // Supplier email
        '+************',         // Supplier phone
        'Dubai, UAE',            // Supplier address
        'INV-123',               // Invoice number
        '2025-05-13',            // Invoice date
        '',                      // Invoice PDF URL
        'NET_30',                // Payment terms
        'DRAFT',                 // Status
        '255.00',                // Subtotal
        '5.00',                  // Tax
        '0.00',                  // Additional charge
        '260.00',                // Total
        'PAY-REF-001',           // Payment reference
        'BANK-REF-001',          // Bank payment reference
        '2025-05-13',            // Bank reference date
        'Sample purchase order',  // Notes
        '2025-05-13',            // Created at
        '2025-05-13',            // Updated at
        'admin',                 // Created by
        'admin',                 // Updated by
        '',                      // Item ID (will be auto-generated)
        'ITEM001',               // Item SKU
        'Paper Cups',            // Item name
        '10',                    // Item quantity
        '15.50',                 // Item unit price
        '155.00',                // Item total
        'Standard size'          // Item notes
      ],
      // Second item of the same PO
      [
        'PO-********-001',      // Same order number
        'ABC Suppliers',         // Same supplier name
        'ABC001',                // Same supplier code
        '<EMAIL>',  // Same supplier email
        '+************',         // Same supplier phone
        'Dubai, UAE',            // Same supplier address
        'INV-123',               // Same invoice number
        '2025-05-13',            // Same invoice date
        '',                      // Same invoice PDF URL
        'NET_30',                // Same payment terms
        'DRAFT',                 // Same status
        '255.00',                // Same subtotal
        '5.00',                  // Same tax
        '0.00',                  // Same additional charge
        '260.00',                // Same total
        'PAY-REF-001',           // Same payment reference
        'BANK-REF-001',          // Same bank payment reference
        '2025-05-13',            // Same bank reference date
        'Sample purchase order',  // Same notes
        '2025-05-13',            // Same created at
        '2025-05-13',            // Same updated at
        'admin',                 // Same created by
        'admin',                 // Same updated by
        '',                      // Item ID (will be auto-generated)
        'ITEM002',               // Different item SKU
        'Paper Plates',          // Different item name
        '5',                     // Different item quantity
        '20.00',                 // Different item unit price
        '100.00',                // Different item total
        'Large size'             // Different item notes
      ],
      // New purchase order
      [
        'PO-********-002',      // Different order number
        'XYZ Corporation',       // Different supplier name
        'XYZ001',                // Different supplier code
        '<EMAIL>',       // Different supplier email
        '+************',         // Different supplier phone
        'Abu Dhabi, UAE',        // Different supplier address
        'INV-456',               // Different invoice number
        '2025-05-14',            // Different invoice date
        '',                      // Different invoice PDF URL
        'CREDIT',                // Different payment terms
        'PENDING',               // Different status
        '50.00',                 // Different subtotal
        '7.50',                  // Different tax
        '0.00',                  // Different additional charge
        '57.50',                 // Different total
        'PAY-REF-002',           // Different payment reference
        'BANK-REF-002',          // Different bank payment reference
        '2025-05-14',            // Different bank reference date
        'Another purchase order', // Different notes
        '2025-05-14',            // Different created at
        '2025-05-14',            // Different updated at
        'admin',                 // Different created by
        'admin',                 // Different updated by
        '',                      // Item ID (will be auto-generated)
        'ITEM003',               // Different item SKU
        'Plastic Forks',         // Different item name
        '100',                   // Different item quantity
        '0.50',                  // Different item unit price
        '50.00',                 // Different item total
        'Disposable'             // Different item notes
      ]
    ];

    // Create the CSV content with headers and sample rows
    const csvContent = stringify([headers, ...sampleRows], {
      header: false
    });

    // Set response headers for file download
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename=purchase-order-template.csv');
    
    // Send the CSV content
    res.send(csvContent);
  } catch (error: any) {
    console.error('Template download error:', error);
    res.status(500).json({
      message: 'Failed to generate template',
      error: error.message
    });
  }
}
