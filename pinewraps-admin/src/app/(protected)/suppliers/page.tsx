'use client';

import { useEffect, useState } from 'react';
import { Users, Plus, Building2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import { Heading } from '@/components/ui/heading';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { columns, type Supplier } from './columns';
import { useRouter } from 'next/navigation';
import axios from 'axios';
import { toast } from '@/lib/toast';
import { auth } from '@/lib/firebase';
import { ImportExportSuppliers } from '@/components/suppliers/import-export-suppliers';

// Create axios instance
const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to add auth token
api.interceptors.request.use(async (config) => {
  const user = auth.currentUser;
  if (user) {
    const token = await user.getIdToken();
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export default function SuppliersPage() {
  const router = useRouter();
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalSuppliers: 0,
    activeSuppliers: 0,
    inactiveSuppliers: 0,
  });
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 50, 
    total: 0,
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Separate function to fetch supplier stats (total counts)
  const fetchSupplierStats = async (search = '') => {
    try {
      const response = await api.get('/api/suppliers/stats', {
        params: { search }
      });
      const { data } = response.data;
      setStats({
        totalSuppliers: data.total || 0,
        activeSuppliers: data.active || 0,
        inactiveSuppliers: data.inactive || 0,
      });
    } catch (error) {
      console.error('Error fetching supplier stats:', error);
      // Don't show error toast for stats to avoid multiple error messages
    }
  };

  const fetchSuppliers = async (page = 1, search = '', status = 'all', pageSize?: number) => {
    try {
      setLoading(true);
      const currentPageSize = pageSize || pagination.pageSize;
      const response = await api.get('/api/suppliers', {
        params: {
          page,
          limit: currentPageSize,
          search,
          isActive: status === 'active' ? 'true' : status === 'inactive' ? 'false' : undefined,
        }
      });
      const { data } = response.data;
      setSuppliers(data.items);
      
      // Update pagination
      setPagination(prev => ({
        ...prev,
        page: data.meta.page,
        total: data.meta.total,
        pageSize: currentPageSize,
      }));
      
      // Fetch accurate stats separately
      fetchSupplierStats(search);
    } catch (error) {
      console.error('Error fetching suppliers:', error);
      toast.error('Failed to load suppliers');
    } finally {
      setLoading(false);
    }
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    fetchSuppliers(page, searchQuery, statusFilter);
  };

  // Handle search
  const handleSearch = (query: string) => {
    console.log('Searching for:', query);
    setSearchQuery(query);
    // Reset to page 1 when searching
    fetchSuppliers(1, query, statusFilter);
    // Also update stats when search changes
    fetchSupplierStats(query);
  };
  
  // Handle status filter change
  const handleStatusFilterChange = (status: string) => {
    setStatusFilter(status);
    fetchSuppliers(1, searchQuery, status);
  };

  // Handle page size change
  const handlePageSizeChange = (newPageSize: number) => {
    setPagination(prev => ({
      ...prev,
      pageSize: newPageSize,
      page: 1 // Reset to first page when changing page size
    }));
    fetchSuppliers(1, searchQuery, statusFilter, newPageSize);
  };

  // This will run on component mount and when the route changes
  useEffect(() => {
    fetchSuppliers(1, '', 'all');
    
    // Add this to ensure the page is refreshed when it becomes visible again
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        fetchSuppliers(pagination.page, searchQuery, statusFilter);
      }
    };
    
    // Add a router refresh listener
    const handleRouteRefresh = () => {
      fetchSuppliers(pagination.page, searchQuery, statusFilter);
    };
    
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleRouteRefresh);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleRouteRefresh);
    };
  }, []);

  const handleAddSupplier = (e: React.MouseEvent) => {
    e.preventDefault();
    router.push('/suppliers/add-supplier');
  };

  return (
    <div className="flex-1 space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <Heading
            title="Supplier Management"
            description="Manage your suppliers and vendor relationships"
          />
        </div>
        <div className="flex items-center space-x-2">
          <ImportExportSuppliers 
            suppliers={suppliers} 
            onImportComplete={() => fetchSuppliers(pagination.page, searchQuery, statusFilter)} 
          />
          <Button onClick={handleAddSupplier}>
            <Plus className="mr-2 h-4 w-4" />
            Add Supplier
          </Button>
        </div>
      </div>
      <Separator />
      
      <div className="grid gap-4 grid-cols-1 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Suppliers</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalSuppliers}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Suppliers</CardTitle>
            <Building2 className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-500">{stats.activeSuppliers}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Inactive Suppliers</CardTitle>
            <Building2 className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-500">{stats.inactiveSuppliers}</div>
          </CardContent>
        </Card>
      </div>

      <DataTable
        columns={columns}
        data={suppliers}
        searchKey="name"
        searchPlaceholder="Search suppliers..."
        loading={loading}
        pagination={{
          page: pagination.page,
          pageSize: pagination.pageSize,
          total: pagination.total,
          onPageChange: handlePageChange
        }}
        meta={{
          onSearch: handleSearch,
          onPageSizeChange: handlePageSizeChange
        }}
        filterComponent={
          <div className="flex items-center ml-2">
            <select
              className="px-3 py-2 rounded-md border border-gray-200 focus:outline-none focus:ring-2 focus:ring-black"
              value={statusFilter}
              onChange={(e) => handleStatusFilterChange(e.target.value)}
            >
              <option value="all">All Suppliers</option>
              <option value="active">Active Only</option>
              <option value="inactive">Inactive Only</option>
            </select>
          </div>
        }
      />
    </div>
  );
}
