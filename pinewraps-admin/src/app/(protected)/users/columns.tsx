"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { ArrowUpDown, MoreHorizontal, Trash2 } from "lucide-react";
import { format } from "date-fns";
import { User } from "@/services/admin.service";
import { useRouter } from 'next/navigation';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useState, useEffect } from 'react';
import { getFirebaseToken } from '@/lib/firebase';
import api, { usersApi } from "@/lib/api";
import toast from 'react-hot-toast';

export type AdminColumn = {
  id: string;
  email: string;
  name: string;
  role: string;
  isActive: boolean;
  type: string;
  createdAt: string;
};

export const columns: ColumnDef<User>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "email",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Email
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
  },
  {
    accessorKey: "name",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Name
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
  },
  {
    accessorKey: "role",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Role
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
      const role = row.getValue("role") as string;
      const roleColors = {
        SUPER_ADMIN: "bg-purple-100 text-purple-800",
        ADMIN: "bg-blue-100 text-blue-800",
        POS_USER: "bg-green-100 text-green-800",
        DRIVER: "bg-orange-100 text-orange-800",
      };

      return (
        <Badge className={roleColors[role] || "bg-gray-100 text-gray-800"}>
          {role.replace("_", " ")}
        </Badge>
      );
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    accessorKey: "isActive",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Status
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
      const isActive = row.getValue("isActive") as boolean;
      return (
        <Badge className={isActive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}>
          {isActive ? "Active" : "Inactive"}
        </Badge>
      );
    },
  },
  {
    accessorKey: "createdAt",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Created At
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
      const date = row.getValue("createdAt") as string;
      return format(new Date(date), "MMM d, yyyy");
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const router = useRouter();
      const user = row.original;
      const [currentUser, setCurrentUser] = useState<any>(null);
      const [isDeleting, setIsDeleting] = useState(false);
      const [showDeleteDialog, setShowDeleteDialog] = useState(false);

      useEffect(() => {
        const fetchCurrentUser = async () => {
          try {
            const token = await getFirebaseToken();
            // Use the /api/auth/verify endpoint directly since it's working
            const response = await api.post('/api/auth/verify', { token });
            
            // Set the current user based on the response format
            if (response.data && response.data.user) {
              setCurrentUser(response.data.user);
              console.log('Current user set from auth/verify:', response.data.user.id);
            } else if (response.data) {
              setCurrentUser(response.data);
              console.log('Current user set from auth/verify data:', response.data.id);
            }
          } catch (error) {
            console.error('Error fetching current user:', error);
          }
        };

        fetchCurrentUser();
      }, []);

      const handleDelete = async () => {
        try {
          setIsDeleting(true);
          const token = await getFirebaseToken();
          console.log(`Attempting to delete user with ID: ${user.id}`);
          
          const response = await usersApi.delete(user.id);
          
          console.log('Delete response:', response.data);
          toast.success('User deleted successfully');
          window.location.reload();
        } catch (error: any) {
          console.error('Error deleting user:', error);
          const errorMessage = error.response?.data?.error || error.response?.data?.message || 'Failed to delete user';
          toast.error(errorMessage);
        } finally {
          setIsDeleting(false);
          setShowDeleteDialog(false);
        }
      };

      return (
        <>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => router.push(`/users/${user.id}`)}>
                Edit Details
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => router.push(`/users/${user.id}/reset-password`)}>
                Reset Password
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => setShowDeleteDialog(true)}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete User
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Delete Confirmation Dialog */}
          <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                <AlertDialogDescription>
                  This action will permanently delete the user <span className="font-semibold">{user.name || user.email}</span> from both the database and Firebase. 
                  This action cannot be undone.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
                <AlertDialogAction 
                  onClick={handleDelete} 
                  disabled={isDeleting}
                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                >
                  {isDeleting ? 'Deleting...' : 'Delete User'}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </>
      );
    },
  },
];
