import type { Request, Response } from 'express';
import { companiesService } from '../services/companies.service';

export class CompaniesController {
  async deleteCompany(req: Request, res: Response) {
    try {
      await companiesService.deleteCompany(req.params.id);
      res.json({ success: true });
    } catch (error: any) {
      res.status(500).json({ success: false, error: error.message });
    }
  }

  async createCompany(req: Request, res: Response) {
    try {
      const company = await companiesService.createCompany(req.body);
      res.json({ success: true, data: company });
    } catch (error: any) {
      if (error.code === 'P2002') {
        res.status(400).json({ success: false, error: 'Company name already exists' });
      } else {
        res.status(500).json({ success: false, error: error.message });
      }
    }
  }

  async getCompanies(req: Request, res: Response) {
    try {
      const companies = await companiesService.getCompanies();
      res.json({ success: true, data: companies });
    } catch (error: any) {
      res.status(500).json({ success: false, error: error.message });
    }
  }

  async getCompanyById(req: Request, res: Response) {
    try {
      const company = await companiesService.getCompanyById(req.params.id);
      if (!company) {
        return res.status(404).json({ success: false, error: 'Company not found' });
      }
      res.json({ success: true, data: company });
    } catch (error: any) {
      res.status(500).json({ success: false, error: error.message });
    }
  }

  async updateCompany(req: Request, res: Response) {
    try {
      const company = await companiesService.updateCompany(req.params.id, req.body);
      res.json({ success: true, data: company });
    } catch (error: any) {
      if (error.code === 'P2002') {
        res.status(400).json({ success: false, error: 'Company name already exists' });
      } else {
        res.status(500).json({ success: false, error: error.message });
      }
    }
  }

  async searchCompanies(req: Request, res: Response) {
    try {
      const { search } = req.query;
      if (typeof search !== 'string') {
        return res.status(400).json({ success: false, error: 'Search query is required' });
      }
      const companies = await companiesService.searchCompanies(search);
      res.json({ success: true, data: companies });
    } catch (error: any) {
      res.status(500).json({ success: false, error: error.message });
    }
  }
}

export const companiesController = new CompaniesController();
