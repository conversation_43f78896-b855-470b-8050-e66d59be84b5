import { prisma } from '../lib/prisma';
import { RedirectType } from '@prisma/client';
import slugify from 'slugify';

export interface CreateRedirectOptions {
  fromPath: string;
  toPath: string;
  statusCode?: number;
  type?: RedirectType;
  reason?: string;
  productId?: string;
  blogPostId?: string;
  collectionId?: string;
}

export interface RedirectCheckResult {
  found: boolean;
  redirect?: {
    id: string;
    fromPath: string;
    toPath: string;
    statusCode: number;
    type: RedirectType;
  };
}

// Cache for redirect lookups
const redirectCache = new Map<string, { result: RedirectCheckResult; timestamp: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes in milliseconds

export class RedirectService {
  /**
   * Generate a consistent slug from text
   */
  static generateSlug(text: string): string {
    return slugify(text, { 
      lower: true, 
      strict: true,
      remove: /[*+~.()'"!:@]/g // Remove special characters
    });
  }

  /**
   * Ensure slug is unique for a given model
   */
  static async ensureUniqueSlug(
    baseSlug: string, 
    model: 'product' | 'blogPost' | 'collection',
    excludeId?: string
  ): Promise<string> {
    let slug = baseSlug;
    let counter = 1;
    
    while (true) {
      const whereClause = {
        slug,
        ...(excludeId && { id: { not: excludeId } })
      };

      let existingRecord;
      switch (model) {
        case 'product':
          existingRecord = await prisma.product.findFirst({ where: whereClause });
          break;
        case 'blogPost':
          existingRecord = await prisma.blogPost.findFirst({ where: whereClause });
          break;
        case 'collection':
          existingRecord = await prisma.collection.findFirst({ where: whereClause });
          break;
      }
      
      if (!existingRecord) break;
      
      slug = `${baseSlug}-${counter}`;
      counter++;
    }
    
    return slug;
  }

  /**
   * Create a redirect
   */
  static async createRedirect(options: CreateRedirectOptions) {
    const {
      fromPath,
      toPath,
      statusCode = 301,
      type = RedirectType.MANUAL,
      reason,
      productId,
      blogPostId,
      collectionId
    } = options;

    // Check if redirect already exists
    const existingRedirect = await prisma.redirect.findFirst({
      where: { fromPath }
    });

    if (existingRedirect) {
      // Update existing redirect
      return await prisma.redirect.update({
        where: { id: existingRedirect.id },
        data: {
          toPath,
          statusCode,
          type,
          reason,
          productId,
          blogPostId,
          collectionId
        }
      });
    }

    // Create new redirect
    return await prisma.redirect.create({
      data: {
        fromPath,
        toPath,
        statusCode,
        type,
        reason,
        productId,
        blogPostId,
        collectionId
      }
    });
  }

  /**
   * Create redirect when slug changes
   */
  static async createSlugChangeRedirect(
    oldSlug: string,
    newSlug: string,
    contentType: 'product' | 'blog' | 'collection',
    contentId: string,
    reason?: string
  ) {
    if (oldSlug === newSlug) return null;

    const pathPrefix = {
      product: '/shop/',
      blog: '/blog/',
      collection: '/collections/'
    }[contentType];

    const fromPath = `${pathPrefix}${oldSlug}`;
    const toPath = `${pathPrefix}${newSlug}`;

    const relationField = {
      product: 'productId',
      blog: 'blogPostId',
      collection: 'collectionId'
    }[contentType];

    return await this.createRedirect({
      fromPath,
      toPath,
      type: RedirectType.AUTO_SLUG,
      reason: reason || `Slug changed from ${oldSlug} to ${newSlug}`,
      [relationField]: contentId
    });
  }

  /**
   * Check for redirect by path
   */
  static async checkForRedirect(path: string): Promise<RedirectCheckResult> {
    // Clean the path by removing trailing slashes and converting to lowercase
    const cleanPath = path.replace(/\/+$/, '').toLowerCase();

    // Check cache first
    const cached = redirectCache.get(cleanPath);
    if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
      console.log('Using cached redirect result for:', cleanPath);
      return cached.result;
    }

    console.log('Checking database for redirect:', cleanPath);
    // Remove domain and protocol if present (e.g., http://localhost:3002/path -> /path)
    // Strip protocol and domain if present
    const pathWithoutDomain = cleanPath.replace(/^https?:\/\/[^/]+/, '');

    // Try different path formats
    const pathFormats = [
      pathWithoutDomain,
      pathWithoutDomain.startsWith('/') ? pathWithoutDomain.substring(1) : pathWithoutDomain,
      `/${pathWithoutDomain.replace(/^\/+|\/+$/g, '')}`
    ];

    console.log('Checking redirect paths:', pathFormats);

    for (const pathFormat of pathFormats) {
      const redirect = await prisma.redirect.findFirst({
        where: { fromPath: pathFormat },
        select: {
          id: true,
          fromPath: true,
          toPath: true,
          statusCode: true,
          type: true
        }
      });

      if (redirect) {
        const result = { found: true, redirect };
        
        // Cache the result
        redirectCache.set(cleanPath, {
          result,
          timestamp: Date.now()
        });
        
        return result;
      }
    }

    const result = { found: false };
    
    // Cache the result
    redirectCache.set(cleanPath, {
      result,
      timestamp: Date.now()
    });
    
    return result;
  }

  /**
   * Get all redirects with pagination
   */
  static async getAllRedirects(page = 1, limit = 50, search?: string) {
    const skip = (page - 1) * limit;
    
    const where = search ? {
      OR: [
        { fromPath: { contains: search, mode: 'insensitive' as const } },
        { toPath: { contains: search, mode: 'insensitive' as const } },
        { reason: { contains: search, mode: 'insensitive' as const } }
      ]
    } : {};

    const [redirects, total] = await Promise.all([
      prisma.redirect.findMany({
        where,
        include: {
          product: { select: { id: true, name: true, slug: true } },
          blogPost: { select: { id: true, title: true, slug: true } },
          collection: { select: { id: true, name: true, slug: true } }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.redirect.count({ where })
    ]);

    return {
      data: redirects,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  /**
   * Delete redirect
   */
  static async deleteRedirect(id: string) {
    return await prisma.redirect.delete({
      where: { id }
    });
  }

  /**
   * Delete redirects by content
   */
  static async deleteRedirectsByContent(
    contentType: 'product' | 'blog' | 'collection',
    contentId: string
  ) {
    const whereClause = {
      product: { productId: contentId },
      blog: { blogPostId: contentId },
      collection: { collectionId: contentId }
    }[contentType];

    return await prisma.redirect.deleteMany({
      where: whereClause
    });
  }

  /**
   * Bulk create redirects
   */
  static async bulkCreateRedirects(redirects: CreateRedirectOptions[]) {
    const results = [];
    
    for (const redirect of redirects) {
      try {
        const result = await this.createRedirect(redirect);
        results.push({ success: true, data: result });
      } catch (error) {
        results.push({ 
          success: false, 
          error: error.message,
          fromPath: redirect.fromPath 
        });
      }
    }
    
    return results;
  }
}
