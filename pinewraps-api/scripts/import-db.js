/**
 * Database Import Script
 * 
 * This script helps import your exported Supabase database into a local PostgreSQL instance
 * Run with: node scripts/import-db.js path/to/exported/file.sql
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Check if file path is provided
if (process.argv.length < 3) {
  console.error('Error: Please provide the path to the exported database file');
  console.error('Usage: node scripts/import-db.js path/to/exported/file.sql');
  process.exit(1);
}

const exportedFilePath = process.argv[2];

// Check if file exists
if (!fs.existsSync(exportedFilePath)) {
  console.error(`Error: File not found: ${exportedFilePath}`);
  process.exit(1);
}

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Prompt for local database connection details
console.log('Please enter your local PostgreSQL connection details:');

rl.question('Host (default: localhost): ', (host) => {
  host = host || 'localhost';
  
  rl.question('Port (default: 5432): ', (port) => {
    port = port || '5432';
    
    rl.question('Username (default: postgres): ', (user) => {
      user = user || 'postgres';
      
      rl.question('Password: ', (password) => {
        if (!password) {
          console.error('Error: Password is required');
          rl.close();
          process.exit(1);
        }
        
        rl.question('Database name: ', (dbname) => {
          if (!dbname) {
            console.error('Error: Database name is required');
            rl.close();
            process.exit(1);
          }
          
          console.log('\nImporting database...');
          console.log(`Host: ${host}`);
          console.log(`Database: ${dbname}`);
          console.log(`Input file: ${exportedFilePath}`);
          
          try {
            // Set environment variables for pg_restore
            const env = {
              ...process.env,
              PGPASSWORD: password
            };
            
            // First, create the database if it doesn't exist
            try {
              execSync(
                `psql -h ${host} -p ${port} -U ${user} -c "CREATE DATABASE ${dbname};"`,
                { 
                  env,
                  stdio: 'inherit'
                }
              );
              console.log(`Created database ${dbname}`);
            } catch (error) {
              console.log(`Database ${dbname} might already exist, continuing with import...`);
            }
            
            // Run pg_restore command
            execSync(
              `pg_restore -h ${host} -p ${port} -U ${user} -d ${dbname} -v "${exportedFilePath}"`,
              { 
                env,
                stdio: 'inherit'
              }
            );
            
            console.log('\nDatabase import completed successfully!');
            console.log('\nTo connect to this database in your application:');
            console.log(`DATABASE_URL=postgresql://${user}:${password}@${host}:${port}/${dbname}`);
            
            // Update Prisma schema if needed
            rl.question('\nDo you want to update your .env file with the new database URL? (y/n): ', (answer) => {
              if (answer.toLowerCase() === 'y') {
                try {
                  // Don't overwrite the existing .env file, create a new one for local development
                  const envPath = path.join(__dirname, '../.env.local');
                  const envContent = `DATABASE_URL=postgresql://${user}:${password}@${host}:${port}/${dbname}\nDIRECT_URL=postgresql://${user}:${password}@${host}:${port}/${dbname}`;
                  
                  fs.writeFileSync(envPath, envContent);
                  console.log(`\nCreated .env.local file with local database connection.`);
                  console.log(`To use this local database, run your application with:`);
                  console.log(`npx dotenv -e .env.local -- npm run dev`);
                } catch (error) {
                  console.error('Error updating .env file:', error.message);
                }
              }
              
              rl.close();
            });
          } catch (error) {
            console.error('Error importing database:', error.message);
            console.error('\nTroubleshooting:');
            console.error('1. Make sure pg_restore is installed and in your PATH');
            console.error('2. Check your database credentials');
            console.error('3. Ensure you have proper permissions to access the database');
            rl.close();
            process.exit(1);
          }
        });
      });
    });
  });
});
