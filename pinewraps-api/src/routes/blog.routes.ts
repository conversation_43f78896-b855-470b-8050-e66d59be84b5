import express from 'express';
import { Blog<PERSON>ontroller } from '../controllers/blog.controller';
import { requireAuth, requireAdmin } from '../middleware/auth';

const router = express.Router();

// Public routes
router.get('/posts', BlogController.getAllPosts);
router.get('/posts/:identifier', BlogController.getPost);
router.get('/categories', BlogController.getAllCategories);
router.get('/categories/:identifier', BlogController.getCategory);
router.get('/categories/:identifier/posts', BlogController.getPostsByCategory);

// Protected routes (admin only)
router.post('/posts', requireAuth, requireAdmin, BlogController.createPost);
router.put('/posts/:id', requireAuth, requireAdmin, BlogController.updatePost);
router.delete('/posts/:id', requireAuth, requireAdmin, BlogController.deletePost);
router.post('/categories', requireAuth, requireAdmin, BlogController.createCategory);
router.put('/categories/:id', requireAuth, requireAdmin, BlogController.updateCategory);
router.delete('/categories/:id', requireAuth, requireAdmin, BlogController.deleteCategory);

export default router;
