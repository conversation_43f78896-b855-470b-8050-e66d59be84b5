'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Bell, ArrowLeft, RefreshCw, Users, User, Award, Crown, CheckCircle, XCircle, Clock, AlertTriangle } from 'lucide-react';
import { toast } from '@/lib/toast';
import { cn } from '@/lib/utils';
import { 
  getPushNotificationById,
  resendPushNotification,
  PushNotificationDto,
  PushNotificationRecipientDto,
  NotificationStatus,
  getNotificationStatusBadge
} from '@/lib/utils/notifications';
import Link from 'next/link';
import React from 'react';

export default function NotificationDetailsPage({ params }: { params: { id: string } }) {
  const [notification, setNotification] = useState<PushNotificationDto | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isResending, setIsResending] = useState(false);

  const fetchNotification = async () => {
    setIsLoading(true);
    try {
      const data = await getPushNotificationById(params.id);
      setNotification(data);
    } catch (error) {
      console.error('Error fetching notification:', error);
      toast.error('Failed to load notification details');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchNotification();
  }, [params.id]);

  const handleResend = async () => {
    setIsResending(true);
    try {
      toast.loading('Resending notification... This may take a moment');
      
      await resendPushNotification(params.id);
      
      toast.dismiss();
      toast.success(
        'Notification resent successfully! It may take a moment to process all recipients'
      );
      
      // Fetch updated notification data
      fetchNotification();
    } catch (error) {
      console.error('Error resending notification:', error);
      
      toast.dismiss();
      toast.error(
        `Failed to resend notification: ${error instanceof Error ? error.message : 'An unexpected error occurred'}`
      );
    } finally {
      setIsResending(false);
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const getTargetTypeLabel = (targetType?: string) => {
    if (!targetType) return { label: 'Unknown', icon: Bell, color: 'bg-gray-100 text-gray-800' };
    
    switch (targetType) {
      case 'ALL':
        return { label: 'All Customers', icon: Users, color: 'bg-blue-100 text-blue-800' };
      case 'VIP':
        return { label: 'VIP Customers', icon: Crown, color: 'bg-purple-100 text-purple-800' };
      case 'REWARD_TIER':
        return { label: 'Reward Tier', icon: Award, color: 'bg-yellow-100 text-yellow-800' };
      case 'SPECIFIC_CUSTOMERS':
        return { label: 'Specific Customers', icon: User, color: 'bg-green-100 text-green-800' };
      default:
        return { label: 'Unknown', icon: Bell, color: 'bg-gray-100 text-gray-800' };
    }
  };

  const getStatusBadge = (status?: NotificationStatus, sentAt?: string | null) => {
    if (!status) return <Badge variant="outline" className="bg-gray-100 text-gray-800 border-gray-200">Unknown</Badge>;
    
    const statusInfo = getNotificationStatusBadge(status, sentAt);
    
    const badgeClass = `bg-${statusInfo.color}-100 text-${statusInfo.color}-800 border-${statusInfo.color}-200`;
    
    return (
      <div className="flex flex-col">
        <Badge variant="outline" className={badgeClass}>{statusInfo.label}</Badge>
        {statusInfo.timestamp && (
          <span className="text-xs text-gray-500 mt-1">
            {statusInfo.timestamp.toLocaleDateString()} {statusInfo.timestamp.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
          </span>
        )}
      </div>
    );
  };

  const getRecipientStatusIcon = (status?: NotificationStatus) => {
    if (!status) return { icon: Clock, color: 'text-gray-400' };
    
    switch (status) {
      case NotificationStatus.PENDING:
        return { icon: Clock, color: 'text-yellow-500' };
      case NotificationStatus.SENT:
        return { icon: CheckCircle, color: 'text-green-500' };
      case NotificationStatus.PARTIAL:
        return { icon: AlertTriangle, color: 'text-orange-500' };
      case NotificationStatus.FAILED:
      case NotificationStatus.ERROR:
        return { icon: XCircle, color: 'text-red-500' };
      default:
        return { icon: Clock, color: 'text-gray-400' };
    }
  };

  if (isLoading) {
    return (
      <div className="p-6 max-w-[1400px] mx-auto flex justify-center items-center h-[60vh]">
        <div className="flex flex-col items-center">
          <svg className="animate-spin h-10 w-10 text-gray-400 mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <p className="text-gray-500">Loading notification details...</p>
        </div>
      </div>
    );
  }

  if (!notification) {
    return (
      <div className="p-6 max-w-[1400px] mx-auto">
        <div className="mb-6">
          <Button asChild variant="outline">
            <Link href="/notifications/history">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Notifications
            </Link>
          </Button>
        </div>
        <Card className="p-8 text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Notification Not Found</h2>
          <p className="text-gray-500 mb-4">The notification you're looking for doesn't exist or has been deleted.</p>
          <Button asChild>
            <Link href="/notifications/history">
              View All Notifications
            </Link>
          </Button>
        </Card>
      </div>
    );
  }

  const targetType = getTargetTypeLabel(notification.targetType);
  const IconComponent = targetType.icon;

  return (
    <div className="p-6 max-w-[1400px] mx-auto">
      {/* Header */}
      <div className="mb-6 flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <Button asChild variant="outline">
            <Link href="/notifications/history">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">Notification Details</h1>
            <p className="text-sm text-gray-500 mt-1">
              View details and delivery status
            </p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={fetchNotification}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button
            onClick={handleResend}
            disabled={isResending || notification.status === NotificationStatus.PENDING}
          >
            {isResending ? (
              <>
                <svg className="animate-spin h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Resending...
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                Resend Notification
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Notification Details */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <Card className="p-6 md:col-span-2">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Notification Information</h2>
          
          <div className="space-y-6">
            {/* Preview */}
            <div className="border rounded-lg p-4 bg-gray-50">
              <div className="flex items-start space-x-3">
                <div className={cn("p-2 rounded-full", targetType.color.split(' ')[0])}>
                  <Bell className="h-5 w-5" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">
                    {notification.title}
                  </p>
                  <p className="text-sm text-gray-500 mt-1">
                    {notification.body}
                  </p>
                </div>
              </div>
            </div>
            
            {/* Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-500">Status</p>
                <div className="mt-1 flex items-center space-x-2">
                  {getStatusBadge(notification.status, notification.sentAt)}
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={handleResend}
                    disabled={isResending || notification.status === NotificationStatus.PENDING}
                  >
                    {isResending ? (
                      <>
                        <svg className="animate-spin h-3 w-3 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Resending...
                      </>
                    ) : (
                      <>
                        <RefreshCw className="h-3 w-3 mr-1" />
                        Resend
                      </>
                    )}
                  </Button>
                </div>
                {notification.error && (
                  <div className="mt-2">
                    <p className="text-sm font-medium text-red-500">Error: {notification.error}</p>
                  </div>
                )}
              </div>
              
              <div>
                <p className="text-sm font-medium text-gray-500">Target</p>
                <div className="mt-1 flex items-center space-x-2">
                  <Badge variant="outline" className={cn("flex items-center space-x-1", targetType.color)}>
                    <IconComponent className="h-3 w-3" />
                    <span>{targetType.label}</span>
                  </Badge>
                  {notification.targetType === 'REWARD_TIER' && notification.targetData?.tier && (
                    <Badge variant="outline" className="bg-gray-100 text-gray-800">
                      {notification.targetData.tier}
                    </Badge>
                  )}
                </div>
              </div>
              
              <div>
                <p className="text-sm font-medium text-gray-500">Created At</p>
                <p className="mt-1 text-sm text-gray-900">{formatDate(notification.createdAt)}</p>
              </div>
              
              <div>
                <p className="text-sm font-medium text-gray-500">Sent At</p>
                <p className="mt-1 text-sm text-gray-900">{formatDate(notification.sentAt)}</p>
              </div>
              
              <div>
                <p className="text-sm font-medium text-gray-500">Created By</p>
                <p className="mt-1 text-sm text-gray-900">
                  {notification.creator 
                    ? `${notification.creator.firstName} ${notification.creator.lastName}`
                    : 'System'
                  }
                </p>
              </div>
              
              <div>
                <p className="text-sm font-medium text-gray-500">Recipients</p>
                <p className="mt-1 text-sm text-gray-900">
                  {notification._count?.recipients || notification.recipients?.length || 0} customers
                </p>
              </div>
            </div>
          </div>
        </Card>
        
        <Card className="p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Delivery Statistics</h2>
          
          <div className="space-y-4">
            {/* Calculate statistics */}
            {notification.recipients && (
              <>
                <div className="grid grid-cols-3 gap-2">
                  <div className="bg-gray-50 p-3 rounded-lg text-center">
                    <p className="text-2xl font-semibold text-gray-900">
                      {notification.recipients.length}
                    </p>
                    <p className="text-xs text-gray-500">Total</p>
                  </div>
                  <div className="bg-green-50 p-3 rounded-lg text-center">
                    <p className="text-2xl font-semibold text-green-600">
                      {notification.recipients.filter(r => r.status === 'SENT').length}
                    </p>
                    <p className="text-xs text-gray-500">Sent</p>
                  </div>
                  <div className="bg-red-50 p-3 rounded-lg text-center">
                    <p className="text-2xl font-semibold text-red-600">
                      {notification.recipients.filter(r => r.status === 'FAILED').length}
                    </p>
                    <p className="text-xs text-gray-500">Failed</p>
                  </div>
                </div>
                
                {/* Delivery progress bar */}
                <div className="mt-4">
                  <div className="flex justify-between text-xs text-gray-500 mb-1">
                    <span>Delivery Progress</span>
                    <span>
                      {Math.round((notification.recipients.filter(r => r.status === 'SENT').length / notification.recipients.length) * 100)}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-green-500 h-2 rounded-full" 
                      style={{ 
                        width: `${(notification.recipients.filter(r => r.status === 'SENT').length / notification.recipients.length) * 100}%` 
                      }}
                    ></div>
                  </div>
                </div>
              </>
            )}
          </div>
        </Card>
      </div>
      
      {/* Recipients Table */}
      {notification.recipients && notification.recipients.length > 0 && (
        <Card className="overflow-hidden">
          <div className="p-4 border-b">
            <h2 className="text-lg font-medium text-gray-900">Recipients</h2>
          </div>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Customer</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Sent At</TableHead>
                  <TableHead>Delivered At</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {notification.recipients.map((recipient) => {
                  const statusIcon = getRecipientStatusIcon(recipient.status);
                  const StatusIcon = statusIcon.icon;
                  
                  return (
                    <TableRow key={recipient.id}>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <div className="p-2 rounded-full bg-gray-100">
                            <User className="h-4 w-4 text-gray-600" />
                          </div>
                          <div>
                            <p className="font-medium text-gray-900">
                              {recipient.customer.firstName} {recipient.customer.lastName}
                            </p>
                            <p className="text-xs text-gray-500">{recipient.customer.email}</p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <div className={cn("text-" + getRecipientStatusIcon(recipient.status).color)}>
                            {React.createElement(getRecipientStatusIcon(recipient.status).icon, { className: "h-4 w-4 mr-2" })}
                          </div>
                          <div>
                            {recipient.status}
                            {recipient.error && (
                              <div className="text-xs text-red-500 mt-1">{recipient.error}</div>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {formatDate(recipient.sentAt)}
                      </TableCell>
                      <TableCell>
                        {recipient.status === NotificationStatus.SENT ? 'Delivered' : 'Not Delivered'}
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        </Card>
      )}
    </div>
  );
}
