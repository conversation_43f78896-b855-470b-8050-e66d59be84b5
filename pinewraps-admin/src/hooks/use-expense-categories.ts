import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "@/components/ui/use-toast";
import { operatingExpenseService, ExpenseCategory } from "@/services/operating-expense.service";

// ExpenseCategory interface is now imported from the service

export const useExpenseCategories = () => {
  const queryClient = useQueryClient();

  const { data: categories, isLoading, error } = useQuery<ExpenseCategory[]>({
    queryKey: ["expense-categories"],
    queryFn: () => operatingExpenseService.getCategories(),
  });

  const createCategory = useMutation({
    mutationFn: (data: Omit<ExpenseCategory, "id" | "createdAt" | "updatedAt">) => 
      operatingExpenseService.createCategory(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["expense-categories"] });
      toast({ title: "Expense category created successfully" });
    },
    onError: () => {
      toast({ title: "Failed to create expense category", variant: "destructive" });
    },
  });

  const updateCategory = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<ExpenseCategory> }) => 
      operatingExpenseService.updateCategory(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["expense-categories"] });
      toast({ title: "Expense category updated successfully" });
    },
    onError: () => {
      toast({ title: "Failed to update expense category", variant: "destructive" });
    },
  });

  const deleteCategory = useMutation({
    mutationFn: (id: string) => operatingExpenseService.deleteCategory(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["expense-categories"] });
      toast({ title: "Expense category deleted successfully" });
    },
    onError: () => {
      toast({ title: "Failed to delete expense category", variant: "destructive" });
    },
  });

  return {
    categories,
    isLoading,
    error,
    createCategory: createCategory.mutateAsync,
    updateCategory: updateCategory.mutateAsync,
    deleteCategory: deleteCategory.mutateAsync,
  };
};
