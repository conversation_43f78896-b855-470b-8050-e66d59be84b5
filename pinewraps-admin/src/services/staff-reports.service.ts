import api from '@/lib/api';

export interface StaffPerformanceMetrics {
  staffId: string;
  staffName: string;
  department: 'kitchen' | 'design' | 'finalCheck' | 'cashier'; // Current role context
  departments: string[]; // All departments this staff works in
  ordersCompleted: number; // Orders completed in THIS role
  averageProcessingTime: number; // in minutes for THIS role
  ordersSentBack: number;
  qualityScore: number;
  productivityScore: number;
  totalWorkingTime: number;
  ordersPerHour: number;
  isMultiRole: boolean; // Indicates if staff has multiple roles
}

export interface DepartmentMetrics {
  department: string;
  totalOrders: number;
  averageProcessingTime: number;
  totalStaff: number;
  efficiency: number;
  returnRate: number;
  staffPerformance: StaffPerformanceMetrics[];
}

export interface QualityMetrics {
  totalOrdersProcessed: number;
  ordersSentBack: number;
  returnRate: number;
  returnsByDepartment: {
    kitchen: number;
    design: number;
  };
  departmentOrderCounts: {
    kitchen: number;
    design: number;
  };
  commonIssues: Array<{
    issue: string;
    count: number;
    department: string;
  }>;
}

export interface StaffDashboardData {
  summary: {
    totalStaff: number;
    totalOrdersProcessed: number;
    averageProductivity: number;
    averageQualityScore: number;
  };
  topPerformers: StaffPerformanceMetrics[];
  departmentMetrics: DepartmentMetrics[];
  qualityMetrics: QualityMetrics;
  staffPerformance: StaffPerformanceMetrics[];
}

export interface ProductivityTrend {
  period: string;
  date: string;
  averageProductivity: number;
  totalOrders: number;
  staffCount: number;
}

export interface StaffOrderDetail {
  orderId: string;
  orderNumber: string;
  customerName: string;
  customerPhone: string;
  orderTotal: number;
  role: 'kitchen' | 'design' | 'finalCheck' | 'cashier';
  startTime: string | null;
  endTime: string | null;
  processingTime: number | null;
  status: string;
  items: Array<{
    id: string;
    productName: string;
    quantity: number;
    totalPrice: number;
    variations?: any;
    notes?: string;
    customImages?: any[];
  }>;
  notes: string | null;
  wasSentBack: boolean;
  complexity: 'low' | 'medium' | 'high';
  createdAt: string;
  deliveryMethod: string;
  requiresKitchen: boolean;
  requiresDesign: boolean;
  requiresFinalCheck: boolean;
}

export interface StaffAdvancedAnalytics {
  dailyPerformance: Array<{
    date: string;
    ordersProcessed: number;
    totalTime: number;
    avgTimePerOrder: number;
    qualityScore: number;
    efficiency: number;
  }>;
  orderComplexityBreakdown: {
    low: { count: number; avgTime: number; qualityRate: number };
    medium: { count: number; avgTime: number; qualityRate: number };
    high: { count: number; avgTime: number; qualityRate: number };
  };
  performanceTrends: {
    speedTrend: 'improving' | 'declining' | 'stable';
    qualityTrend: 'improving' | 'declining' | 'stable';
    productivityTrend: 'improving' | 'declining' | 'stable';
    trendPercentage: number;
  };
  peakPerformanceHours: Array<{
    hour: number;
    avgProcessingTime: number;
    orderCount: number;
    efficiency: number;
  }>;
  qualityMetrics: {
    totalOrdersProcessed: number;
    ordersSentBack: number;
    qualityRate: number;
    commonIssues: Array<{
      issue: string;
      count: number;
      percentage: number;
    }>;
    improvementSuggestions: string[];
  };
  timeDistribution: {
    fastOrders: number;
    normalOrders: number;
    slowOrders: number;
    averageByComplexity: {
      low: number;
      medium: number;
      high: number;
    };
  };
}

export interface IndividualStaffDetails {
  staffInfo: {
    id: string;
    name: string;
    email: string;
    departments: string[];
    primaryDepartment: string;
    role: string;
    joinDate: string;
    isActive: boolean;
    isMultiRole: boolean;
  };
  performanceMetrics: StaffPerformanceMetrics;
  orderHistory: StaffOrderDetail[];
  advancedAnalytics: StaffAdvancedAnalytics;
  timeAnalytics: {
    dailyHours: Array<{
      date: string;
      hoursWorked: number;
      ordersCompleted: number;
      efficiency: number;
    }>;
    peakHours: Array<{
      hour: number;
      orderCount: number;
      avgProcessingTime: number;
    }>;
    weeklyTrends: Array<{
      week: string;
      productivity: number;
      qualityScore: number;
      ordersCompleted: number;
    }>;
  };
  qualityAnalytics: {
    qualityTrend: Array<{
      period: string;
      qualityScore: number;
      ordersSentBack: number;
    }>;
    commonIssues: Array<{
      issue: string;
      count: number;
      lastOccurrence: string;
    }>;
    improvementAreas: string[];
  };
  performanceComparison: {
    departmentRank: number;
    departmentTotal: number;
    percentile: number;
    comparedToAverage: {
      productivity: number;
      quality: number;
      speed: number;
    };
  };
}

export interface StaffReportsParams {
  staffId?: string;
  department?: 'kitchen' | 'design' | 'finalCheck' | 'cashier' | 'final-check';
  timeRange?: '7d' | '14d' | '30d' | '3m' | 'all' | 'custom';
  startDate?: string;
  endDate?: string;
  showAllRoles?: boolean;
}

class StaffReportsService {
  /**
   * Get individual staff performance metrics
   */
  async getStaffPerformance(params: StaffReportsParams = {}): Promise<StaffPerformanceMetrics[]> {
    const queryParams = new URLSearchParams();
    
    if (params.staffId) queryParams.append('staffId', params.staffId);
    if (params.department) {
      const normalizedDept = this.getBackendDepartmentName(params.department);
      console.log(`Department mapping: ${params.department} -> ${normalizedDept}`);
      queryParams.append('department', normalizedDept);
    }
    if (params.timeRange) queryParams.append('timeRange', params.timeRange);
    if (params.startDate) queryParams.append('startDate', params.startDate);
    if (params.endDate) queryParams.append('endDate', params.endDate);
    if (params.showAllRoles) queryParams.append('showAllRoles', 'true');

    const apiUrl = `/api/staff-reports/performance?${queryParams.toString()}`;
    console.log(`Fetching staff performance from: ${apiUrl}`);
    
    try {
      const response = await api.get(apiUrl);
      
      // Add detailed debugging for Final Check data
      if (params.department === 'finalCheck' || params.department === 'final-check') {
        console.log('Final Check staff performance data received:', response.data);
        console.log('Number of Final Check staff records:', response.data.data.length);
        
        // Check for staff with zero orders
        const zeroOrdersStaff = response.data.data.filter((staff: any) => staff.ordersCompleted === 0);
        if (zeroOrdersStaff.length > 0) {
          console.warn(`${zeroOrdersStaff.length} Final Check staff have zero orders:`, zeroOrdersStaff);
        }
        
        // Log order counts for each staff member
        response.data.data.forEach((staff: any) => {
          console.log(`Staff ${staff.staffName} (${staff.staffId}): ${staff.ordersCompleted} orders completed`);
        });
      }
      
      return response.data.data;
    } catch (error) {
      console.error('Error fetching staff performance:', error);
      throw error;
    }
  }

  /**
   * Get department performance metrics
   */
  async getDepartmentMetrics(params: Omit<StaffReportsParams, 'staffId' | 'department'> = {}): Promise<DepartmentMetrics[]> {
    const queryParams = new URLSearchParams();
    
    if (params.timeRange) queryParams.append('timeRange', params.timeRange);
    if (params.startDate) queryParams.append('startDate', params.startDate);
    if (params.endDate) queryParams.append('endDate', params.endDate);

    const apiUrl = `/api/staff-reports/departments?${queryParams.toString()}`;
    console.log(`Fetching department metrics from: ${apiUrl}`);
    
    try {
      const response = await api.get(apiUrl);
      const metrics = response.data.data;
      
      // Log the departments received to help debug
      console.log('Departments received:', metrics.map(d => d.department));
      
      return metrics;
    } catch (error) {
      console.error('Error fetching department metrics:', error);
      throw error;
    }
  }

  /**
   * Get quality control metrics
   */
  async getQualityMetrics(params: Omit<StaffReportsParams, 'staffId' | 'department'> = {}): Promise<QualityMetrics> {
    const queryParams = new URLSearchParams();
    
    if (params.timeRange) queryParams.append('timeRange', params.timeRange);
    if (params.startDate) queryParams.append('startDate', params.startDate);
    if (params.endDate) queryParams.append('endDate', params.endDate);

    const apiUrl = `/api/staff-reports/quality?${queryParams.toString()}`;
    console.log(`Fetching quality metrics from: ${apiUrl}`);
    
    try {
      const response = await api.get(apiUrl);
      const metrics = response.data.data;
      
      // Log the quality metrics to help debug
      console.log('Quality metrics received:', metrics);
      console.log('Total orders processed:', metrics.totalOrdersProcessed);
      console.log('Returns by department:', metrics.returnsByDepartment);
      
      // Quality metrics now only include kitchen and design departments
      // Final check is the department that sends orders back, not the one that gets returns
      
      return metrics;
    } catch (error) {
      console.error('Error fetching quality metrics:', error);
      throw error;
    }
  }

  /**
   * Get comprehensive staff dashboard data
   */
  async getStaffDashboard(params: Omit<StaffReportsParams, 'staffId'> = {}): Promise<StaffDashboardData> {
    const queryParams = new URLSearchParams();

    if (params.department) {
      const normalizedDept = this.getBackendDepartmentName(params.department);
      console.log(`Dashboard department mapping: ${params.department} -> ${normalizedDept}`);
      queryParams.append('department', normalizedDept);
    }
    if (params.timeRange) queryParams.append('timeRange', params.timeRange);
    if (params.startDate) queryParams.append('startDate', params.startDate);
    if (params.endDate) queryParams.append('endDate', params.endDate);

    const apiUrl = `/api/staff-reports/dashboard?${queryParams.toString()}`;
    console.log(`Fetching staff dashboard from: ${apiUrl}`);
    
    try {
      const response = await api.get(apiUrl);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching staff dashboard:', error);
      throw error;
    }
  }

  /**
   * Get productivity trends over time
   */
  async getProductivityTrends(params: StaffReportsParams = {}): Promise<ProductivityTrend[]> {
    const queryParams = new URLSearchParams();

    if (params.staffId) queryParams.append('staffId', params.staffId);
    if (params.department) queryParams.append('department', params.department);
    if (params.timeRange) queryParams.append('timeRange', params.timeRange);

    const response = await api.get(`/api/staff-reports/trends?${queryParams.toString()}`);
    return response.data.data;
  }

  /**
   * Get detailed individual staff analytics
   */
  async getIndividualStaffDetails(
    staffId: string,
    params: Omit<StaffReportsParams, 'staffId' | 'department'> = {}
  ): Promise<IndividualStaffDetails> {
    const queryParams = new URLSearchParams();

    if (params.timeRange) queryParams.append('timeRange', params.timeRange);
    if (params.startDate) queryParams.append('startDate', params.startDate);
    if (params.endDate) queryParams.append('endDate', params.endDate);

    const response = await api.get(`/api/staff-reports/staff/${staffId}?${queryParams.toString()}`);
    return response.data.data;
  }

  /**
   * Get department display name
   */
  getDepartmentDisplayName(department: string): string {
    // First normalize the department name to ensure consistent lookup
    const normalizedDepartment = this.getBackendDepartmentName(department);
    
    const departmentNames: { [key: string]: string } = {
      kitchen: 'Kitchen',
      design: 'Design',
      finalCheck: 'Final Check',
      cashier: 'Cashier/POS',
    };
    return departmentNames[normalizedDepartment] || department;
  }
  
  /**
   * Get normalized department name for backend API calls
   */
  getBackendDepartmentName(department: string): string {
    // Map frontend department names to backend API format
    const departmentMapping: { [key: string]: string } = {
      kitchen: 'kitchen',
      design: 'design',
      finalCheck: 'finalCheck', // Backend expects camelCase
      'final-check': 'finalCheck', // Convert kebab-case to camelCase for backend
      'final check': 'finalCheck', // Convert space to camelCase
      finalcheck: 'finalCheck', // Handle lowercase
      cashier: 'cashier',
    };
  
    // Handle case insensitivity by checking lowercase version
    let result = departmentMapping[department] || 
                 departmentMapping[department.toLowerCase()] || 
                 department;
               
    console.log(`Department mapping: ${department} → ${result}`);
    return result;
  }

  /**
   * Get department color for charts
   */
  getDepartmentColor(department: string): string {
    // First normalize the department name to ensure consistent lookup
    const normalizedDepartment = this.getBackendDepartmentName(department);
    
    const departmentColors: { [key: string]: string } = {
      kitchen: '#ef4444', // red
      design: '#3b82f6', // blue
      finalCheck: '#10b981', // green
      cashier: '#f59e0b', // amber
    };
    return departmentColors[normalizedDepartment] || '#6b7280';
  }

  /**
   * Format processing time for display
   */
  formatProcessingTime(minutes: number): string {
    if (minutes < 60) {
      return `${Math.round(minutes)}m`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = Math.round(minutes % 60);
    return `${hours}h ${remainingMinutes}m`;
  }

  /**
   * Get performance rating based on score
   */
  getPerformanceRating(score: number): { label: string; color: string } {
    if (score >= 90) return { label: 'Excellent', color: 'text-green-600' };
    if (score >= 80) return { label: 'Good', color: 'text-blue-600' };
    if (score >= 70) return { label: 'Average', color: 'text-yellow-600' };
    if (score >= 60) return { label: 'Below Average', color: 'text-orange-600' };
    return { label: 'Poor', color: 'text-red-600' };
  }

  /**
   * Calculate percentage change
   */
  calculatePercentageChange(current: number, previous: number): number {
    if (previous === 0) return current > 0 ? 100 : 0;
    return ((current - previous) / previous) * 100;
  }

  /**
   * Format percentage for display
   */
  formatPercentage(value: number, decimals: number = 1): string {
    return `${value.toFixed(decimals)}%`;
  }

  /**
   * Format number with commas
   */
  formatNumber(value: number): string {
    return new Intl.NumberFormat('en-US').format(value);
  }

  /**
   * Get time range display name
   */
  getTimeRangeDisplayName(timeRange: string): string {
    const timeRangeNames: { [key: string]: string } = {
      '7d': 'Last 7 days',
      '14d': 'Last 14 days',
      '30d': 'Last 30 days',
      '3m': 'Last 3 months',
      'all': 'All time',
      'custom': 'Custom range',
    };
    return timeRangeNames[timeRange] || timeRange;
  }

  /**
   * Export staff performance data to CSV
   */
  exportStaffPerformanceCSV(data: StaffPerformanceMetrics[], filename: string = 'staff-performance.csv'): void {
    const headers = [
      'Staff Name',
      'Department',
      'Orders Completed',
      'Avg Processing Time (min)',
      'Orders Sent Back',
      'Quality Score (%)',
      'Productivity Score (%)',
      'Total Working Time (min)',
      'Orders Per Hour',
    ];

    const csvContent = [
      headers.join(','),
      ...data.map(staff => [
        `"${staff.staffName}"`,
        this.getDepartmentDisplayName(staff.department),
        staff.ordersCompleted,
        staff.averageProcessingTime.toFixed(2),
        staff.ordersSentBack,
        staff.qualityScore.toFixed(2),
        staff.productivityScore.toFixed(2),
        staff.totalWorkingTime.toFixed(2),
        staff.ordersPerHour.toFixed(2),
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}

export const staffReportsService = new StaffReportsService();
export default staffReportsService;
