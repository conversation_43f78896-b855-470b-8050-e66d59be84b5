'use client';

import { useEffect, useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { toast } from '@/lib/toast';
import { customerService, type Customer } from '@/services/customer.service';
import { Loader2, ArrowLeft, ShoppingBag, TrendingUp, Package, Calendar, Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Heading } from '@/components/ui/heading';
import { DataTable } from '@/components/ui/data-table';
import { AnalyticsCard } from '@/components/ui/analytics-card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { orderColumns } from './order-columns';
import api from '@/lib/api';

interface OrderItem {
  id: string;
  name: string;
  variant?: string;
  quantity: number;
  price: number;
}

interface Order {
  id: string;
  orderNumber: string;
  status: string;
  total: number;
  subtotal: number;
  items: OrderItem[];
  createdAt: string;
  updatedAt: string;
  source: 'ONLINE' | 'POS';
  paymentMethod?: string;
  deliveryMethod?: string;
}

interface CustomerOrdersData {
  customer: Customer;
  orders: Order[];
  analytics: {
    totalOrders: number;
    totalSpent: number;
    averageOrderValue: number;
    mostOrderedProducts: Array<{
      name: string;
      quantity: number;
      totalSpent: number;
    }>;
  };
}

export default function CustomerOrdersPage() {
  const params = useParams();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<CustomerOrdersData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [sourceFilter, setSourceFilter] = useState<'ALL' | 'ONLINE' | 'POS'>('ALL');

  const loadCustomerOrders = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const customerId = params.customerId as string;
      
      // Load customer details
      const customerResponse = await customerService.getCustomer(customerId);
      const customer = customerResponse.data;
      
      // Load customer orders using multiple identifiers for better accuracy
      const queryParams = new URLSearchParams();

      // Add email for online orders (primary identifier)
      if (customer.email) {
        queryParams.append('email', customer.email);
      }

      // Add phone for POS orders (POS orders are typically linked by phone)
      if (customer.phone) {
        queryParams.append('phone', customer.phone);
      }

      // Add customer ID if available
      if (customer.id) {
        queryParams.append('customerId', customer.id);
      }

      // Add Firebase UID if available
      if ((customer as any).firebaseUid) {
        queryParams.append('firebaseUid', (customer as any).firebaseUid);
      }

      console.log('Customer data:', {
        id: customer.id,
        email: customer.email,
        phone: customer.phone,
        firebaseUid: (customer as any).firebaseUid
      });
      console.log('Fetching orders with params:', queryParams.toString());

      const ordersResponse = await api.get(`/api/orders/customer/all-orders?${queryParams.toString()}`);

      console.log('Orders API response:', ordersResponse.data);

      if (!ordersResponse.data.success) {
        console.error('API Error:', ordersResponse.data.error);
        throw new Error(ordersResponse.data.error?.message || 'Failed to fetch orders');
      }

      // Check the structure of the response
      console.log('API Response structure:', {
        hasData: !!ordersResponse.data.data,
        dataKeys: ordersResponse.data.data ? Object.keys(ordersResponse.data.data) : [],
        dataType: typeof ordersResponse.data.data
      });

      // Handle different possible response structures
      let onlineOrders = [];
      let posOrders = [];

      if (ordersResponse.data.data) {
        // Check if it's the expected structure with separate arrays
        if (ordersResponse.data.data.onlineOrders && ordersResponse.data.data.posOrders) {
          onlineOrders = ordersResponse.data.data.onlineOrders || [];
          posOrders = ordersResponse.data.data.posOrders || [];
        }
        // Check if it's a single array of all orders
        else if (Array.isArray(ordersResponse.data.data)) {
          const allOrders = ordersResponse.data.data;
          onlineOrders = allOrders.filter((order: any) => order.source === 'ONLINE' || !order.source);
          posOrders = allOrders.filter((order: any) => order.source === 'POS');
        }
        // Check if orders are directly in data
        else if (ordersResponse.data.data.results && Array.isArray(ordersResponse.data.data.results)) {
          const allOrders = ordersResponse.data.data.results;
          onlineOrders = allOrders.filter((order: any) => order.source === 'ONLINE' || !order.source);
          posOrders = allOrders.filter((order: any) => order.source === 'POS');
        }
      }

      console.log('Processed orders:', {
        onlineCount: onlineOrders.length,
        posCount: posOrders.length,
        onlineOrders: onlineOrders.slice(0, 2), // Log first 2 for debugging
        posOrders: posOrders.slice(0, 2) // Log first 2 for debugging
      });

      // Format orders to have consistent structure
      const formattedOnlineOrders = onlineOrders.map((order: any) => ({
        ...order,
        source: 'ONLINE' as const,
        orderNumber: order.orderNumber || order.id,
        total: order.total || 0,
        subtotal: order.subtotal || 0,
        items: order.items || [],
        paymentMethod: order.payment?.paymentMethod || order.paymentMethod,
        deliveryMethod: order.deliveryMethod
      }));

      const formattedPosOrders = posOrders.map((order: any) => ({
        ...order,
        source: 'POS' as const,
        orderNumber: order.orderNumber || order.id,
        total: order.totalAmount || order.total || 0,
        subtotal: order.subtotal || order.totalAmount || 0,
        items: order.items || [],
        paymentMethod: order.paymentMethod,
        deliveryMethod: 'PICKUP' // POS orders are typically pickup
      }));

      const orders = [...formattedOnlineOrders, ...formattedPosOrders]
        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
      
      // Calculate analytics
      const analytics = calculateAnalytics(orders);
      
      setData({
        customer,
        orders,
        analytics
      });
      
    } catch (error: any) {
      console.error('Error loading customer orders:', error);
      setError(error.message || 'Failed to load customer orders');
      toast.error('Failed to load customer orders');
    } finally {
      setLoading(false);
    }
  };

  const calculateAnalytics = (orders: Order[]) => {
    const totalOrders = orders.length;
    const totalSpent = orders.reduce((sum, order) => sum + order.total, 0);
    const averageOrderValue = totalOrders > 0 ? totalSpent / totalOrders : 0;
    
    // Calculate most ordered products
    const productMap = new Map<string, { quantity: number; totalSpent: number }>();
    
    orders.forEach(order => {
      order.items.forEach(item => {
        const key = item.variant ? `${item.name} (${item.variant})` : item.name;
        const existing = productMap.get(key) || { quantity: 0, totalSpent: 0 };
        productMap.set(key, {
          quantity: existing.quantity + item.quantity,
          totalSpent: existing.totalSpent + (item.price * item.quantity)
        });
      });
    });
    
    const mostOrderedProducts = Array.from(productMap.entries())
      .map(([name, data]) => ({ name, ...data }))
      .sort((a, b) => b.quantity - a.quantity)
      .slice(0, 5);
    
    return {
      totalOrders,
      totalSpent,
      averageOrderValue,
      mostOrderedProducts
    };
  };

  useEffect(() => {
    loadCustomerOrders();
  }, [params.customerId]);

  if (loading) {
    return (
      <div className="flex h-[400px] items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className="flex h-[400px] flex-col items-center justify-center gap-4">
        <p className="text-muted-foreground">{error || 'Failed to load customer orders'}</p>
        <Button onClick={loadCustomerOrders}>Try Again</Button>
      </div>
    );
  }

  const { customer, orders, analytics } = data;

  // Filter orders based on source
  const filteredOrders = sourceFilter === 'ALL'
    ? orders
    : orders.filter(order => order.source === sourceFilter);

  // Calculate source-specific analytics
  const onlineOrders = orders.filter(order => order.source === 'ONLINE');
  const posOrders = orders.filter(order => order.source === 'POS');

  // Analytics cards data
  const analyticsCards = [
    {
      title: 'Total Orders',
      value: analytics.totalOrders.toString(),
      icon: ShoppingBag,
      description: `${onlineOrders.length} online, ${posOrders.length} POS`
    },
    {
      title: 'Total Spent',
      value: `AED ${analytics.totalSpent.toFixed(2)}`,
      icon: TrendingUp,
      description: 'Across all orders'
    },
    {
      title: 'Average Order',
      value: `AED ${analytics.averageOrderValue.toFixed(2)}`,
      icon: Package,
      description: 'Per order value'
    },
    {
      title: 'Customer Since',
      value: new Date(customer.createdAt).toLocaleDateString(),
      icon: Calendar,
      description: 'Member for ' + Math.floor((Date.now() - new Date(customer.createdAt).getTime()) / (1000 * 60 * 60 * 24)) + ' days'
    }
  ];

  return (
    <div className="flex-1 space-y-4">
      <div className="flex items-center justify-between">
        <Heading 
          title={`Orders for ${customer.firstName} ${customer.lastName}`}
          description={`View all orders placed by ${customer.email}`}
        />
        <Button variant="outline" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
      </div>
      <Separator />
      
      {/* Analytics Cards */}
      <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
        {analyticsCards.map((card) => (
          <AnalyticsCard
            key={card.title}
            title={card.title}
            value={card.value}
            icon={card.icon}
          />
        ))}
      </div>

      {/* Most Ordered Products */}
      {analytics.mostOrderedProducts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Most Ordered Products</CardTitle>
            <CardDescription>Top 5 products ordered by this customer</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.mostOrderedProducts.map((product, index) => (
                <div key={product.name} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Badge variant="outline" className="w-8 h-8 rounded-full flex items-center justify-center">
                      {index + 1}
                    </Badge>
                    <div>
                      <p className="font-medium">{product.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {product.quantity} orders • AED {product.totalSpent.toFixed(2)} total
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Orders Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Order History</CardTitle>
              <CardDescription>
                {sourceFilter === 'ALL'
                  ? `All orders placed by this customer (${orders.length} total)`
                  : `${sourceFilter} orders (${filteredOrders.length} of ${orders.length} total)`
                }
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <Select value={sourceFilter} onValueChange={(value: 'ALL' | 'ONLINE' | 'POS') => setSourceFilter(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">All Orders</SelectItem>
                  <SelectItem value="ONLINE">Online Only</SelectItem>
                  <SelectItem value="POS">POS Only</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {filteredOrders.length > 0 ? (
            <DataTable
              columns={orderColumns}
              data={filteredOrders}
              searchKey="orderNumber"
            />
          ) : (
            <div className="text-center py-8">
              <ShoppingBag className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-2 text-sm font-semibold text-gray-900">No orders found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {sourceFilter === 'ALL'
                  ? "This customer hasn't placed any orders yet."
                  : `No ${sourceFilter.toLowerCase()} orders found for this customer.`
                }
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
