import { Collection } from '@/types/collection';

class CollectionService {
  private baseUrl = process.env.NEXT_PUBLIC_API_URL;

  /**
   * Get all published collections
   */
  async getCollections(params?: { page?: number; limit?: number }): Promise<Collection[]> {
    try {
      if (!this.baseUrl) {
        console.error('API URL is not configured. NEXT_PUBLIC_API_URL:', process.env.NEXT_PUBLIC_API_URL);
        return [];
      }

      const searchParams = new URLSearchParams();
      searchParams.append('status', 'PUBLISHED');
      if (params?.page) searchParams.append('page', params.page.toString());
      if (params?.limit) searchParams.append('limit', params.limit.toString());
      searchParams.append('include', 'products');

      const apiUrl = `${this.baseUrl}/api/collections/public?${searchParams.toString()}`;
      console.log('Fetching collections from:', apiUrl);

      const response = await fetch(apiUrl, {
        next: { revalidate: 60 }, // Cache for 1 minute
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Failed to fetch collections:', {
          status: response.status,
          statusText: response.statusText,
          error: errorText,
          url: apiUrl
        });
        return [];
      }

      const result = await response.json();
      console.log('Collections API response:', JSON.stringify(result, null, 2));
      
      // Handle different response formats
      let collections: Collection[] = [];
      
      // Format 1: Direct array
      if (Array.isArray(result)) {
        collections = result;
      } 
      // Format 2: { data: Collection[] }
      else if (result.data && Array.isArray(result.data)) {
        collections = result.data;
      }
      // Format 3: { data: { collections: Collection[] } }
      else if (result.data?.collections && Array.isArray(result.data.collections)) {
        collections = result.data.collections;
      }
      
      console.log(`Found ${collections.length} collections`);
      return collections;
    } catch (error) {
      console.error('Error in getCollections:', error);
      return [];
    }
  }

  /**
   * Get a single collection by slug
   */
  async getCollectionBySlug(slug: string): Promise<Collection> {
    try {
      if (!this.baseUrl) {
        console.error('API URL is not configured. NEXT_PUBLIC_API_URL:', process.env.NEXT_PUBLIC_API_URL);
        throw new Error('API URL is not configured');
      }

      const searchParams = new URLSearchParams();
      searchParams.append('include', 'products');

      const apiUrl = `${this.baseUrl}/api/collections/public/${slug}?${searchParams.toString()}`;
      console.log('Fetching collection from:', apiUrl);

      const response = await fetch(apiUrl, {
        next: { revalidate: 60 }, // Cache for 1 minute
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Failed to fetch collection:', {
          status: response.status,
          statusText: response.statusText,
          error: errorText,
          url: apiUrl
        });
        throw new Error(`Failed to fetch collection: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log('Collection API response:', JSON.stringify(result, null, 2));
      
      // Handle different response formats
      let collection: Collection | null = null;
      
      // Format 1: Direct object
      if (result && typeof result === 'object' && !Array.isArray(result)) {
        collection = result;
      } 
      // Format 2: { data: Collection }
      else if (result.data && typeof result.data === 'object' && !Array.isArray(result.data)) {
        collection = result.data;
      }
      // Format 3: { data: { collection: Collection } }
      else if (result.data?.collection && typeof result.data.collection === 'object') {
        collection = result.data.collection;
      }
      
      if (!collection) {
        console.error('Invalid collection response format:', result);
        throw new Error('Invalid collection response format');
      }
      
      console.log('Parsed collection:', collection);
      return collection;
    } catch (error) {
      console.error('Error in getCollectionBySlug:', error);
      throw error; // Re-throw to be handled by the caller
    }
  }

  /**
   * Get featured collections
   */
  async getFeaturedCollections(limit = 3): Promise<Collection[]> {
    const collections = await this.getCollections({ limit });
    return collections.slice(0, limit);
  }
}

export const collectionService = new CollectionService();
