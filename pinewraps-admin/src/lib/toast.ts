import { toast as sonnerToast } from 'sonner';

/**
 * Unified toast utility using Son<PERSON> for consistent notifications across the admin panel
 */
export const toast = {
  /**
   * Show success toast
   */
  success: (message: string, options?: { description?: string; action?: any }) => {
    return sonnerToast.success(message, {
      description: options?.description,
      action: options?.action,
    });
  },

  /**
   * Show error toast
   */
  error: (message: string, options?: { description?: string; action?: any }) => {
    return sonnerToast.error(message, {
      description: options?.description,
      action: options?.action,
    });
  },

  /**
   * Show warning toast
   */
  warning: (message: string, options?: { description?: string; action?: any }) => {
    return sonnerToast.warning(message, {
      description: options?.description,
      action: options?.action,
    });
  },

  /**
   * Show info toast
   */
  info: (message: string, options?: { description?: string; action?: any }) => {
    return sonnerToast.info(message, {
      description: options?.description,
      action: options?.action,
    });
  },

  /**
   * Show loading toast
   */
  loading: (message: string, options?: { description?: string }) => {
    return sonnerToast.loading(message, {
      description: options?.description,
    });
  },

  /**
   * Show promise toast (for async operations)
   */
  promise: <T>(
    promise: Promise<T>,
    options: {
      loading: string;
      success: string | ((data: T) => string);
      error: string | ((error: any) => string);
      description?: string;
    }
  ) => {
    return sonnerToast.promise(promise, {
      loading: options.loading,
      success: options.success,
      error: options.error,
      description: options.description,
    });
  },

  /**
   * Dismiss a specific toast
   */
  dismiss: (toastId?: string | number) => {
    return sonnerToast.dismiss(toastId);
  },

  /**
   * Custom toast with full control
   * @param render A function that returns a ReactElement
   * @param options Toast options
   */
  custom: (render: (id: string | number) => React.ReactElement, options?: any) => {
    return sonnerToast.custom(render, options);
  },

  // CRUD operation specific toasts
  crud: {
    /**
     * Success toasts for CRUD operations
     */
    created: (entityName: string) => {
      return sonnerToast.success(`${entityName} created successfully`, {
        description: `The ${entityName.toLowerCase()} has been added to the system.`,
      });
    },

    updated: (entityName: string) => {
      return sonnerToast.success(`${entityName} updated successfully`, {
        description: `The ${entityName.toLowerCase()} changes have been saved.`,
      });
    },

    deleted: (entityName: string) => {
      return sonnerToast.success(`${entityName} deleted successfully`, {
        description: `The ${entityName.toLowerCase()} has been removed from the system.`,
      });
    },

    bulkDeleted: (count: number, entityName: string) => {
      return sonnerToast.success(`${count} ${entityName.toLowerCase()}s deleted successfully`, {
        description: `${count} items have been removed from the system.`,
      });
    },

    /**
     * Error toasts for CRUD operations
     */
    createError: (entityName: string, error?: string) => {
      return sonnerToast.error(`Failed to create ${entityName.toLowerCase()}`, {
        description: error || `There was an error creating the ${entityName.toLowerCase()}. Please try again.`,
      });
    },

    updateError: (entityName: string, error?: string) => {
      return sonnerToast.error(`Failed to update ${entityName.toLowerCase()}`, {
        description: error || `There was an error updating the ${entityName.toLowerCase()}. Please try again.`,
      });
    },

    deleteError: (entityName: string, error?: string) => {
      return sonnerToast.error(`Failed to delete ${entityName.toLowerCase()}`, {
        description: error || `There was an error deleting the ${entityName.toLowerCase()}. Please try again.`,
      });
    },

    fetchError: (entityName: string, error?: string) => {
      return sonnerToast.error(`Failed to load ${entityName.toLowerCase()}s`, {
        description: error || `There was an error loading the ${entityName.toLowerCase()}s. Please refresh the page.`,
      });
    },

    /**
     * Loading toasts for CRUD operations
     */
    creating: (entityName: string) => {
      return sonnerToast.loading(`Creating ${entityName.toLowerCase()}...`, {
        description: 'Please wait while we save your changes.',
      });
    },

    updating: (entityName: string) => {
      return sonnerToast.loading(`Updating ${entityName.toLowerCase()}...`, {
        description: 'Please wait while we save your changes.',
      });
    },

    deleting: (entityName: string) => {
      return sonnerToast.loading(`Deleting ${entityName.toLowerCase()}...`, {
        description: 'Please wait while we remove the item.',
      });
    },

    loading: (entityName: string) => {
      return sonnerToast.loading(`Loading ${entityName.toLowerCase()}s...`, {
        description: 'Please wait while we fetch the data.',
      });
    },
  },

  // Status change toasts
  status: {
    activated: (entityName: string) => {
      return sonnerToast.success(`${entityName} activated`, {
        description: `The ${entityName.toLowerCase()} is now active.`,
      });
    },

    deactivated: (entityName: string) => {
      return sonnerToast.success(`${entityName} deactivated`, {
        description: `The ${entityName.toLowerCase()} is now inactive.`,
      });
    },

    published: (entityName: string) => {
      return sonnerToast.success(`${entityName} published`, {
        description: `The ${entityName.toLowerCase()} is now live.`,
      });
    },

    unpublished: (entityName: string) => {
      return sonnerToast.success(`${entityName} unpublished`, {
        description: `The ${entityName.toLowerCase()} is now hidden.`,
      });
    },
  },

  // Import/Export toasts
  io: {
    exportStarted: (entityName: string) => {
      return sonnerToast.loading(`Exporting ${entityName.toLowerCase()}s...`, {
        description: 'Your download will start shortly.',
      });
    },

    exportSuccess: (entityName: string) => {
      return sonnerToast.success(`${entityName}s exported successfully`, {
        description: 'Your file has been downloaded.',
      });
    },

    importStarted: (entityName: string) => {
      return sonnerToast.loading(`Importing ${entityName.toLowerCase()}s...`, {
        description: 'Please wait while we process your file.',
      });
    },

    importSuccess: (count: number, entityName: string) => {
      return sonnerToast.success(`${count} ${entityName.toLowerCase()}s imported successfully`, {
        description: 'All items have been added to the system.',
      });
    },

    importError: (entityName: string, error?: string) => {
      return sonnerToast.error(`Failed to import ${entityName.toLowerCase()}s`, {
        description: error || 'Please check your file format and try again.',
      });
    },
  },
};

export default toast;
