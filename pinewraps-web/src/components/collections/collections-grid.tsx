import { Collection } from '@/types/collection';
import Link from 'next/link';

import { collectionService } from '@/services/collection.service';

async function getCollections() {
  // Set a high limit to get all published collections
  return collectionService.getCollections({ limit: 100 });
}

export default async function CollectionsGrid() {
  const collections = await getCollections();
  
  if (collections.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground mb-4">No collections found.</p>
        <p className="text-sm text-gray-500">
          Check your internet connection or try again later.
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-12">
      {collections.map((collection: Collection) => (
        <Link key={collection.id} href={`/collections/${collection.slug}`} className="group block bg-gray-50 rounded-2xl overflow-hidden hover:bg-gray-100 transition-colors">
          <div className="p-6 md:p-8">
            <div className="flex items-center justify-between gap-4 mb-4">
              <h3 className="text-lg md:text-xl font-semibold text-gray-900 group-hover:text-gray-700 transition-colors">
                {collection.name}
              </h3>
              <span className="px-3 py-1 text-sm font-medium text-gray-700 bg-white rounded-full border border-gray-200">
                {collection.products?.length || 0} items
              </span>
            </div>
            {collection.description && (
              <p className="text-base text-gray-600 line-clamp-2 mb-6">
                {collection.description}
              </p>
            )}
            <div className="inline-flex items-center text-sm font-medium text-gray-900">
              View Collection
              <svg className="w-4 h-4 ml-2 transition-transform group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </div>
        </Link>
      ))}
    </div>
  );
}
