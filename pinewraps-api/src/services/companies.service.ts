import { prisma } from '../lib/prisma';
import { Company } from '@prisma/client';

export interface CreateCompanyDto {
  name: string;
  email?: string;
  phone?: string;
  address?: string;
}

export interface UpdateCompanyDto {
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
}

export class CompaniesService {
  async createCompany(data: CreateCompanyDto) {
    return await prisma.company.create({
      data
    });
  }

  async getCompanies() {
    return await prisma.company.findMany({
      orderBy: { name: 'asc' }
    });
  }

  async getCompanyById(id: string) {
    return await prisma.company.findUnique({
      where: { id }
    });
  }

  async updateCompany(id: string, data: UpdateCompanyDto) {
    return await prisma.company.update({
      where: { id },
      data
    });
  }

  async deleteCompany(id: string) {
    return await prisma.company.delete({
      where: { id }
    });
  }

  async searchCompanies(search: string) {
    return await prisma.company.findMany({
      where: {
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
          { phone: { contains: search, mode: 'insensitive' } }
        ]
      },
      orderBy: { name: 'asc' },
      take: 10
    });
  }
}

export const companiesService = new CompaniesService();
