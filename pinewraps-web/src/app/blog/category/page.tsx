import { Metada<PERSON> } from 'next';
import Link from 'next/link';
import { BlogService } from '@/services/blog.service';
import { createMetadata } from '@/lib/metadata';

export async function generateMetadata(): Promise<Metadata> {
  return createMetadata({
    title: 'Blog Categories - Pinewraps Dubai',
    description: 'Browse all blog categories from Pinewraps Dubai. Find articles on cakes, flowers, gifting, and more.',
    path: '/blog/category',
  });
}

export const dynamic = 'force-dynamic';

export default async function BlogCategoriesPage() {
  try {
    const { data: categories } = await BlogService.getAllCategories();
    
    return (
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb Navigation */}
        <nav className="text-sm mb-8">
          <ol className="list-none p-0 inline-flex">
            <li className="flex items-center">
              <Link href="/" className="text-gray-500 hover:text-gray-700">Home</Link>
              <span className="mx-2 text-gray-500">/</span>
            </li>
            <li className="flex items-center">
              <Link href="/blog" className="text-gray-500 hover:text-gray-700">Blog</Link>
              <span className="mx-2 text-gray-500">/</span>
            </li>
            <li className="flex items-center">
              <span className="text-gray-900">Categories</span>
            </li>
          </ol>
        </nav>

        {/* Main Title */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900">Blog Categories</h1>
          <p className="mt-4 text-xl text-gray-600">Browse articles by topic</p>
        </div>

        {/* Categories List */}
        {!categories || categories.length === 0 ? (
          <div className="text-center py-10">
            <p className="text-lg text-gray-500">No categories found.</p>
            <Link href="/blog" className="mt-4 inline-block text-blue-600 hover:underline">
              View all blog posts
            </Link>
          </div>
        ) : (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 max-w-6xl mx-auto">
            {categories.map((category) => (
              <Link
                key={category.id}
                href={`/blog/category/${category.slug}`}
                className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition duration-300"
              >
                <h2 className="text-xl font-semibold text-gray-900 mb-2">{category.name}</h2>
                <p className="text-gray-600 line-clamp-2">Browse articles in this category</p>
                <div className="mt-4 text-blue-600 hover:text-blue-800">
                  Browse articles →
                </div>
              </Link>
            ))}
          </div>
        )}
      </div>
    );
  } catch (error) {
    console.error('Error loading categories page:', error);
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4">Blog Categories</h1>
          <p className="text-xl text-gray-600">Browse articles by topic</p>
        </div>
        <div className="text-center py-10">
          <p className="text-lg text-gray-500">Failed to load categories. Please try again later.</p>
          <Link href="/blog" className="mt-4 inline-block text-blue-600 hover:underline">
            Return to all blog posts
          </Link>
        </div>
      </div>
    );
  }
}
