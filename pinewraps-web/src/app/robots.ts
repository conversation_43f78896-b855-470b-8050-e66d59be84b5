import { MetadataRoute } from 'next';

export default function robots(): MetadataRoute.Robots {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://pinewraps.com';
  
  return {
    rules: [
      {
        userAgent: '*',
        allow: '/',
        disallow: [
          '/api/',
          '/admin/',
          '/account/',
          '/dashboard',
          '/pos',
          '/_next/',
          '/auth/',
          '/checkout/',
          '/cart/',
          '/wishlist/',
          '/*.js$',
          '/*.css$',
          '/login/',
          '/_vercel/',
          '/*?dpl=',
          '/thank-you/',
        ],
      },
      {
        userAgent: 'Googlebot',
        allow: '/',
        disallow: [
          '/api/',
          '/admin/',
          '/account/',
          '/_next/',
          '/auth/',
          '/checkout/',
          '/cart/',
          '/wishlist/',
          '/*.js$',
          '/*.css$',
          '/login/',
          '/_vercel/',
          '/*?dpl=',
          '/thank-you/',
        ],
        crawlDelay: 10,
      },
    ],
    sitemap: `${baseUrl}/sitemap.xml`,
    host: baseUrl,
  };
}
