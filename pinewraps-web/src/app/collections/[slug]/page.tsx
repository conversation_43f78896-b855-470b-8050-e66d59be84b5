import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { OptimizedImage } from '@/components/ui/OptimizedImage';
import Link from 'next/link';
import { Breadcrumbs } from '@/components/shared/Breadcrumbs';
import JsonLd from '@/components/seo/JsonLd';

export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  // Await params to ensure it's ready
  const slug = await params.slug;
  try {
    const collection = await getCollection(slug);
    // Use only the seoTitle if available, or just the collection name
    // Use absolute title to override the template in layout.tsx
    const title = { absolute: collection.seoTitle || `${collection.name} Collection` };
    // Use seoDescription if available, or just the description without mentioning Pinewraps
    const description = collection.seoDescription || collection.description || `Explore our ${collection.name} collection`;

    // For canonical URL, we need to use the current domain
    // Use the NEXT_PUBLIC_SITE_URL environment variable
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3002';
    const canonicalUrl = `${baseUrl}/collections/${slug}`;

    return {
      title,
      description,
      keywords: collection.seoKeywords?.join(', '),
      // Set the canonical URL for this page
      alternates: {
        canonical: canonicalUrl,
      },
      openGraph: {
        // For OpenGraph, we need to use a string
        title: typeof title === 'object' && 'absolute' in title ? title.absolute : String(title),
        description,
        url: canonicalUrl, // Also set the URL in OpenGraph
        images: collection.image ? [{ url: collection.image }] : undefined,
        type: 'website',
        siteName: 'Pinewraps',
      },
    };
  } catch (error) {
    return {
      title: { absolute: 'Collection Not Found' },
      description: 'Explore our curated collections of premium gift wrapping products.',
    };
  }
}

import { collectionService } from '@/services/collection.service';
import { Collection } from '@/types/collection';

async function getCollection(slug: string): Promise<Collection> {
  return collectionService.getCollectionBySlug(slug);
}

// Add JSON-LD structured data component
function CollectionJsonLd({ collection }: { collection: Collection }) {
  // Format the collection data for structured data
  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'CollectionPage',
    name: collection.name,
    description: collection.description || '',
    url: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://pinewraps.com'}/collections/${collection.slug}`,
    mainEntity: {
      '@type': 'ItemList',
      itemListElement: collection.products.map((item, index) => {
        // Get the first image URL if available
        const imageUrl = item.product.images && item.product.images.length > 0
          ? item.product.images[0].url
          : '';

        return {
          '@type': 'ListItem',
          position: index + 1,
          item: {
            '@type': 'Product',
            name: item.product.name,
            url: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://pinewraps.com'}/shop/${item.product.slug}`,
            image: imageUrl,
            offers: {
              '@type': 'Offer',
              price: item.product.basePrice,
              priceCurrency: 'AED',
              availability: 'https://schema.org/InStock'
            }
          }
        };
      })
    }
  };

  // Return the JSON-LD as a script tag
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
    />
  );
}

// Add FAQ schema for better search visibility
function FaqJsonLd({ collection }: { collection: Collection }) {
  // Generate dynamic FAQs based on the collection
  const faqs = [
    {
      question: `How can I order from the ${collection.name} collection?`,
      answer: `You can order items from our ${collection.name} collection directly through our website. Simply browse the collection, select your desired items, and proceed to checkout. For custom orders, you can also contact us via WhatsApp or phone.`
    },
    {
      question: `Do you offer same-day delivery for ${collection.name}?`,
      answer: `Yes, we offer same-day delivery across Dubai for orders placed before 2 PM. Our ${collection.name} collection is available for immediate delivery to ensure your special moments are celebrated on time.`
    },
    {
      question: `Can I customize items from the ${collection.name} collection?`,
      answer: `Absolutely! We specialize in creating personalized designs for any occasion. Contact our team through WhatsApp or phone to discuss your specific requirements for the ${collection.name} collection.`
    },
    {
      question: `What areas in Dubai do you deliver the ${collection.name} collection to?`,
      answer: `We deliver our ${collection.name} collection to all areas in Dubai, including Downtown, Dubai Marina, Palm Jumeirah, JBR, Business Bay, and all other neighborhoods.`
    }
  ];

  const faqJsonLd = {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer
      }
    }))
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(faqJsonLd) }}
    />
  );
}

export default async function CollectionPage({ params }: { params: { slug: string } }) {
  let collection: Collection;
  try {
    collection = await getCollection(params.slug);
  } catch (error) {
    notFound();
  }

  return (
    <div>
      {/* Add structured data for SEO */}
      <CollectionJsonLd collection={collection} />
      <FaqJsonLd collection={collection} />
      
      {/* Add breadcrumb structured data */}
      <JsonLd 
        type="breadcrumb" 
        data={[
          { label: 'Home', href: '/' },
          { label: 'Collections', href: '/collections' },
          { label: collection.name }
        ]} 
      />
      {/* Collection Title Section */}
      <div className="w-full bg-gray-50">
        <div className="py-16 px-4 text-center">
          <div className="max-w-4xl mx-auto space-y-8">
            {/* Title */}
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900">
              {collection.name}
            </h1>

            {/* Breadcrumbs */}
            <nav className="py-2" aria-label="Breadcrumb">
              <Breadcrumbs 
                items={[
                  { label: 'Home', href: '/' },
                  { label: 'Collections', href: '/collections' },
                  { label: collection.name }
                ]}
                className="justify-center"
              />
            </nav>

            {/* Description */}
            {collection.description && (
              <p className="text-base md:text-lg text-gray-600 max-w-4xl mx-auto">
                {collection.description}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Products Grid */}
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-medium text-gray-900">
              {collection.name} Products
            </h2>
            <p className="text-sm text-gray-500">
              {collection.products?.length || 0} products
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-x-6 gap-y-8">
            {collection.products?.map(({ product }) => (
              <Link key={product.id} href={`/shop/${product.slug}`} className="group block">
                <div className="relative aspect-square w-full overflow-hidden bg-gray-100 rounded-lg mb-4">
                  <OptimizedImage
                    src={product.images?.find(img => img.isPrimary)?.url || product.images?.[0]?.url || '/placeholder.jpg'}
                    alt={product.name}
                    fill
                    sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                    className="object-cover transition-transform duration-300 ease-in-out group-hover:scale-105"
                    quality={75}
                  />
                </div>
                <div className="mt-3 space-y-1">
                  <h3 className="text-sm font-medium text-gray-900 group-hover:text-gray-600 transition-colors">
                    {product.name}
                  </h3>
                  <div className="text-sm font-medium text-gray-900">
                    AED {product.basePrice.toFixed(2)}
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </div>

      {/* Collection Content Section with CTA */}
      {collection.content && (
        <div className="bg-white border-t border-gray-100 mt-16">
          <div className="container mx-auto px-4 py-16 md:py-24">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12">
              {/* Content Column - Takes up 2/3 of the space */}
              <div className="lg:col-span-2">
                <div
                  className="prose prose-gray max-w-none prose-a:text-primary prose-a:font-medium prose-a:no-underline hover:prose-a:underline prose-a:transition-colors"
                  dangerouslySetInnerHTML={{ __html: collection.content }}
                />
              </div>

              {/* CTA Column - Takes up 1/3 of the space */}
              <div className="lg:col-span-1">
                <div className="sticky top-24 bg-gray-50 rounded-xl overflow-hidden shadow-sm border border-gray-100">
                  <div className="p-6 md:p-8">
                    <h3 className="text-xl font-bold text-gray-900 mb-4">Custom Orders</h3>
                    <p className="text-gray-600 mb-6">
                      Looking for custom cakes or flower arrangements? We specialize in creating personalized designs for any occasion.
                    </p>

                    <div className="space-y-6">
                      {/* Phone */}
                      <div className="flex items-start space-x-4">
                        <div className="bg-primary/10 p-2 rounded-full">
                          <svg className="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                          </svg>
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">Call Us</h4>
                          <a href="tel:+971544044864" className="text-primary hover:underline">
                            +971 54 404 4864
                          </a>
                        </div>
                      </div>

                      {/* WhatsApp */}
                      <div className="flex items-start space-x-4">
                        <div className="bg-green-50 p-2 rounded-full">
                          <svg className="h-5 w-5 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12.04 2c-5.46 0-9.91 4.45-9.91 9.91 0 1.75.46 3.45 1.32 4.95L2.05 22l5.25-1.38c1.45.79 3.08 1.21 4.74 1.21 5.46 0 9.91-4.45 9.91-9.91 0-2.65-1.03-5.14-2.9-7.01A9.816 9.816 0 0012.04 2m.01 1.67c2.2 0 4.26.86 5.82 2.42a8.225 8.225 0 012.41 5.83c0 4.54-3.7 8.23-8.24 8.23-1.48 0-2.93-.39-4.19-1.15l-.3-.17-3.12.82.83-3.04-.2-.32a8.188 8.188 0 01-1.26-4.38c.01-4.54 3.7-8.24 8.25-8.24M8.53 7.33c-.16 0-.43.06-.66.31-.22.25-.87.86-.87 2.07 0 1.22.89 2.39 1 2.56.14.17 1.76 2.67 4.25 3.73.59.27 1.05.42 1.41.53.59.19 1.13.16 1.56.1.48-.07 1.46-.6 1.67-1.18.21-.58.21-1.07.15-1.18-.07-.1-.23-.16-.48-.27-.25-.14-1.47-.74-1.69-.82-.23-.08-.37-.12-.56.12-.16.25-.64.81-.78.97-.15.17-.29.19-.53.07-.26-.13-1.06-.39-2-1.23-.74-.66-1.23-1.47-1.38-1.72-.12-.24-.01-.39.11-.5.11-.11.27-.29.37-.44.13-.14.17-.25.25-.41.08-.17.04-.31-.02-.43-.06-.11-.56-1.35-.77-1.84-.2-.48-.4-.42-.56-.43-.14 0-.3-.01-.47-.01z" />
                          </svg>
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">WhatsApp</h4>
                          <a
                            href="https://wa.me/971544044864"
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-green-600 hover:underline"
                          >
                            Message on WhatsApp
                          </a>
                        </div>
                      </div>

                      {/* Email */}
                      <div className="flex items-start space-x-4">
                        <div className="bg-primary/10 p-2 rounded-full">
                          <svg className="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                          </svg>
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">Email</h4>
                          <a
                            href="mailto:<EMAIL>"
                            className="text-primary hover:underline"
                          >
                            <EMAIL>
                          </a>
                        </div>
                      </div>
                    </div>

                    <div className="mt-8">
                      <a
                        href={`https://wa.me/971544044864?text=Hello%20Pinewraps!%20I'm%20interested%20in%20a%20custom%20order%20for%20${collection.name}.`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="block w-full py-3 px-4 bg-primary hover:bg-primary/90 text-white font-medium text-center rounded-lg transition-colors shadow-sm"
                      >
                        Inquire Now
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
