#!/usr/bin/env node

/**
 * Database Protection System
 * Prevents accidental database resets and destructive operations
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Load environment variables
require('dotenv').config();

const PRODUCTION_INDICATORS = [
  'supabase.co',
  'amazonaws.com',
  'planetscale.com',
  'railway.app',
  'heroku.com',
  'vercel.com',
  'production',
  'prod'
];

const DANGEROUS_COMMANDS = [
  'db:reset',
  'migrate:reset',
  'db:push --force-reset',
  'migrate:dev --create-only',
  'db:seed --reset'
];

class DatabaseProtection {
  constructor() {
    this.databaseUrl = process.env.DATABASE_URL || process.env.DIRECT_URL;
    this.productionProtection = process.env.PRODUCTION_PROTECTION === 'true';
    this.environment = process.env.ENVIRONMENT || process.env.NODE_ENV;
    this.allowReset = process.env.ALLOW_DATABASE_RESET === 'true';
  }

  isProductionDatabase() {
    if (!this.databaseUrl) {
      console.log('⚠️  No database URL found');
      return true; // Err on the side of caution
    }

    return PRODUCTION_INDICATORS.some(indicator => 
      this.databaseUrl.toLowerCase().includes(indicator.toLowerCase())
    );
  }

  isProductionEnvironment() {
    if (this.productionProtection) return true;
    if (this.environment && ['production', 'prod'].includes(this.environment.toLowerCase())) return true;
    return false;
  }

  async confirmDestructiveOperation(operation) {
    if (!this.isProductionDatabase() && !this.isProductionEnvironment()) {
      return true; // Allow in development
    }

    console.log('\n🚨 DANGER: DESTRUCTIVE DATABASE OPERATION DETECTED 🚨');
    console.log('═══════════════════════════════════════════════════════');
    console.log(`Operation: ${operation}`);
    console.log(`Database: ${this.maskDatabaseUrl()}`);
    console.log(`Environment: ${this.environment || 'unknown'}`);
    console.log(`Production Protection: ${this.productionProtection ? 'ENABLED' : 'DISABLED'}`);
    console.log('═══════════════════════════════════════════════════════');

    if (this.isProductionDatabase()) {
      console.log('❌ PRODUCTION DATABASE DETECTED - Operation BLOCKED');
      console.log('This appears to be a production database. Destructive operations are not allowed.');
      return false;
    }

    if (!this.allowReset) {
      console.log('❌ DATABASE RESET DISABLED - Operation BLOCKED');
      console.log('ALLOW_DATABASE_RESET is set to false. Set it to true to allow this operation.');
      return false;
    }

    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    return new Promise((resolve) => {
      rl.question('\n⚠️  Type "CONFIRM RESET" to proceed (anything else will cancel): ', (answer) => {
        rl.close();
        const confirmed = answer.trim() === 'CONFIRM RESET';
        if (confirmed) {
          console.log('✅ Operation confirmed by user');
        } else {
          console.log('❌ Operation cancelled by user');
        }
        resolve(confirmed);
      });
    });
  }

  maskDatabaseUrl() {
    if (!this.databaseUrl) return 'No URL';
    
    // Mask sensitive parts of the URL
    return this.databaseUrl
      .replace(/\/\/[^:]+:[^@]+@/, '//***:***@')
      .replace(/password=[^&]+/g, 'password=***');
  }

  async checkCommand(command) {
    const isDangerous = DANGEROUS_COMMANDS.some(dangerous => 
      command.toLowerCase().includes(dangerous.toLowerCase())
    );

    if (isDangerous) {
      const allowed = await this.confirmDestructiveOperation(command);
      if (!allowed) {
        console.log('\n🛡️  Database protection prevented a destructive operation');
        process.exit(1);
      }
    }

    return true;
  }

  createBackup() {
    // This would integrate with your backup system
    console.log('📦 Creating automatic backup before schema change...');
    // Implementation would depend on your backup strategy
  }
}

// Export for use in other scripts
module.exports = DatabaseProtection;

// CLI usage
if (require.main === module) {
  const protection = new DatabaseProtection();
  const command = process.argv.slice(2).join(' ');
  
  if (command) {
    protection.checkCommand(command).then(allowed => {
      if (allowed) {
        console.log('✅ Command allowed to proceed');
      }
    });
  } else {
    console.log('Database Protection System');
    console.log('Usage: node db-protection.js <command>');
    console.log(`Production Database: ${protection.isProductionDatabase() ? 'YES' : 'NO'}`);
    console.log(`Production Environment: ${protection.isProductionEnvironment() ? 'YES' : 'NO'}`);
    console.log(`Reset Allowed: ${protection.allowReset ? 'YES' : 'NO'}`);
  }
}
