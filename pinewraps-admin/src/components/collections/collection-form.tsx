'use client';

import { useState } from 'react';
import { Collection, CollectionStatus, CreateCollectionDTO } from '@/types/collection';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { But<PERSON> } from '@/components/ui/button';
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
  FormDescription,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { RichTextEditor } from '@/components/ui/rich-text-editor';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { CollectionImageUpload } from './collection-image-upload';
import { CollectionProductSelect } from './collection-product-select';
import { CollectionKeywordInput } from './collection-keyword-input';
import { Card, CardContent } from '@/components/ui/card';

const formSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  slug: z.string().optional(),
  description: z.string().optional(),
  content: z.string().optional(),
  status: z.nativeEnum(CollectionStatus),
  image: z.string().optional(),
  seoTitle: z.string().optional(),
  seoDescription: z.string().optional(),
  seoKeywords: z.array(z.string()),
  products: z.array(z.string()),
});

interface CollectionFormProps {
  initialData?: Collection;
  onSubmit: (data: CreateCollectionDTO) => Promise<void>;
  loading?: boolean;
}

export function CollectionForm({ initialData, onSubmit, loading }: CollectionFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: initialData?.name || '',
      slug: initialData?.slug || '',
      description: initialData?.description || '',
      content: initialData?.content || '',
      status: initialData?.status || CollectionStatus.DRAFT,
      image: initialData?.image || '',
      seoTitle: initialData?.seoTitle || '',
      seoDescription: initialData?.seoDescription || '',
      seoKeywords: initialData?.seoKeywords || [],
      products: initialData?.products.map(p => p.productId) || [],
    },
  });

  const handleSubmit = async (values: z.infer<typeof formSchema>) => {
    // Ensure required fields are present for CreateCollectionDTO
    const data: CreateCollectionDTO = {
      name: values.name,
      description: values.description,
      content: values.content,
      status: values.status,
      image: values.image,
      seoTitle: values.seoTitle,
      seoDescription: values.seoDescription,
      seoKeywords: values.seoKeywords,
      products: values.products
    };
    try {
      setIsSubmitting(true);
      await onSubmit(data);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8 grid grid-cols-1 md:grid-cols-4 gap-8">
        {/* Main Content - 3 columns */}
        <div className="col-span-3 space-y-6">
          {/* Basic Information */}
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-semibold">Collection Details</h3>
                </div>
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Collection Name</FormLabel>
                        <FormControl>
                          <Input {...field} disabled={loading} placeholder="Enter collection name" className="text-lg" />
                        </FormControl>
                        <FormDescription>
                          A unique name for your collection that customers will see.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="slug"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>URL Slug</FormLabel>
                        <FormControl>
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-gray-500">/collections/</span>
                            <Input {...field} disabled={loading} placeholder="url-slug" className="flex-1" />
                          </div>
                        </FormControl>
                        <FormDescription>
                          The URL slug for this collection. Leave empty to auto-generate from name.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea 
                            {...field} 
                            disabled={loading} 
                            placeholder="Enter collection description"
                            className="min-h-[100px]"
                          />
                        </FormControl>
                        <FormDescription>
                          Provide a detailed description of what this collection represents.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="content"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Content</FormLabel>
                        <FormControl>
                          <RichTextEditor
                            content={field.value || ''}
                            onChange={field.onChange}
                            placeholder="Enter detailed content for SEO ranking"
                          />
                        </FormControl>
                        <FormDescription>
                          Add rich formatted content to help this collection rank better in search engines.
                          Use headings, lists, and other formatting to make your content more engaging.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Collection Image */}
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-xl font-semibold">Collection Image</h3>
                </div>
                <FormField
                  control={form.control}
                  name="image"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <div className="p-4 border-2 border-dashed rounded-lg">
                          <CollectionImageUpload
                            value={field.value}
                            onChange={field.onChange}
                            onRemove={() => field.onChange('')}
                            disabled={loading}
                          />
                        </div>
                      </FormControl>
                      <FormDescription>
                        Upload a high-quality image that represents this collection.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Products */}
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-xl font-semibold">Products in Collection</h3>
                </div>
                <FormField
                  control={form.control}
                  name="products"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <CollectionProductSelect
                          value={field.value}
                          onChange={field.onChange}
                          disabled={loading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* SEO Settings */}
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-xl font-semibold">SEO Settings</h3>
                </div>
                <FormField
                  control={form.control}
                  name="seoTitle"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>SEO Title</FormLabel>
                      <FormControl>
                        <Input {...field} disabled={loading} placeholder="Enter SEO title" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="seoDescription"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>SEO Description</FormLabel>
                      <FormControl>
                        <Textarea {...field} disabled={loading} placeholder="Enter SEO description" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="seoKeywords"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>SEO Keywords</FormLabel>
                      <FormControl>
                        <CollectionKeywordInput
                          value={field.value}
                          onChange={field.onChange}
                          disabled={loading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar - 1 column */}
        <div className="space-y-6">
          {/* Collection Status */}
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-xl font-semibold">Collection Status</h3>
                </div>
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          disabled={loading}
                        >
                          <FormControl>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value={CollectionStatus.DRAFT}>Draft</SelectItem>
                            <SelectItem value={CollectionStatus.PUBLISHED}>Published</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Set the collection as draft while you work on it, or publish it to make it live.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="space-y-4">
            <Button 
              type="submit" 
              disabled={loading || isSubmitting}
              size="lg"
              className="w-full"
            >
              {isSubmitting ? 'Saving...' : initialData ? 'Update Collection' : 'Create Collection'}
            </Button>
            <Button 
              type="button" 
              variant="outline" 
              size="lg"
              className="w-full"
              onClick={() => window.history.back()}
            >
              Cancel
            </Button>
          </div>
        </div>


      </form>
    </Form>
  );
}
