'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearch<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import PageTitle from "@/components/ui/page-title";
import Link from "next/link";
import { getProducts, formatPrice, Category, SortOption } from "@/services/api";
import { Product } from "@/types/product";
import ProductGridImage from "@/components/shop/product-grid-image";
import ErrorMessage from "@/components/shop/error-message";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { SubcategoryFilters } from "@/components/filters/subcategory-filters";
import { Package } from "lucide-react";

const sortOptions: { value: SortOption; label: string }[] = [
  { value: 'createdAt:desc', label: 'Newest First' },
  { value: 'createdAt:asc', label: 'Oldest First' },
  { value: 'basePrice:desc', label: 'Price: High to Low' },
  { value: 'basePrice:asc', label: 'Price: Low to High' },
];

function getProductPrice(product: Product): { min: number; max: number | null } {
  // Special handling for Sets category
  if (product.category?.name === 'Sets') {
    // For Sets, we want to show the final price (base + all four options combined)
    // Find the highest price variants for the 4 cake options
    let totalPrice = product.basePrice;

    // Get unique option types for cake flavors
    const cakeFlavorOptions = product.variants
      .filter(variant =>
        variant.values.some(val =>
          val.value.option.name.toLowerCase().includes('cake') ||
          val.value.option.name.toLowerCase().includes('flavor')
        )
      );

    // Get the 4 highest priced variants (assuming 4 cakes in a set)
    const highestPriceVariants = cakeFlavorOptions
      .sort((a, b) => b.price - a.price)
      .slice(0, 4);

    // Add the prices of the highest variants to the base price
    highestPriceVariants.forEach(variant => {
      totalPrice += variant.price;
    });

    return { min: totalPrice, max: null };
  }

  // For other categories, use the existing logic
  // If no variants, return base price
  if (!product.variants || product.variants.length === 0) {
    return { min: product.basePrice, max: null };
  }

  const allPrices: number[] = [];

  // Get all variant prices
  product.variants.forEach(variant => {
    if (typeof variant.price === 'number') {
      allPrices.push(variant.price);
    }
  });

  // If no prices found, return base price
  if (allPrices.length === 0) {
    return { min: product.basePrice, max: null };
  }

  // Return min and max prices
  const min = Math.min(...allPrices);
  const max = Math.max(...allPrices);

  return {
    min,
    max: min !== max ? max : null
  };
}

// Helper function to check if product has ready stock variants
function hasReadyStockVariants(product: Product): boolean {
  return product.variants?.some(variant => variant.isReadyStock === true) || false;
}

// Helper function to get ready stock count
function getReadyStockCount(product: Product): number {
  if (!product.variants) return 0;
  return product.variants
    .filter(variant => variant.isReadyStock === true)
    .reduce((total, variant) => total + (variant.readyStockQty || 0), 0);
}

// Fallback component for Suspense boundary
function ShopPageFallback() {
  return (
    <>
      <PageTitle
        title="Shop"
        breadcrumbs={[
          { label: "Home", href: "/" },
          { label: "Shop", href: "/shop" }
        ]}
      />
      <div className="min-h-screen bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Filters skeleton */}
          <div className="mb-8 flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex flex-wrap gap-4 items-center animate-pulse">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="bg-gray-100 rounded-md h-10 w-24"></div>
              ))}
            </div>
            <div className="bg-gray-100 rounded-md h-10 w-[200px] animate-pulse"></div>
          </div>

          {/* Products skeleton */}
          <div className="animate-pulse">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {[...Array(12)].map((_, i) => (
                <div key={i} className="rounded-lg overflow-hidden border border-gray-100">
                  <div className="bg-gray-100 h-64 w-full"></div>
                  <div className="p-4">
                    <div className="bg-gray-100 h-6 w-3/4 mb-2 rounded"></div>
                    <div className="bg-gray-100 h-4 w-1/2 rounded"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

// Main shop page content component
function ShopPageContent() {
  const searchParams = useSearchParams();
  const router = useRouter();

  const [loading, setLoading] = useState(true);
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Initialize state from URL parameters or defaults
  const [currentPage, setCurrentPage] = useState(() => {
    const page = searchParams.get('page');
    return page ? parseInt(page, 10) : 1;
  });

  const [selectedCategory, setSelectedCategory] = useState<string>(() => {
    return searchParams.get('category') || '';
  });

  const [selectedSort, setSelectedSort] = useState<SortOption>(() => {
    return (searchParams.get('sort') as SortOption) || 'createdAt:desc';
  });

  // Get subcategory from URL for filtering
  const selectedSubcategory = searchParams.get('subcategory') || '';

  const [totalPages, setTotalPages] = useState(1);
  const [totalProducts, setTotalProducts] = useState(0);
  const productsPerPage = 12;

  // Add canonical tag for SEO
  useEffect(() => {
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://pinewraps.com';
    const canonicalUrl = `${baseUrl}/shop`;

    // Remove existing canonical tag if any
    const existingCanonical = document.querySelector('link[rel="canonical"]');
    if (existingCanonical) {
      existingCanonical.remove();
    }

    // Add new canonical tag
    const canonical = document.createElement('link');
    canonical.rel = 'canonical';
    canonical.href = canonicalUrl;
    document.head.appendChild(canonical);

    // Cleanup function to remove canonical tag when component unmounts
    return () => {
      const canonicalToRemove = document.querySelector('link[rel="canonical"]') as HTMLLinkElement;
      if (canonicalToRemove && canonicalToRemove.href === canonicalUrl) {
        canonicalToRemove.remove();
      }
    };
  }, []);

  // Helper function to create query strings
  const createQueryString = (name: string, value: string) => {
    const params = new URLSearchParams(searchParams.toString());
    if (value) {
      params.set(name, value);
    } else {
      params.delete(name);
    }
    // Reset page when changing filters
    if (name !== 'page') {
      params.delete('page');
    }
    return params.toString();
  };

  // Update URL when state changes
  useEffect(() => {
    const params = new URLSearchParams();

    if (currentPage > 1) {
      params.set('page', currentPage.toString());
    }

    if (selectedCategory) {
      params.set('category', selectedCategory);
    }

    if (selectedSubcategory) {
      params.set('subcategory', selectedSubcategory);
    }

    if (selectedSort !== 'createdAt:desc') {
      params.set('sort', selectedSort);
    }

    const newUrl = `/shop${params.toString() ? `?${params.toString()}` : ''}`;
    router.push(newUrl, { scroll: false });
  }, [currentPage, selectedCategory, selectedSubcategory, selectedSort, router]);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        // Use hierarchical endpoint to get proper parent-child structure
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/api/categories/public/hierarchical?platform=WEB`,
          {
            headers: {
              'X-Platform': 'WEB'
            }
          }
        );

        if (response.ok) {
          const data = await response.json();
          if (data.success && Array.isArray(data.data)) {
            // Extract only parent categories (those returned by hierarchical endpoint are already parents)
            setCategories(data.data);
          } else {
            console.error('Invalid categories data format');
            setCategories([]);
          }
        } else {
          throw new Error('Failed to fetch categories');
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
        setCategories([]);
      }
    };

    fetchCategories();
  }, []);

  // Simple memoization for product fetching
  const memoizedProductFetch = (() => {
    const cache: Record<string, { data: any, timestamp: number }> = {};

    return async (page: number, limit: number, category?: string, sort?: SortOption, subcategory?: string) => {
      const cacheKey = `${page}-${limit}-${category || ''}-${sort || ''}-${subcategory || ''}`;
      const now = Date.now();
      const cacheTime = 5 * 60 * 1000; // 5 minutes

      // Return cached result if valid
      if (cache[cacheKey] && now - cache[cacheKey].timestamp < cacheTime) {
        return cache[cacheKey].data;
      }

      // Fetch new data
      const result = await getProducts(page, limit, category, sort, subcategory);

      // Update cache
      cache[cacheKey] = { data: result, timestamp: now };

      return result;
    };
  })();

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        setError(null);

        // Use the memoized function to fetch products
        const result = await memoizedProductFetch(
          currentPage,
          productsPerPage,
          selectedCategory || undefined,
          selectedSort,
          selectedSubcategory || undefined
        );

        const productsData = result?.data || [];

        // Map API products to match the Product type expected by ProductGridImage
        const mappedProducts = productsData.map((product: any) => ({
          ...product,
          // Add missing properties required by our Product type
          stock: product.stock ?? 0,
          options: product.options ?? [],
          updatedAt: product.updatedAt ?? product.createdAt
        })) as Product[];

        setProducts(mappedProducts);
        setTotalPages(result?.totalPages || 1);
        setTotalProducts(result?.total || 0);

        // Store in sessionStorage for faster access on back navigation
        sessionStorage.setItem('shop_products', JSON.stringify({
          products: mappedProducts,
          totalPages: result?.totalPages || 1,
          totalProducts: result?.total || 0,
          timestamp: Date.now(),
          params: { page: currentPage, category: selectedCategory, subcategory: selectedSubcategory, sort: selectedSort }
        }));
      } catch (error) {
        console.error('Error fetching products:', error);
        setError('Failed to load products. Please try again later.');
        setProducts([]);
      } finally {
        setLoading(false);
      }
    };

    // Check if we have cached data in sessionStorage that matches current params
    const cachedData = sessionStorage.getItem('shop_products');
    if (cachedData) {
      try {
        const parsed = JSON.parse(cachedData);
        const params = parsed.params;
        const isParamsMatch =
          params.page === currentPage &&
          params.category === selectedCategory &&
          params.subcategory === selectedSubcategory &&
          params.sort === selectedSort;

        // Use cached data if parameters match and cache is less than 5 minutes old
        const isCacheValid = Date.now() - parsed.timestamp < 5 * 60 * 1000; // 5 minutes

        if (isParamsMatch && isCacheValid) {
          setProducts(parsed.products);
          setTotalPages(parsed.totalPages);
          setTotalProducts(parsed.totalProducts);
          setLoading(false);
          return;
        }
      } catch (e) {
        console.error('Error parsing cached products:', e);
        // Continue with fetching if there's an error with the cache
      }
    }

    fetchProducts();
  }, [currentPage, selectedCategory, selectedSubcategory, selectedSort]);

  const handlePageChange = (newPage: number) => {
    if (newPage < 1 || newPage > totalPages) return;
    setCurrentPage(newPage);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleCategoryChange = (categoryId: string) => {
    const queryString = createQueryString('category', categoryId);
    const newUrl = `/shop${queryString ? `?${queryString}` : ''}`;
    router.push(newUrl, { scroll: false });
    setSelectedCategory(categoryId);
    setCurrentPage(1);
  };

  const handleSortChange = (sort: SortOption) => {
    const queryString = createQueryString('sort', sort);
    const newUrl = `/shop${queryString ? `?${queryString}` : ''}`;
    router.push(newUrl, { scroll: false });
    setSelectedSort(sort);
    setCurrentPage(1);
  };

  if (loading) {
    return (
      <>
        <PageTitle
          title="Shop"
          breadcrumbs={[
            { label: "Home", href: "/" },
            { label: "Shop", href: "/shop" }
          ]}
        />
        <div className="min-h-screen bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {/* Filters skeleton */}
            <div className="mb-8 flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
              <div className="flex flex-wrap gap-4 items-center animate-pulse">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="bg-gray-100 rounded-md h-10 w-24"></div>
                ))}
              </div>
              <div className="bg-gray-100 rounded-md h-10 w-[200px] animate-pulse"></div>
            </div>

            {/* Products skeleton - white background */}
            <div className="animate-pulse">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {[...Array(12)].map((_, i) => (
                  <div key={i} className="rounded-lg overflow-hidden border border-gray-100">
                    <div className="bg-gray-100 h-64 w-full"></div>
                    <div className="p-4">
                      <div className="bg-gray-100 h-6 w-3/4 mb-2 rounded"></div>
                      <div className="bg-gray-100 h-4 w-1/2 rounded"></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }

  if (error) {
    return (
      <>
        <PageTitle
          title="Shop"
          breadcrumbs={[
            { label: "Home", href: "/" },
            { label: "Shop", href: "/shop" }
          ]}
        />
        <div className="min-h-screen bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <ErrorMessage message={error} />
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <PageTitle
        title="Shop"
        description={`${totalProducts} products available`}
        breadcrumbs={[
          { label: "Home", href: "/" },
          { label: "Shop", href: "/shop" }
        ]}
      />
      <div className="min-h-screen bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Filters */}
          <div className="mb-8 space-y-4">
            {/* Category Filters */}
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
              <div className="flex flex-wrap gap-4 items-center">
                <Button
                  variant={selectedCategory === '' ? 'default' : 'outline'}
                  onClick={() => handleCategoryChange('')}
                  className="flex items-center gap-2"
                >
                  All Products
                </Button>
                {categories.map((category) => (
                  <Button
                    key={category.id}
                    variant={selectedCategory === category.id ? 'default' : 'outline'}
                    onClick={() => handleCategoryChange(category.id)}
                    className="flex items-center gap-2"
                  >
                    {category.name}
                  </Button>
                ))}
              </div>
              <Select value={selectedSort} onValueChange={(value) => handleSortChange(value as SortOption)}>
                <SelectTrigger className="w-[200px]">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  {sortOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Subcategory Filters */}
            {selectedCategory && (
              <Suspense fallback={<div className="flex flex-wrap gap-2 items-center animate-pulse">
                <span className="text-sm text-gray-600 mr-2">Loading filters...</span>
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="bg-gray-100 rounded-md h-8 w-20"></div>
                ))}
              </div>}>
                <SubcategoryFilters selectedCategory={selectedCategory} />
              </Suspense>
            )}
          </div>

          {(!products || products.length === 0) ? (
            <div className="text-center py-8">
              <h3 className="text-lg font-medium">No products found</h3>
              <p className="text-gray-600 mt-2">Try adjusting your filters or check back later for new products</p>
            </div>
          ) : (
            <>
              {(() => {
                // Group products by subcategory when a specific category is selected
                if (selectedCategory && categories.length > 0) {
                  
                  // Group products by their subcategory
                  const groupedProducts = products.reduce((groups: { [key: string]: { name: string; products: Product[] } }, product) => {
                    const subcategoryId = product.subcategoryId || 'no-subcategory';
                    const subcategoryName = product.subcategory?.name || 'Other Products';
                    
                    if (!groups[subcategoryId]) {
                      groups[subcategoryId] = {
                        name: subcategoryName,
                        products: []
                      };
                    }
                    groups[subcategoryId].products.push(product);
                    return groups;
                  }, {});

                  // Sort subcategory groups to show tagged products first, "Other Products" last
                  const subcategoryGroups = Object.values(groupedProducts).sort((a, b) => {
                    // "Other Products" should always come last
                    if (a.name === 'Other Products' && b.name !== 'Other Products') return 1;
                    if (b.name === 'Other Products' && a.name !== 'Other Products') return -1;
                    // For other subcategories, sort alphabetically
                    return a.name.localeCompare(b.name);
                  });
                  
                  // If we have subcategories, show grouped display
                  if (subcategoryGroups.length > 1 || (subcategoryGroups.length === 1 && subcategoryGroups[0].name !== 'Other Products')) {
                    return (
                      <div className="space-y-12 mb-8">
                        {subcategoryGroups.map((group, groupIndex) => (
                          <div key={groupIndex} className="space-y-6">
                            {/* Subcategory Header */}
                            <div className="border-b border-gray-200 pb-4">
                              <h2 className="text-3xl font-bold text-gray-900 tracking-tight">
                                {group.name}
                              </h2>
                              <p className="text-gray-600 mt-1">
                                {group.products.length} {group.products.length === 1 ? 'product' : 'products'}
                              </p>
                            </div>
                            
                            {/* Products Grid for this subcategory */}
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                              {group.products.map((product) => {
                                const price = getProductPrice(product);
                                const primaryImage = product.images.find(img => img.isPrimary) || product.images[0];
                                const hasReadyStock = hasReadyStockVariants(product);
                                const readyStockCount = getReadyStockCount(product);

                                return (
                                  <Link
                                    key={product.id}
                                    href={`/shop/${product.slug}`}
                                    className="group"
                                  >
                                    <div className="bg-white rounded-lg shadow-sm overflow-hidden transition-transform duration-200 ease-in-out group-hover:shadow-md">
                                      <div className="relative aspect-square">
                                        <ProductGridImage
                                          product={product}
                                          src={primaryImage?.url || '/placeholder.jpg'}
                                          alt={primaryImage?.alt || product.name}
                                          className="w-full h-full object-cover"
                                        />
                                        {/* Ready Stock Badge */}
                                        {hasReadyStock && (
                                          <div className="absolute top-2 left-2">
                                            <Badge variant="secondary" className="bg-green-100 text-green-800 border-green-200 text-xs">
                                              <Package className="w-3 h-3 mr-1" />
                                              {readyStockCount > 0 ? `${readyStockCount} Ready` : 'Ready'}
                                            </Badge>
                                          </div>
                                        )}
                                      </div>
                                      <div className="p-4">
                                        <h3 className="text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors">
                                          {product.name}
                                        </h3>
                                        <p className="mt-2 text-gray-600">
                                          {product.category?.name === 'Sets'
                                            ? `Starting from 332 AED`
                                            : price.max
                                              ? `${formatPrice(price.min)} - ${formatPrice(price.max)}`
                                              : formatPrice(price.min)
                                          }
                                        </p>
                                      </div>
                                    </div>
                                  </Link>
                                );
                              })}
                            </div>
                          </div>
                        ))}
                      </div>
                    );
                  }
                }
                
                // Fallback to regular grid display if no category selected or no subcategories
                return (
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
                    {products.map((product) => {
                      const price = getProductPrice(product);
                      const primaryImage = product.images.find(img => img.isPrimary) || product.images[0];
                      const hasReadyStock = hasReadyStockVariants(product);
                      const readyStockCount = getReadyStockCount(product);

                      return (
                        <Link
                          key={product.id}
                          href={`/shop/${product.slug}`}
                          className="group"
                        >
                          <div className="bg-white rounded-lg shadow-sm overflow-hidden transition-transform duration-200 ease-in-out group-hover:shadow-md">
                            <div className="relative aspect-square">
                              <ProductGridImage
                                product={product}
                                src={primaryImage?.url || '/placeholder.jpg'}
                                alt={primaryImage?.alt || product.name}
                                className="w-full h-full object-cover"
                              />
                              {/* Ready Stock Badge */}
                              {hasReadyStock && (
                                <div className="absolute top-2 left-2">
                                  <Badge variant="secondary" className="bg-green-100 text-green-800 border-green-200 text-xs">
                                    <Package className="w-3 h-3 mr-1" />
                                    {readyStockCount > 0 ? `${readyStockCount} Ready` : 'Ready'}
                                  </Badge>
                                </div>
                              )}
                            </div>
                            <div className="p-4">
                              <h3 className="text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors">
                                {product.name}
                              </h3>
                              <p className="mt-2 text-gray-600">
                                {product.category?.name === 'Sets'
                                  ? `Starting from 332 AED`
                                  : price.max
                                    ? `${formatPrice(price.min)} - ${formatPrice(price.max)}`
                                    : formatPrice(price.min)
                                }
                              </p>
                            </div>
                          </div>
                        </Link>
                      );
                    })}
                  </div>
                );
              })()}

              {/* Pagination */}
              <div className="flex justify-between items-center mt-8 border-t pt-6">
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-200 transition-colors duration-200"
                >
                  ← Previous
                </button>

                <div className="flex items-center space-x-2">
                  {/* First page */}
                  {currentPage > 3 && (
                    <button
                      onClick={() => handlePageChange(1)}
                      className={`w-10 h-10 rounded-md flex items-center justify-center ${
                        currentPage === 1
                          ? 'bg-black text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      } transition-colors duration-200`}
                    >
                      1
                    </button>
                  )}

                  {/* Ellipsis for many pages */}
                  {currentPage > 4 && (
                    <span className="text-gray-500">...</span>
                  )}

                  {/* Page numbers */}
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;

                    // Calculate which page numbers to show
                    if (currentPage <= 3) {
                      // If we're near the start, show first 5 pages
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      // If we're near the end, show last 5 pages
                      pageNum = totalPages - 4 + i;
                    } else {
                      // Otherwise show 2 before and 2 after current page
                      pageNum = currentPage - 2 + i;
                    }

                    // Only render if the page number is valid
                    if (pageNum > 0 && pageNum <= totalPages) {
                      return (
                        <button
                          key={pageNum}
                          onClick={() => handlePageChange(pageNum)}
                          className={`w-10 h-10 rounded-md flex items-center justify-center ${
                            currentPage === pageNum
                              ? 'bg-black text-white'
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                          } transition-colors duration-200`}
                        >
                          {pageNum}
                        </button>
                      );
                    }
                    return null;
                  }).filter(Boolean)}

                  {/* Ellipsis for many pages */}
                  {currentPage < totalPages - 3 && (
                    <span className="text-gray-500">...</span>
                  )}

                  {/* Last page */}
                  {totalPages > 3 && currentPage < totalPages - 2 && (
                    <button
                      onClick={() => handlePageChange(totalPages)}
                      className={`w-10 h-10 rounded-md flex items-center justify-center ${
                        currentPage === totalPages
                          ? 'bg-black text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      } transition-colors duration-200`}
                    >
                      {totalPages}
                    </button>
                  )}
                </div>

                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-200 transition-colors duration-200"
                >
                  Next →
                </button>
              </div>
            </>
          )}
        </div>
      </div>
    </>
  );
}

// Main exported component with Suspense boundary
export default function ShopPage() {
  return (
    <Suspense fallback={<ShopPageFallback />}>
      <ShopPageContent />
    </Suspense>
  );
}