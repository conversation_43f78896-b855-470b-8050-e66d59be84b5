import { NextResponse } from 'next/server';
import slugify from 'slugify';

function generateSlug(name: string): string {
  return slugify(name, { lower: true, strict: true });
}

interface Product {
  id: string;
  name: string;
  description?: string;
  slug: string;
  images?: { url: string }[];
  inStock: boolean;
  price: number;
  category?: string;
  type?: 'cake' | 'flowers' | 'gift' | 'other';
}

// Google Product Category Taxonomy IDs
// Full list: https://www.google.com/basepages/producttype/taxonomy-with-ids.en-US.txt
const GOOGLE_PRODUCT_CATEGORIES = {
  // Cakes & Desserts
  cake: '976759', // Food, Beverages & Tobacco > Food Items > Baked Goods > Cakes & Dessert Bars > Cakes
  cupcakes: '976760', // Food, Beverages & Tobacco > Food Items > Baked Goods > Cakes & Dessert Bars > Cupcakes
  // Flowers & Plants
  flowers: '1279', // Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing > Kimono Outerwear
  flower_arrangements: '1279', // Using same as flowers for now
  // Gift Baskets
  gift_baskets: '1107', // Arts & Entertainment > Party & Celebration > Party Supplies > Gift Giving > Gift Baskets
  // Default fallback
  other: '166'
} as const;

// Map your product categories to Google's product categories
function getGoogleProductCategory(categoryName?: string, productType?: string): string {
  if (!categoryName && !productType) return GOOGLE_PRODUCT_CATEGORIES.other;
  
  const lowerCategory = categoryName?.toLowerCase() || '';
  
  // Map based on product type first
  if (productType) {
    if (productType in GOOGLE_PRODUCT_CATEGORIES) {
      return GOOGLE_PRODUCT_CATEGORIES[productType as keyof typeof GOOGLE_PRODUCT_CATEGORIES];
    }
  }
  
  // Then try to map based on category name
  if (lowerCategory.includes('cake') || lowerCategory.includes('cupcake')) {
    return GOOGLE_PRODUCT_CATEGORIES.cake;
  }
  if (lowerCategory.includes('flower') || lowerCategory.includes('bouquet')) {
    return GOOGLE_PRODUCT_CATEGORIES.flowers;
  }
  if (lowerCategory.includes('gift') || lowerCategory.includes('basket')) {
    return GOOGLE_PRODUCT_CATEGORIES.gift_baskets;
  }
  
  return GOOGLE_PRODUCT_CATEGORIES.other;
}

async function getProducts(): Promise<Product[]> {
  try {
    // In production, use the Render API URL
    const apiUrl = process.env.NODE_ENV === 'production' 
      ? 'https://pinewraps-api.onrender.com' 
      : process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

    if (!apiUrl) {
      console.error('API URL is not configured');
      return [];
    }

    // First fetch to get total pages
    const url = `${apiUrl}/api/products/public?limit=100`;
    console.log('Fetching products from:', url);

    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
      },
      next: { revalidate: 3600 } // Revalidate every hour
    });
    
    if (!response.ok) {
      console.error('API response not OK:', response.status, response.statusText);
      const errorText = await response.text();
      console.error('Response body:', errorText);
      return [];
    }

    const data = await response.json();
    console.log('API Response:', JSON.stringify(data, null, 2));
    
    // Handle different response formats
    let products = [];
    let totalPages = 1;
    
    if (data.data && Array.isArray(data.data.products) && data.data.pagination) {
      // Standard response format
      products = data.data.products;
      totalPages = data.data.pagination.pages || 1;
    } else if (Array.isArray(data)) {
      // Direct array response
      products = data;
    } else if (data.products && Array.isArray(data.products)) {
      // Alternative response format
      products = data.products;
      totalPages = data.pagination?.pages || 1;
    } else {
      console.error('Unexpected API response format:', data);
      return [];
    }
    
    // Fetch all remaining pages if pagination exists
    if (totalPages > 1) {
      const pagePromises = [];
      for (let page = 2; page <= totalPages; page++) {
        const pageUrl = `${apiUrl}/api/products/public?page=${page}&limit=100`;
        pagePromises.push(
          fetch(pageUrl).then(async (res) => {
            if (!res.ok) return [];
            const pageData = await res.json();
            return pageData.data?.products || pageData.products || [];
          }).catch(err => {
            console.error(`Error fetching page ${page}:`, err);
            return [];
          })
        );
      }
      
      const pagesData = await Promise.all(pagePromises);
      pagesData.forEach(pageProducts => {
        if (Array.isArray(pageProducts)) {
          products.push(...pageProducts);
        }
      });
    }

    console.log(`Found ${products.length} total products`);
    
    // Map API response to merchant feed format, filtering out products without prices
    return products
      .filter((product: any) => {
        if (!product) return false;
        
        // Only include products with a valid price
        const basePrice = product.basePrice ?? product.price;
        const hasPrice = basePrice !== null && 
                        basePrice !== undefined && 
                        !isNaN(Number(basePrice));
                        
        if (!hasPrice) {
          console.warn(`Product ${product.id || 'unknown'} (${product.name || 'unnamed'}) skipped: missing or invalid price`);
          return false;
        }

        // Ensure required fields are present
        if (!product.id || !product.name) {
          console.warn(`Product ${product.id || 'unknown'} skipped: missing required fields`);
          return false;
        }

        return true;
      })
      .map((product: any) => {
        const basePrice = product.basePrice ?? product.price;
        const inStock = product.inStock !== false && 
                       (product.stockQuantity === undefined || product.stockQuantity > 0);
        
        // Log the category mapping for debugging
        const googleCategory = getGoogleProductCategory(product.category?.name, product.type);
        console.log(`Product: ${product.name}, Category: ${product.category?.name || 'none'}, Type: ${product.type || 'none'}, Mapped to Google Category: ${googleCategory}`);
        
        // Ensure we have a proper description
        const description = product.description || 
          `${product.name} - Premium quality ${product.type || product.category?.name || 'product'} from Pinewraps. ${product.type === 'cake' ? 'Freshly baked and beautifully decorated.' : product.type === 'flowers' ? 'Fresh and elegantly arranged.' : product.type === 'gift' ? 'Thoughtfully curated gift.' : 'Exclusive item from our collection.'}`;
        
        return {
          id: product.id,
          name: product.name,
          description: description,
          slug: product.slug || generateSlug(product.name),
          images: Array.isArray(product.images) ? product.images : [{ url: product.image }].filter(Boolean),
          inStock: inStock,
          price: Number(basePrice),
          category: product.category?.name || product.category,
          type: product.type
        };
      });
  } catch (error) {
    console.error('Error fetching products:', error);
    return [];
  }
}

export async function GET() {
  // Get website URL from environment or use default
  const websiteUrl = process.env.FRONTEND_URL || 'https://pinewraps.com';
  
  try {
    const products = await getProducts();
    
    if (!products || products.length === 0) {
      console.warn('No products found for merchant feed');
      return new NextResponse('<?xml version="1.0" encoding="UTF-8"?>\n<rss xmlns:g="http://base.google.com/ns/1.0" version="2.0">\n<channel>\n<title>Pinewraps Products</title>\n<link>' + websiteUrl + '</link>\n<description>No products available</description>\n</channel>\n</rss>', {
        headers: {
          'Content-Type': 'application/xml',
          'Cache-Control': 'public, max-age=3600'
        }
      });
    }

    // Create XML feed with proper feed settings
    const xmlContent = `<?xml version="1.0" encoding="UTF-8"?>
<rss xmlns:g="http://base.google.com/ns/1.0" version="2.0">
  <channel>
    <title>Pinewraps Products</title>
    <link>${websiteUrl}</link>
    <description>Latest products from Pinewraps</description>
    <g:feed_published_date>${new Date().toISOString()}</g:feed_published_date>
    <g:feed_version>1.0</g:feed_version>
    ${products.map((product: Product) => `
    <item>
      <g:id>${String(product.id).trim()}</g:id>
      <g:title>${escapeXml(product.name)}</g:title>
      <g:description>${escapeXml(product.description || `${product.name} - Premium quality product from Pinewraps`)}</g:description>
      <g:link>${websiteUrl}/shop/${product.slug}</g:link>
      <g:image_link>${product.images?.[0]?.url || ''}</g:image_link>
      <g:availability>${product.inStock !== false ? 'in stock' : 'out of stock'}</g:availability>
      <g:price>${product.price ? `${Number(product.price).toFixed(2)} AED` : '0.00 AED'}</g:price>
      <g:brand>Pinewraps</g:brand>
      <g:condition>new</g:condition>
      <g:identifier_exists>yes</g:identifier_exists>
      <g:item_group_id>${String(product.id).trim()}</g:item_group_id>
      <g:mpn>${String(product.id).replace(/[^a-zA-Z0-9]/g, '')}</g:mpn>
      <g:gtin></g:gtin>
      <g:google_product_category>${getGoogleProductCategory(product.category, product.type)}</g:google_product_category>
      <g:shipping>
        <g:country>AE</g:country>
        <g:service>Standard</g:service>
        <g:price>30 AED</g:price>
      </g:shipping>
      <g:shipping_weight>1 kg</g:shipping_weight>
      <g:shipping_length>30 cm</g:shipping_length>
      <g:shipping_width>30 cm</g:shipping_width>
      <g:shipping_height>30 cm</g:shipping_height>
      <g:shipping_label>Fragile</g:shipping_label>
      <g:shipping_details>
        <g:shipping_service>Standard Delivery</g:shipping_service>
        <g:shipping_price>30 AED</g:shipping_price>
        <g:shipping_region>AE</g:shipping_region>
        <g:shipping_time>
          <g:min_handling_time>1</g:min_handling_time>
          <g:max_handling_time>2</g:max_handling_time>
          <g:min_transit_time>1</g:min_transit_time>
          <g:max_transit_time>3</g:max_transit_time>
        </g:shipping_time>
      </g:shipping_details>
      <g:tax>
        <g:rate>5</g:rate>
        <g:country>AE</g:country>
      </g:tax>
      <g:merchant_return_policy>
        <g:return_policy_category>RETURN_ACCEPTED</g:return_policy_category>
        <g:return_window_days>7</g:return_window_days>
        <g:return_policy_label>Refund Policy</g:return_policy_label>
        <g:return_policy_url>${websiteUrl}/refund-policy</g:return_policy_url>
        <g:refund_option>MONEY_BACK</g:refund_option>
      </g:merchant_return_policy>
    </item>
    `).join('')}
  </channel>
</rss>`;

    console.log(`Generated merchant feed with ${products.length} products`);
    return new NextResponse(xmlContent, {
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600' // Cache for 1 hour
      }
    });
  } catch (error) {
    console.error('Error generating merchant feed:', error);
    return new NextResponse('<?xml version="1.0" encoding="UTF-8"?>\n<rss xmlns:g="http://base.google.com/ns/1.0" version="2.0">\n<channel>\n<title>Pinewraps Products</title>\n<link>' + websiteUrl + '</link>\n<description>Error generating product feed</description>\n</channel>\n</rss>', {
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'no-store'
      },
      status: 500
    });
  }
}

// Helper function to escape XML special characters
function escapeXml(unsafe: string): string {
  return unsafe.replace(/[<>&'"]/g, (c) => {
    switch (c) {
      case '<': return '&lt;';
      case '>': return '&gt;';
      case '&': return '&amp;';
      case '\'': return '&apos;';
      case '"': return '&quot;';
      default: return c;
    }
  });
}
