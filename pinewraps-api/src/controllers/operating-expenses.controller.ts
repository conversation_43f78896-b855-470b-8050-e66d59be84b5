import { Request, Response } from 'express';
import { operatingExpensesService } from '../services/operating-expenses.service';
import { ExpenseStatus } from '@prisma/client';
import {
  CreateExpenseCategoryDto,
  UpdateExpenseCategoryDto,
  CreateOperatingExpenseDto,
  UpdateOperatingExpenseDto,
  ExpenseQueryParams,
} from '../types/operating-expenses.types';

export class OperatingExpensesController {
  // Expense Categories
  async createExpenseCategory(req: Request, res: Response) {
    try {
      const data: CreateExpenseCategoryDto = req.body;
      
      // Validate required fields
      if (!data.name) {
        return res.status(400).json({ error: 'Category name is required' });
      }

      const category = await operatingExpensesService.createExpenseCategory(data);
      res.status(201).json({ success: true, data: category });
    } catch (error: any) {
      console.error('Error creating expense category:', error);
      if (error.code === 'P2002') {
        return res.status(400).json({ error: 'Category name already exists' });
      }
      res.status(500).json({ error: 'Failed to create expense category' });
    }
  }

  async getExpenseCategories(req: Request, res: Response) {
    try {
      const includeStats = req.query.includeStats === 'true';
      const categories = await operatingExpensesService.getExpenseCategories(includeStats);
      res.json({ success: true, data: categories });
    } catch (error) {
      console.error('Error fetching expense categories:', error);
      res.status(500).json({ error: 'Failed to fetch expense categories' });
    }
  }

  async getExpenseCategoryById(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const category = await operatingExpensesService.getExpenseCategoryById(id);
      
      if (!category) {
        return res.status(404).json({ error: 'Expense category not found' });
      }

      res.json({ success: true, data: category });
    } catch (error) {
      console.error('Error fetching expense category:', error);
      res.status(500).json({ error: 'Failed to fetch expense category' });
    }
  }

  async updateExpenseCategory(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const data: UpdateExpenseCategoryDto = req.body;

      const category = await operatingExpensesService.updateExpenseCategory(id, data);
      res.json({ success: true, data: category });
    } catch (error: any) {
      console.error('Error updating expense category:', error);
      if (error.code === 'P2002') {
        return res.status(400).json({ error: 'Category name already exists' });
      }
      if (error.code === 'P2025') {
        return res.status(404).json({ error: 'Expense category not found' });
      }
      res.status(500).json({ error: 'Failed to update expense category' });
    }
  }

  async deleteExpenseCategory(req: Request, res: Response) {
    try {
      const { id } = req.params;
      await operatingExpensesService.deleteExpenseCategory(id);
      res.json({ success: true, message: 'Expense category deleted successfully' });
    } catch (error: any) {
      console.error('Error deleting expense category:', error);
      if (error.code === 'P2025') {
        return res.status(404).json({ error: 'Expense category not found' });
      }
      res.status(500).json({ error: 'Failed to delete expense category' });
    }
  }

  // Operating Expenses
  async createOperatingExpense(req: Request, res: Response) {
    try {
      const data: CreateOperatingExpenseDto = req.body;
      const createdById = req.user?.uid || req.user?.id;

      if (!createdById) {
        return res.status(401).json({ error: 'User authentication required' });
      }

      // Validate required fields
      if (!data.invoiceNumber || !data.companyId || !data.amount || !data.invoiceDate || !data.categoryId) {
        return res.status(400).json({
          error: 'Invoice number, company ID, amount, invoice date, and category are required'
        });
      }

      const expense = await operatingExpensesService.createOperatingExpense(data, createdById);
      res.status(201).json({ success: true, data: expense });
    } catch (error: any) {
      console.error('Error creating operating expense:', error);
      if (error.code === 'P2002') {
        return res.status(400).json({ error: 'Invoice number already exists' });
      }
      if (error.code === 'P2003') {
        return res.status(400).json({ error: 'Invalid category selected' });
      }
      res.status(500).json({ error: 'Failed to create operating expense' });
    }
  }

  async getOperatingExpenses(req: Request, res: Response) {
    try {
      const params: ExpenseQueryParams = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 20,
        sortBy: req.query.sortBy as any || 'createdAt',
        sortOrder: req.query.sortOrder as any || 'desc',
        search: req.query.search as string,
        status: req.query.status as ExpenseStatus,
        categoryId: req.query.categoryId as string,
        companyId: req.query.companyId as string,
        startDate: req.query.startDate as string,
        endDate: req.query.endDate as string,
        minAmount: req.query.minAmount ? parseFloat(req.query.minAmount as string) : undefined,
        maxAmount: req.query.maxAmount ? parseFloat(req.query.maxAmount as string) : undefined,
      };

      const result = await operatingExpensesService.getOperatingExpenses(params);
      res.json({ success: true, data: result });
    } catch (error) {
      console.error('Error fetching operating expenses:', error);
      res.status(500).json({ error: 'Failed to fetch operating expenses' });
    }
  }

  async getOperatingExpenseById(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const expense = await operatingExpensesService.getOperatingExpenseById(id);
      
      if (!expense) {
        return res.status(404).json({ error: 'Operating expense not found' });
      }

      res.json({ success: true, data: expense });
    } catch (error) {
      console.error('Error fetching operating expense:', error);
      res.status(500).json({ error: 'Failed to fetch operating expense' });
    }
  }

  async updateOperatingExpense(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const data: UpdateOperatingExpenseDto = req.body;

      const expense = await operatingExpensesService.updateOperatingExpense(id, data);
      res.json({ success: true, data: expense });
    } catch (error: any) {
      console.error('Error updating operating expense:', error);
      if (error.code === 'P2002') {
        return res.status(400).json({ error: 'Invoice number already exists' });
      }
      if (error.code === 'P2025') {
        return res.status(404).json({ error: 'Operating expense not found' });
      }
      if (error.code === 'P2003') {
        return res.status(400).json({ error: 'Invalid category selected' });
      }
      res.status(500).json({ error: 'Failed to update operating expense' });
    }
  }

  async deleteOperatingExpense(req: Request, res: Response) {
    try {
      const { id } = req.params;
      await operatingExpensesService.deleteOperatingExpense(id);
      res.json({ success: true, message: 'Operating expense deleted successfully' });
    } catch (error: any) {
      console.error('Error deleting operating expense:', error);
      if (error.code === 'P2025') {
        return res.status(404).json({ error: 'Operating expense not found' });
      }
      res.status(500).json({ error: 'Failed to delete operating expense' });
    }
  }

  async markExpenseAsPaid(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { paidDate, bankReference, paymentMethod } = req.body;

      if (!paidDate) {
        return res.status(400).json({ error: 'Paid date is required' });
      }

      const expense = await operatingExpensesService.markExpenseAsPaid(id, {
        paidDate,
        bankReference,
        paymentMethod,
      });

      res.json({ success: true, data: expense });
    } catch (error: any) {
      console.error('Error marking expense as paid:', error);
      if (error.code === 'P2025') {
        return res.status(404).json({ error: 'Operating expense not found' });
      }
      res.status(500).json({ error: 'Failed to mark expense as paid' });
    }
  }

  async getExpenseStats(req: Request, res: Response) {
    try {
      const stats = await operatingExpensesService.getExpenseStats();
      res.json({ success: true, data: stats });
    } catch (error) {
      console.error('Error fetching expense stats:', error);
      res.status(500).json({ error: 'Failed to fetch expense statistics' });
    }
  }
}

export const operatingExpensesController = new OperatingExpensesController();
