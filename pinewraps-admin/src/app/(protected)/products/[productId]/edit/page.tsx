'use client';

import ProductForm from "@/components/products/ProductForm";
import { useEffect, useState, use } from "react";
import { Category } from "@/types/category";
import { Product } from "@/types/product";
import { categoriesApi, productsApi } from "@/lib/api";
import { toast } from "@/components/ui/use-toast";
import { Loader2 } from "lucide-react";

interface EditProductPageProps {
  params: Promise<{
    productId: string;
  }>;
}

const EditProductPage: React.FC<EditProductPageProps> = ({ params }) => {
  const { productId } = use(params);
  const [product, setProduct] = useState<Product | null>(null);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Fetch product data
        const productResponse = await productsApi.getById(productId);
        if (productResponse.data?.success) {
          setProduct(productResponse.data.data);
        } else {
          toast({
            title: "Error",
            description: "Failed to fetch product",
            variant: "destructive"
          });
        }

        // Fetch categories
        const categoriesResponse = await categoriesApi.getAll();
        if (categoriesResponse.data?.success) {
          setCategories(categoriesResponse.data.data);
        } else {
          toast({
            title: "Error",
            description: "Failed to fetch categories",
            variant: "destructive"
          });
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        toast({
          title: "Error",
          description: "Failed to load required data",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [productId]);

  if (loading) {
    return (
      <div className="flex h-full items-center justify-center">
        <Loader2 className="h-6 w-6 animate-spin" />
      </div>
    );
  }

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <ProductForm 
        initialData={product} 
        categories={categories}
        productId={productId}
      />
    </div>
  );
}

export default EditProductPage;
