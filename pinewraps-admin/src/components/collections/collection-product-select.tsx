'use client';

import { useEffect, useState } from 'react';
import { productsApi } from '@/lib/api';
import { Check, X, Search, Filter, Grid, List, Package, SortAsc, SortDesc } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';

interface Product {
  id: string;
  name: string;
  sku: string;
  basePrice: number;
  images: { url: string }[];
  category?: { name: string };
}

interface CollectionProductSelectProps {
  value: string[];
  onChange: (value: string[]) => void;
  disabled?: boolean;
}

export function CollectionProductSelect({
  value = [],
  onChange,
  disabled
}: CollectionProductSelectProps) {

  const [allProducts, setAllProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'name' | 'price' | 'category'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [priceRange, setPriceRange] = useState<'all' | 'low' | 'medium' | 'high'>('all');

  useEffect(() => {
    loadProducts();
  }, []);

  const loadProducts = async () => {
    try {
      setLoading(true);
      console.log('Fetching products...');
      const response = await productsApi.getPublic({
        limit: 1000 // Set high limit to get all products
      });
      console.log('API params:', { limit: 1000 });
      console.log('Products response:', response.data);
      
      // Extract products from the nested response
      const responseData = response.data.data || {};
      const productList = responseData.products || [];
      console.log('Processed products:', productList);
      
      setAllProducts(productList);
    } catch (error) {
      console.error('Error loading products:', error);
      setAllProducts([]);
    } finally {
      setLoading(false);
    }
  };

  // Get unique categories for filter
  const categories = Array.from(new Set(allProducts.map(p => p.category?.name).filter(Boolean)));

  // Enhanced filtering and sorting
  const filteredAndSortedProducts = allProducts
    .filter(product => {
      // Search filter
      if (searchQuery) {
        const searchLower = searchQuery.toLowerCase();
        const matchesSearch =
          product.name.toLowerCase().includes(searchLower) ||
          product.sku.toLowerCase().includes(searchLower) ||
          product.category?.name.toLowerCase().includes(searchLower);
        if (!matchesSearch) return false;
      }

      // Category filter
      if (categoryFilter !== 'all' && product.category?.name !== categoryFilter) {
        return false;
      }

      // Price range filter
      if (priceRange !== 'all') {
        const price = product.basePrice;
        switch (priceRange) {
          case 'low': return price < 50;
          case 'medium': return price >= 50 && price < 200;
          case 'high': return price >= 200;
          default: return true;
        }
      }

      return true;
    })
    .sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'price':
          comparison = a.basePrice - b.basePrice;
          break;
        case 'category':
          comparison = (a.category?.name || '').localeCompare(b.category?.name || '');
          break;
        default:
          comparison = 0;
      }

      return sortOrder === 'asc' ? comparison : -comparison;
    });

  const toggleProduct = (productId: string) => {
    const currentValue = value || [];
    const newValue = currentValue.includes(productId)
      ? currentValue.filter(id => id !== productId)
      : [...currentValue, productId];
    onChange(newValue);
  };

  const selectedProducts = allProducts.filter(product => value.includes(product.id));

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Package className="h-5 w-5" />
          Product Selection
          {selectedProducts.length > 0 && (
            <Badge variant="secondary" className="ml-2">
              {selectedProducts.length} selected
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Selected Products Summary */}
        {selectedProducts.length > 0 && (
          <Card className="bg-muted/30">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium">
                  Selected Products ({selectedProducts.length})
                </CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    onChange([]);
                  }}
                  disabled={disabled}
                  className="h-8 px-2"
                >
                  <X className="h-4 w-4" />
                  Clear All
                </Button>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <ScrollArea className="h-32">
                <div className="grid grid-cols-1 gap-2">
                  {selectedProducts.map((product) => (
                    <div
                      key={product.id}
                      className="flex items-center justify-between bg-background p-3 rounded-md border"
                    >
                      <div className="flex items-center space-x-3">
                        {product.images?.[0] && (
                          <img
                            src={product.images[0].url}
                            alt={product.name}
                            className="w-8 h-8 object-cover rounded"
                          />
                        )}
                        <div>
                          <p className="font-medium text-sm">{product.name}</p>
                          <p className="text-xs text-muted-foreground">
                            {product.category?.name} · AED {product.basePrice}
                          </p>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          toggleProduct(product.id);
                        }}
                        disabled={disabled}
                        className="h-8 w-8 p-0"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
              ))}
            </div>
          </ScrollArea>
            </CardContent>
          </Card>
        )}

        {/* Enhanced Search and Filters */}
        <div className="space-y-4">
          {/* Search Bar */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search products by name, SKU, or category..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
              disabled={disabled}
            />
          </div>

          {/* Filters and View Controls */}
          <div className="flex flex-wrap items-center gap-4 p-4 bg-muted/30 rounded-lg">
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Filters:</span>
            </div>

            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={priceRange} onValueChange={(value) => setPriceRange(value as 'all' | 'low' | 'medium' | 'high')}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Price" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Prices</SelectItem>
                <SelectItem value="low">Under AED 50</SelectItem>
                <SelectItem value="medium">AED 50-200</SelectItem>
                <SelectItem value="high">Over AED 200</SelectItem>
              </SelectContent>
            </Select>

            <Separator orientation="vertical" className="h-6" />

            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">Sort:</span>
              <Select value={sortBy} onValueChange={(value) => setSortBy(value as 'name' | 'price' | 'category')}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="price">Price</SelectItem>
                  <SelectItem value="category">Category</SelectItem>
                </SelectContent>
              </Select>

              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
                }}
                className="p-2"
              >
                {sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
              </Button>
            </div>

            <Separator orientation="vertical" className="h-6" />

            <div className="flex items-center gap-1">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setViewMode('grid');
                }}
                className="p-2"
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setViewMode('list');
                }}
                className="p-2"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Products Display */}
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-base">
                  {loading ? 'Loading...' : `${filteredAndSortedProducts.length} Products`}
                </CardTitle>
                {!loading && searchQuery && (
                  <Badge variant="outline">
                    Filtered from {allProducts.length}
                  </Badge>
                )}
              </div>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[400px]">
                {loading ? (
                  <div className="flex items-center justify-center h-32">
                    <div className="text-center">
                      <Package className="h-8 w-8 mx-auto mb-2 text-muted-foreground animate-pulse" />
                      <p className="text-muted-foreground">Loading products...</p>
                    </div>
                  </div>
                ) : filteredAndSortedProducts.length === 0 ? (
                  <div className="flex items-center justify-center h-32">
                    <div className="text-center">
                      <Package className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                      <p className="text-muted-foreground">
                        {allProducts.length === 0 ? 'No products available' : 'No matching products found'}
                      </p>
                      {searchQuery && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            setSearchQuery('');
                          }}
                          className="mt-2"
                        >
                          Clear search
                        </Button>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className={cn(
                    "gap-4",
                    viewMode === 'grid'
                      ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"
                      : "space-y-2"
                  )}>
                    {filteredAndSortedProducts.map((product) => (
                      <div
                        key={product.id}
                        className={cn(
                          "border rounded-lg p-3 cursor-pointer transition-all duration-200",
                          "hover:shadow-md hover:border-primary/30",
                          value.includes(product.id)
                            ? "bg-primary/5 border-primary shadow-sm"
                            : "bg-background hover:bg-muted/30",
                          viewMode === 'list' && "flex items-center space-x-4"
                        )}
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          toggleProduct(product.id);
                        }}
                      >
                        <div className={cn(
                          "relative",
                          viewMode === 'grid' ? "mb-3" : "flex-shrink-0"
                        )}>
                          {product.images?.[0] ? (
                            <img
                              src={product.images[0].url}
                              alt={product.name}
                              className={cn(
                                "object-cover rounded",
                                viewMode === 'grid'
                                  ? "w-full h-32"
                                  : "w-16 h-16"
                              )}
                            />
                          ) : (
                            <div className={cn(
                              "bg-muted flex items-center justify-center rounded",
                              viewMode === 'grid'
                                ? "w-full h-32"
                                : "w-16 h-16"
                            )}>
                              <Package className={cn(
                                "text-muted-foreground",
                                viewMode === 'grid' ? "h-8 w-8" : "h-6 w-6"
                              )} />
                            </div>
                          )}

                          {/* Selection indicator */}
                          <div className={cn(
                            "absolute top-2 right-2 rounded-full p-1 transition-all",
                            value.includes(product.id)
                              ? "bg-primary text-primary-foreground"
                              : "bg-background/80 text-muted-foreground opacity-0 group-hover:opacity-100"
                          )}>
                            <Check className="h-3 w-3" />
                          </div>
                        </div>

                        <div className={cn(
                          "space-y-1",
                          viewMode === 'list' && "flex-1"
                        )}>
                          <h4 className="font-medium text-sm leading-tight">
                            {product.name}
                          </h4>
                          <p className="text-xs text-muted-foreground">
                            SKU: {product.sku}
                          </p>
                          <div className="flex items-center justify-between">
                            <Badge variant="outline" className="text-xs">
                              {product.category?.name || 'Uncategorized'}
                            </Badge>
                            <span className="font-semibold text-sm">
                              AED {product.basePrice}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </CardContent>
          </Card>
        </div>
      </CardContent>
    </Card>
  );
}
