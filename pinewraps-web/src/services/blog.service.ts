import type { BlogPost } from '../types/blog';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

export type ApiResponse<T> = {
  success: boolean;
  data: T;
  meta?: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
};

export const BlogService = {
  async getAllPosts(page = 1, limit = 12): Promise<ApiResponse<BlogPost[]>> {
    try {
      const response = await fetch(
        `${API_URL}/api/blog/posts?page=${page}&limit=${limit}&status=PUBLISHED`
      );
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      if (!data.success || !Array.isArray(data.data)) {
        throw new Error('Invalid response format');
      }
      return {
        success: data.success,
        data: data.data,
        meta: data.pagination || undefined
      };
    } catch (error) {
      console.error('Error fetching blog posts:', error);
      throw error;
    }
  },

  async getPostBySlug(slug: string): Promise<ApiResponse<BlogPost>> {
    try {
      const response = await fetch(`${API_URL}/api/blog/posts/${slug}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      if (!data.success || !data.data) {
        throw new Error('Invalid response format');
      }
      
      // Check if the post is published
      if (data.data.status !== 'PUBLISHED') {
        throw new Error('Post not found or not published');
      }
      
      return data;
    } catch (error) {
      console.error('Error fetching blog post:', error);
      throw error;
    }
  },

  async getPostsByCategory(categorySlug: string, page = 1, limit = 12): Promise<ApiResponse<BlogPost[]>> {
    try {
      const response = await fetch(
        `${API_URL}/api/blog/categories/${categorySlug}/posts?page=${page}&limit=${limit}`
      );
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      if (!data.success || !Array.isArray(data.data)) {
        throw new Error('Invalid response format');
      }
      return {
        success: data.success,
        data: data.data,
        meta: data.pagination || undefined
      };
    } catch (error) {
      console.error('Error fetching posts by category:', error);
      throw error;
    }
  },

  async getAllCategories(): Promise<ApiResponse<{ id: string; name: string; slug: string }[]>> {
    try {
      const response = await fetch(`${API_URL}/api/blog/categories`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      if (!data.success || !Array.isArray(data.data)) {
        throw new Error('Invalid response format');
      }
      return {
        success: data.success,
        data: data.data,
        meta: data.pagination || undefined
      };
    } catch (error) {
      console.error('Error fetching blog categories:', error);
      throw error;
    }
  }
};
