'use client';

import { Suspense } from 'react';
import { getFirebaseToken } from '@/lib/firebase';
import api from '@/lib/api';
import { UserForm } from '@/components/users/UserForm';
import { Loader2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { use } from 'react';

interface PageProps {
  params: Promise<{
    id: string;
  }>;
}

function UserFormWrapper({ id }: { id: string }) {
  const [user, setUser] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchUser() {
      try {
        const token = await getFirebaseToken();
        const response = await api.get(`/api/users/${id}`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });
        setUser(response.data.data);
      } catch (error) {
        console.error('Error fetching user:', error);
        setError('Failed to fetch user');
      }
    }

    fetchUser();
  }, [id]);

  if (error) return <div className="text-red-500">{error}</div>;
  if (!user) return (
    <div className="flex items-center justify-center h-32">
      <Loader2 className="h-8 w-8 animate-spin" />
    </div>
  );

  return <UserForm initialData={user} />;
}

export default function EditUserPage({ params }: PageProps) {
  const { id } = use(params);
  
  return (
    <div className="flex-col">
      <div className="flex-1 space-y-4 p-8 pt-6">
        <UserFormWrapper id={id} />
      </div>
    </div>
  );
}
