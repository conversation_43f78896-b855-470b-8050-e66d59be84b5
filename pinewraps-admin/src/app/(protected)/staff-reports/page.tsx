'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Users,
  Clock,
  TrendingUp,
  Award,
  AlertTriangle,
  Download,
  RefreshCw,
  Calendar,
  Filter,
  BarChart3
} from 'lucide-react';
import { toast } from 'sonner';
import { format, subDays } from 'date-fns';
import { DateRangePicker } from '@/components/ui/date-range-picker';
import { DateRange } from 'react-day-picker';

import staffReportsService, { 
  StaffDashboardData, 
  StaffPerformanceMetrics,
  DepartmentMetrics,
  QualityMetrics 
} from '@/services/staff-reports.service';

import StaffPerformanceTable from '@/components/staff-reports/staff-performance-table';
import DepartmentMetricsChart from '@/components/staff-reports/department-metrics-chart';
import QualityControlPanel from '@/components/staff-reports/quality-control-panel';

const timeRanges = [
  { value: '7d', label: 'Last 7 days' },
  { value: '14d', label: 'Last 14 days' },
  { value: '30d', label: 'Last 30 days' },
  { value: '3m', label: 'Last 3 months' },
  { value: 'all', label: 'All time' },
  { value: 'custom', label: 'Custom Range' },
];

const departments = [
  { value: 'all', label: 'All Departments' },
  { value: 'kitchen', label: 'Kitchen' },
  { value: 'design', label: 'Design' },
  { value: 'finalCheck', label: 'Final Check' },
  { value: 'cashier', label: 'Cashier/POS' },
];

export default function StaffReportsPage() {
  const router = useRouter();
  const [dashboardData, setDashboardData] = useState<StaffDashboardData | null>(null);
  const [allStaffPerformance, setAllStaffPerformance] = useState<StaffPerformanceMetrics[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [timeRange, setTimeRange] = useState('30d');
  const [selectedDepartment, setSelectedDepartment] = useState('all');
  const [dateRange, setDateRange] = useState<DateRange | undefined>();

  // Optimized dashboard data fetching
  const fetchDashboardData = async () => {
    try {
      setIsRefreshing(true);
      console.log('Fetching dashboard data with optimized approach...');

      const params: any = { timeRange };

      // Add department filter if not 'all'
      if (selectedDepartment !== 'all') {
        params.department = selectedDepartment;
      }

      if (timeRange === 'custom' && dateRange?.from) {
        params.startDate = format(dateRange.from, 'yyyy-MM-dd');
        if (dateRange.to) {
          params.endDate = format(dateRange.to, 'yyyy-MM-dd');
        }
      }

      // Fetch dashboard data and all staff performance separately
      const startTime = performance.now();

      // Get dashboard data (filtered by department for overview metrics)
      const data = await staffReportsService.getStaffDashboard(params);

      // Get ALL staff performance data (unfiltered) for the Staff Performance tab
      const allStaffParams: any = {
        timeRange: timeRange as '7d' | '14d' | '30d' | '3m' | 'all' | 'custom',
        showAllRoles: true // This will show multi-role staff separately for each department
      };
      if (timeRange === 'custom' && dateRange?.from) {
        allStaffParams.startDate = format(dateRange.from, 'yyyy-MM-dd');
        if (dateRange.to) {
          allStaffParams.endDate = format(dateRange.to, 'yyyy-MM-dd');
        }
      }

      const allStaffData = await staffReportsService.getStaffPerformance(allStaffParams);

      const endTime = performance.now();

      console.log(`Dashboard data loaded in ${Math.round(endTime - startTime)}ms`);
      console.log(`Found ${allStaffData.length} total staff performance records (including multi-role)`);

      setDashboardData(data);
      setAllStaffPerformance(allStaffData);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      toast.error('Failed to load staff reports data');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Initial load
  useEffect(() => {
    fetchDashboardData();
  }, [timeRange, dateRange, selectedDepartment]);

  // Reset date range when switching away from custom
  useEffect(() => {
    if (timeRange !== 'custom') {
      setDateRange(undefined);
    }
  }, [timeRange]);

  // For Staff Performance tab, show ALL staff with their roles (including multi-role staff appearing separately)
  // This ensures multi-role staff appear once for each department they work in
  const filteredStaffPerformance = allStaffPerformance;

  // Debug logging
  console.log(`Staff Performance Tab: Showing ${filteredStaffPerformance.length} staff records`);
  if (filteredStaffPerformance.length > 0) {
    const multiRoleStaff = filteredStaffPerformance.filter(s => s.isMultiRole);
    const uniqueStaff = new Set(filteredStaffPerformance.map(s => s.staffId)).size;
    console.log(`- Unique staff members: ${uniqueStaff}`);
    console.log(`- Multi-role records: ${multiRoleStaff.length}`);
    console.log(`- Total records (including separate roles): ${filteredStaffPerformance.length}`);
  }

  return (
    <div className="space-y-6">
      {/* Overview Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-blue-500 text-white">
            <BarChart3 className="h-6 w-6" />
          </div>
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Staff Performance Overview</h2>
            <p className="text-muted-foreground">
              Comprehensive dashboard across all departments
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Time Range Filter */}
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="h-9 w-[140px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {timeRanges.map((range) => (
                <SelectItem key={range.value} value={range.value}>
                  {range.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Department Filter */}
          <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
            <SelectTrigger className="h-9 w-[160px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {departments.map((dept) => (
                <SelectItem key={dept.value} value={dept.value}>
                  {dept.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Date Range Picker (only shown when timeRange is 'custom') */}
          {timeRange === 'custom' && (
            <DateRangePicker
              value={dateRange}
              onChange={(range) => {
                if (range?.from) {
                  setDateRange({
                    from: range.from,
                    to: range.to || range.from
                  });
                } else {
                  setDateRange(undefined);
                }
              }}
              placeholder="Select dates"
              className="w-[220px]"
            />
          )}

          <Button
            variant="outline"
            size="sm"
            onClick={fetchDashboardData}
            disabled={isRefreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          

        </div>
      </div>



      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Staff</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isLoading ? (
                <Skeleton className="h-8 w-16" />
              ) : (
                dashboardData?.summary.totalStaff || 0
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              Active staff members
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Orders Processed</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isLoading ? (
                <Skeleton className="h-8 w-20" />
              ) : (
                staffReportsService.formatNumber(dashboardData?.summary.totalOrdersProcessed || 0)
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              Total orders completed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Productivity</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isLoading ? (
                <Skeleton className="h-8 w-16" />
              ) : (
                `${dashboardData?.summary.averageProductivity.toFixed(1) || 0}%`
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              Overall productivity score
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Quality Score</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isLoading ? (
                <Skeleton className="h-8 w-16" />
              ) : (
                `${dashboardData?.summary.averageQualityScore.toFixed(1) || 0}%`
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              Average quality rating
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="staff">Staff Performance</TabsTrigger>
          <TabsTrigger value="departments">Departments</TabsTrigger>
          <TabsTrigger value="quality">Quality Control</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 lg:grid-cols-2">
            {/* Top Performers */}
            <Card>
              <CardHeader>
                <CardTitle>Top Performers</CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="space-y-3">
                    {[...Array(5)].map((_, i) => (
                      <div key={i} className="flex items-center space-x-3">
                        <Skeleton className="h-8 w-8 rounded-full" />
                        <div className="space-y-1">
                          <Skeleton className="h-4 w-32" />
                          <Skeleton className="h-3 w-20" />
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="space-y-3">
                    {dashboardData?.topPerformers.map((staff, index) => (
                      <div
                        key={staff.staffId}
                        className="flex items-center justify-between p-2 rounded-lg hover:bg-muted/50 cursor-pointer transition-colors"
                        onClick={() => router.push(`/staff-reports/${staff.staffId}`)}
                      >
                        <div className="flex items-center space-x-3">
                          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium">
                            {index + 1}
                          </div>
                          <div>
                            <p className="font-medium hover:text-primary transition-colors">{staff.staffName}</p>
                            <p className="text-sm text-muted-foreground">
                              {staffReportsService.getDepartmentDisplayName(staff.department)}
                            </p>
                          </div>
                        </div>
                        <Badge variant="secondary">
                          {staff.productivityScore.toFixed(1)}%
                        </Badge>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Department Overview */}
            <Card>
              <CardHeader>
                <CardTitle>Department Overview</CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="space-y-3">
                    {[...Array(4)].map((_, i) => (
                      <div key={i} className="flex items-center justify-between">
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-4 w-16" />
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="space-y-3">
                    {dashboardData?.departmentMetrics.map((dept) => (
                      <div key={dept.department} className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <div 
                            className="h-3 w-3 rounded-full"
                            style={{ backgroundColor: staffReportsService.getDepartmentColor(dept.department) }}
                          />
                          <span className="font-medium">
                            {staffReportsService.getDepartmentDisplayName(dept.department)}
                          </span>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">{dept.totalOrders} orders</p>
                          <p className="text-sm text-muted-foreground">
                            {dept.efficiency.toFixed(1)}% efficiency
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="staff">
          <StaffPerformanceTable 
            data={filteredStaffPerformance}
            isLoading={isLoading}
          />
        </TabsContent>

        <TabsContent value="departments">
          <DepartmentMetricsChart 
            data={dashboardData?.departmentMetrics || []}
            isLoading={isLoading}
          />
        </TabsContent>

        <TabsContent value="quality">
          <QualityControlPanel 
            data={dashboardData?.qualityMetrics}
            isLoading={isLoading}
          />
        </TabsContent>


      </Tabs>
    </div>
  );
}
