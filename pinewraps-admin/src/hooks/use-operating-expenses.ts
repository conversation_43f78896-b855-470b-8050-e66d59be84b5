import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "@/components/ui/use-toast";
import { operatingExpenseService, OperatingExpense } from "@/services/operating-expense.service";

// OperatingExpense interface is now imported from the service

export const useOperatingExpenses = () => {
  const queryClient = useQueryClient();

  const { data: expenses, isLoading, error } = useQuery<OperatingExpense[]>({
    queryKey: ["operating-expenses"],
    queryFn: () => operatingExpenseService.getExpenses(),
  });

  const createExpense = useMutation({
    mutationFn: (data: Omit<OperatingExpense, "id" | "createdAt" | "updatedAt" | "paid">) => 
      operatingExpenseService.createExpense(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["operating-expenses"] });
      toast({ title: "Operating expense created successfully" });
    },
    onError: () => {
      toast({ title: "Failed to create operating expense", variant: "destructive" });
    },
  });

  const updateExpense = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<OperatingExpense> }) => 
      operatingExpenseService.updateExpense(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["operating-expenses"] });
      toast({ title: "Operating expense updated successfully" });
    },
    onError: () => {
      toast({ title: "Failed to update operating expense", variant: "destructive" });
    },
  });

  const deleteExpense = useMutation({
    mutationFn: (id: string) => operatingExpenseService.deleteExpense(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["operating-expenses"] });
      toast({ title: "Operating expense deleted successfully" });
    },
    onError: () => {
      toast({ title: "Failed to delete operating expense", variant: "destructive" });
    },
  });

  const markExpenseAsPaid = useMutation({
    mutationFn: (id: string) => operatingExpenseService.markAsPaid(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["operating-expenses"] });
      toast({ title: "Operating expense marked as paid" });
    },
    onError: () => {
      toast({ title: "Failed to mark operating expense as paid", variant: "destructive" });
    },
  });

  return {
    expenses,
    isLoading,
    error,
    createExpense: createExpense.mutateAsync,
    updateExpense: updateExpense.mutateAsync,
    deleteExpense: deleteExpense.mutateAsync,
    markExpenseAsPaid: markExpenseAsPaid.mutateAsync,
  };
};
