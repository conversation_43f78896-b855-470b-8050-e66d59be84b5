'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { SupplierForm } from '@/components/suppliers/SupplierForm';
import toast from 'react-hot-toast';
import { Loader2 } from 'lucide-react';
import api from '@/lib/api';

export default function EditSupplierPage() {
  const params = useParams();
  const [supplier, setSupplier] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchSupplier = async () => {
      try {
        const response = await api.get(`/api/suppliers/${params.supplierId}`);
        setSupplier(response.data.data);
      } catch (error) {
        console.error('Error fetching supplier:', error);
        toast.error('Failed to load supplier');
      } finally {
        setLoading(false);
      }
    };

    if (params.supplierId) {
      fetchSupplier();
    }
  }, [params.supplierId]);

  if (loading) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <Loader2 className="h-6 w-6 animate-spin" />
      </div>
    );
  }

  if (!supplier) {
    return (
      <div className="flex h-full items-center justify-center">
        <p className="text-muted-foreground">Supplier not found</p>
      </div>
    );
  }

  return (
    <div className="flex-col">
      <div className="flex-1 space-y-4 p-8 pt-6">
        <SupplierForm initialData={supplier} />
      </div>
    </div>
  );
}
