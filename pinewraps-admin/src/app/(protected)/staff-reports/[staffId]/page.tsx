'use client';

import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  ArrowLeft,
  User,
  Clock,
  TrendingUp,
  Award,
  Target,
  Calendar,
  Download,
  RefreshCw,
  Mail,
  MapPin,
  Trophy,
  AlertTriangle,
  BarChart3,
  PieChart,
  Activity,
  CheckCircle,
  Star,
  Timer,
  Users,
  ChefHat,
  Palette,
  Calculator
} from 'lucide-react';
import { toast } from 'sonner';
import { format } from 'date-fns';

import staffReportsService, { IndividualStaffDetails } from '@/services/staff-reports.service';

const timeRanges = [
  { value: '7d', label: 'Last 7 days' },
  { value: '14d', label: 'Last 14 days' },
  { value: '30d', label: 'Last 30 days' },
  { value: '3m', label: 'Last 3 months' },
  { value: 'all', label: 'All time' },
];

const departmentIcons = {
  kitchen: ChefHat,
  design: Palette,
  finalCheck: CheckCircle,
  cashier: Calculator,
};

const departmentColors = {
  kitchen: 'bg-red-500',
  design: 'bg-blue-500',
  finalCheck: 'bg-green-500',
  cashier: 'bg-amber-500',
};

export default function IndividualStaffPage() {
  const params = useParams();
  const router = useRouter();
  const staffId = params.staffId as string;

  const [staffDetails, setStaffDetails] = useState<IndividualStaffDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const ordersPerPage = 20;
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [timeRange, setTimeRange] = useState('30d');

  // Fetch staff details
  const fetchStaffDetails = async () => {
    try {
      setIsRefreshing(true);
      
      const params: any = { timeRange };
      const data = await staffReportsService.getIndividualStaffDetails(staffId, params);
      setStaffDetails(data);
    } catch (error) {
      console.error('Error fetching staff details:', error);
      toast.error('Failed to load staff details');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Initial load
  useEffect(() => {
    if (staffId) {
      setCurrentPage(1); // Reset to first page when filters change
      fetchStaffDetails();
    }
  }, [staffId, timeRange]);



  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Skeleton className="h-10 w-10" />
          <div className="space-y-2">
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-4 w-32" />
          </div>
        </div>
        
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!staffDetails) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
        <AlertTriangle className="h-12 w-12 text-muted-foreground" />
        <div className="text-center">
          <h3 className="text-lg font-semibold">Staff Member Not Found</h3>
          <p className="text-muted-foreground">The requested staff member could not be found.</p>
        </div>
        <Button onClick={() => router.back()}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Go Back
        </Button>
      </div>
    );
  }

  const { staffInfo, performanceMetrics, orderHistory = [], performanceComparison } = staffDetails;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{staffInfo.name}</h1>
            <div className="flex items-center gap-4 text-muted-foreground">
              <div className="flex items-center gap-1">
                <MapPin className="h-4 w-4" />
                <span className="capitalize">{staffReportsService.getDepartmentDisplayName(staffInfo.primaryDepartment)}</span>
              </div>
              <div className="flex items-center gap-1">
                <Mail className="h-4 w-4" />
                <span>{staffInfo.email}</span>
              </div>
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <span>Joined {format(new Date(staffInfo.joinDate), 'MMM yyyy')}</span>
              </div>
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {timeRanges.map((range) => (
                <SelectItem key={range.value} value={range.value}>
                  {range.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Button
            variant="outline"
            size="sm"
            onClick={fetchStaffDetails}
            disabled={isRefreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          

        </div>
      </div>

      {/* Performance Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Orders Completed</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{performanceMetrics.ordersCompleted || 0}</div>
            <p className="text-xs text-muted-foreground">
              {performanceMetrics.ordersPerHour?.toFixed(1) || '0.0'} orders/hour
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Processing Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {staffReportsService.formatProcessingTime(performanceMetrics.averageProcessingTime)}
            </div>
            <p className="text-xs text-muted-foreground">
              Per order completion
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Quality Score</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {performanceMetrics.qualityScore?.toFixed(1) || '0.0'}%
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant={performanceMetrics.ordersSentBack > 0 ? "destructive" : "default"}>
                {performanceMetrics.ordersSentBack} sent back
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Department Rank</CardTitle>
            <Trophy className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              #{performanceComparison.departmentRank}
            </div>
            <p className="text-xs text-muted-foreground">
              of {performanceComparison.departmentTotal} • {performanceComparison.percentile}th percentile
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Performance Comparison */}
      <Card>
        <CardHeader>
          <CardTitle>Performance vs Department Average</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="text-center">
              <div className="text-2xl font-bold">
                <span className={performanceComparison.comparedToAverage.productivity >= 0 ? 'text-green-600' : 'text-red-600'}>
                  {performanceComparison.comparedToAverage.productivity >= 0 ? '+' : ''}
                  {performanceComparison.comparedToAverage.productivity}%
                </span>
              </div>
              <p className="text-sm text-muted-foreground">Productivity</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">
                <span className={performanceComparison.comparedToAverage.quality >= 0 ? 'text-green-600' : 'text-red-600'}>
                  {performanceComparison.comparedToAverage.quality >= 0 ? '+' : ''}
                  {performanceComparison.comparedToAverage.quality}%
                </span>
              </div>
              <p className="text-sm text-muted-foreground">Quality</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">
                <span className={performanceComparison.comparedToAverage.speed >= 0 ? 'text-green-600' : 'text-red-600'}>
                  {performanceComparison.comparedToAverage.speed >= 0 ? '+' : ''}
                  {performanceComparison.comparedToAverage.speed}%
                </span>
              </div>
              <p className="text-sm text-muted-foreground">Speed</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Order History */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Order History</CardTitle>
        </CardHeader>
        <CardContent>
          {Array.isArray(orderHistory) && orderHistory.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-3">Order #</th>
                    <th className="text-left p-3">Customer</th>
                    <th className="text-left p-3">Status</th>
                    <th className="text-left p-3">Created</th>
                    <th className="text-left p-3">Processing Time</th>
                    <th className="text-left p-3">Amount</th>
                    <th className="text-left p-3">Items</th>
                    <th className="text-left p-3">Complexity</th>
                    <th className="text-left p-3">Quality</th>
                  </tr>
                </thead>
                <tbody>
                  {orderHistory
                    .slice((currentPage - 1) * ordersPerPage, currentPage * ordersPerPage)
                    .map((order, index) => (
                    <tr key={`${order.orderId}-${order.orderNumber}-${index}`} className="border-b hover:bg-muted/50">
                      <td className="p-3 font-medium">{order.orderNumber}</td>
                      <td className="p-3">{order.customerName}</td>
                      <td className="p-3">
                        <Badge variant="outline">{order.status}</Badge>
                      </td>
                      <td className="p-3">
                        {format(new Date(order.createdAt), 'MMM dd, HH:mm')}
                      </td>
                      <td className="p-3">
                        {order.processingTime ?
                          staffReportsService.formatProcessingTime(order.processingTime) :
                          'N/A'
                        }
                      </td>
                      <td className="p-3">AED {order.totalAmount?.toFixed(2) || '0.00'}</td>
                      <td className="p-3">{order.itemCount || 0}</td>
                      <td className="p-3">
                        <Badge
                          variant={
                            order.complexity === 'high' ? 'destructive' :
                            order.complexity === 'medium' ? 'secondary' : 'default'
                          }
                        >
                          {order.complexity}
                        </Badge>
                      </td>
                      <td className="p-3">
                        {order.wasSentBack ? (
                          <Badge variant="destructive">Sent Back</Badge>
                        ) : (
                          <Badge variant="default">Good</Badge>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {Array.isArray(orderHistory) && orderHistory.length > ordersPerPage && (
                <div className="mt-4 flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    Showing {((currentPage - 1) * ordersPerPage) + 1} to {Math.min(currentPage * ordersPerPage, orderHistory?.length || 0)} of {orderHistory?.length || 0} orders
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                      disabled={currentPage === 1}
                    >
                      Previous
                    </Button>
                    <span className="text-sm">
                      Page {currentPage} of {Math.ceil((orderHistory?.length || 0) / ordersPerPage)}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.min(prev + 1, Math.ceil((orderHistory?.length || 0) / ordersPerPage)))}
                      disabled={currentPage === Math.ceil((orderHistory?.length || 0) / ordersPerPage)}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              No order history available for the selected time period.
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
