import { Metadata } from 'next';
import Link from 'next/link';
import CollectionsGrid from '@/components/collections/collections-grid';

export const metadata: Metadata = {
  title: { absolute: 'Premium Gift Collections | Cakes & Flowers in Dubai' },
  description: 'Discover our exclusive collections of luxury cakes, flower arrangements, and gift sets for birthdays, weddings, and special occasions with same-day delivery in Dubai.',
  keywords: ['gift collections dubai', 'cake collections', 'flower arrangements dubai', 'birthday cakes dubai', 'wedding flowers dubai', 'luxury gifts dubai', 'same day delivery'],
  alternates: {
    // Use the NEXT_PUBLIC_SITE_URL environment variable
    canonical: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3002'}/collections`,
  },
  openGraph: {
    title: 'Premium Gift Collections | Cakes & Flowers in Dubai',
    description: 'Discover our exclusive collections of luxury cakes, flower arrangements, and gift sets for birthdays, weddings, and special occasions with same-day delivery in Dubai.',
    url: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3002'}/collections`,
    type: 'website',
  },
};

export default async function CollectionsPage() {
  return (
    <div>
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-b from-purple-50 to-white">
        {/* Decorative background elements */}
        <div className="absolute inset-0 bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] [background-size:16px_16px] opacity-30" />

        {/* Colorful gradient blobs */}
        <div className="absolute top-0 left-0 w-[800px] h-[800px] bg-gradient-to-br from-primary/20 to-purple-200/30 rounded-full blur-3xl -translate-x-1/2 -translate-y-1/2" />
        <div className="absolute bottom-0 right-0 w-[800px] h-[800px] bg-gradient-to-tl from-pink-200/30 to-primary/20 rounded-full blur-3xl translate-x-1/2 translate-y-1/2" />
        <div className="absolute top-1/2 left-1/2 w-[600px] h-[600px] bg-gradient-to-r from-yellow-200/20 to-primary/10 rounded-full blur-3xl -translate-x-1/2 -translate-y-1/2" />

        {/* Line art icons */}
        <div className="absolute inset-0 overflow-hidden opacity-[0.08]">
          {/* Gift box icon */}
          <svg className="absolute top-20 left-[15%] w-24 h-24 text-primary/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M20 12v10H4V12M22 7H2v5h20V7zM12 22V7M12 7H7.5C6.672 7 6 6.328 6 5.5S6.672 4 7.5 4c1.5 0 4.5 3 4.5 3zM12 7h4.5c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5c-1.5 0-4.5 3-4.5 3z" />
          </svg>
          {/* Ribbon icon */}
          <svg className="absolute top-[40%] right-[10%] w-32 h-32 text-pink-400/30" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M12 8c-2.21 0-4-1.79-4-4s1.79-4 4-4 4 1.79 4 4-1.79 4-4 4zm0 0v16m-8-8l8 8 8-8" />
          </svg>
          {/* Bow icon */}
          <svg className="absolute bottom-32 left-[20%] w-28 h-28 text-purple-400/30" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3M3 12c0-1.66 4-3 9-3s9 1.34 9 3M12 9c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3zm0 0v12" />
          </svg>
          {/* Small decorative elements */}
          <div className="absolute top-[25%] right-[25%] w-16 h-16 border-2 border-primary/30 rounded-full rotate-45" />
          <div className="absolute bottom-[15%] right-[35%] w-12 h-12 border-2 border-pink-400/30 rounded-lg rotate-12" />
          <div className="absolute top-[60%] left-[30%] w-8 h-8 border-2 border-purple-400/30 rotate-45" />
        </div>

        {/* Overlay gradients */}
        <div className="absolute inset-0 bg-gradient-to-b from-white/0 via-white/0 to-white" />
        <div className="absolute inset-0 bg-gradient-to-r from-white/50 via-transparent to-white/50" />
        <div className="container mx-auto px-4">
          <div className="relative py-20 md:py-28 lg:py-32 flex flex-col items-center text-center">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              Premium Cake & Flower Collections in Dubai
            </h1>
            <p className="text-lg md:text-xl text-gray-600 max-w-2xl mx-auto mb-6">
              Discover our exclusive collections of luxury cakes, flower arrangements, and gift sets for birthdays,
              anniversaries, and special occasions with same-day delivery across Dubai.
            </p>
            <p className="text-base text-gray-600 max-w-2xl mx-auto mb-12">
              From elegant <strong>birthday cakes</strong> and <strong>wedding flowers</strong> to custom gift arrangements,
              our collections feature the finest ingredients and freshest blooms for your celebration needs.
            </p>
            <div className="flex items-center gap-4">
              <Link
                href="/shop"
                className="inline-flex items-center px-6 py-3 rounded-full bg-primary text-white hover:bg-primary/90 transition-colors shadow-lg shadow-primary/20 hover:shadow-primary/30"
              >
                Shop All Products
              </Link>
              <Link
                href="#collections"
                className="inline-flex items-center px-6 py-3 rounded-full bg-white/90 backdrop-blur-sm border border-gray-200 text-gray-900 hover:bg-white transition-colors shadow-lg shadow-black/5"
              >
                View Collections
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Collections Section */}
      <div id="collections" className="bg-white py-16 md:py-24">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900">Our Collections</h2>
            <p className="mt-4 text-gray-600">Explore our curated selection of premium gifts</p>
          </div>
          <CollectionsGrid />
        </div>
      </div>
    </div>
  );
}
