'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import PageTitle from '@/components/ui/page-title';
import { Product } from '@/types/product';
import { getProductPrice } from '@/utils/product';
import { formatPrice } from '@/utils/format';
import ProductGridImage from '@/components/shop/product-grid-image';
import ErrorMessage from '@/components/shop/error-message';
import { SubcategoryFilters } from '@/components/filters/subcategory-filters';

// Fallback component for Suspense boundary
function CakesPageFallback() {
  return (
    <>
      <PageTitle
        title="Luxury Cakes Collection"
        description="Handcrafted with love and premium ingredients"
        breadcrumbs={[
          { label: "Home", href: "/" },
          { label: "Shop", href: "/shop" },
          { label: "Cakes", href: "/cakes" }
        ]}
      />
      <div className="min-h-screen bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="bg-gray-200 rounded-lg h-64"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

// Main content component
function CakesPageContent() {
  const searchParams = useSearchParams();

  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [, setTotalProducts] = useState(0);

  // Get subcategory from URL for filtering
  const selectedSubcategory = searchParams.get('subcategory') || '';
  const productsPerPage = 12;

  // Add canonical tag for SEO
  useEffect(() => {
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://pinewraps.com';
    const canonicalUrl = `${baseUrl}/cakes`;

    // Remove existing canonical tag if any
    const existingCanonical = document.querySelector('link[rel="canonical"]');
    if (existingCanonical) {
      existingCanonical.remove();
    }

    // Add new canonical tag
    const canonical = document.createElement('link');
    canonical.rel = 'canonical';
    canonical.href = canonicalUrl;
    document.head.appendChild(canonical);

    // Cleanup function to remove canonical tag when component unmounts
    return () => {
      const canonicalToRemove = document.querySelector('link[rel="canonical"]') as HTMLLinkElement;
      if (canonicalToRemove && canonicalToRemove.href === canonicalUrl) {
        canonicalToRemove.remove();
      }
    };
  }, []);

  // Helper function to update URL with current page
  useEffect(() => {
    const params = new URLSearchParams(searchParams.toString());

    if (currentPage > 1) {
      params.set('page', currentPage.toString());
    } else {
      params.delete('page');
    }

    const newUrl = `/cakes${params.toString() ? `?${params.toString()}` : ''}`;
    window.history.replaceState({}, '', newUrl);
  }, [currentPage, searchParams]);

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        setError(null);

        // First, get the category ID for Cakes
        const categoryRes = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/api/categories/public`
        );
        
        if (!categoryRes.ok) {
          throw new Error('Failed to fetch categories');
        }

        const categoryData = await categoryRes.json();
        const cakesCategory = categoryData.data.find((cat: any) => cat.name === 'Cakes');

        if (!cakesCategory) {
          throw new Error('Cakes category not found');
        }

        // Build the products URL with optional subcategory filter
        let productsUrl = `${process.env.NEXT_PUBLIC_API_URL}/api/products/public?category=${cakesCategory.id}&page=${currentPage}&limit=${productsPerPage}`;

        if (selectedSubcategory) {
          productsUrl += `&subcategory=${selectedSubcategory}`;
        }

        // Then fetch products for this category with pagination
        const productsRes = await fetch(productsUrl);
        
        if (!productsRes.ok) {
          throw new Error('Failed to fetch products');
        }

        const result = await productsRes.json();
        
        if (!result.success) {
          throw new Error(result.data);
        }

        const { products, pagination } = result.data;
        
        // Map API products to our Product type
        const mappedProducts = products.map((product: any) => ({
          ...product,
          // Add missing properties from our Product type
          stock: product.stock ?? 0,
          options: product.options ?? [],
          updatedAt: product.updatedAt ?? product.createdAt,
          // Keep variant and variation data
          variants: product.variants ?? [],
          variations: product.variations ?? [],
          variantCombinations: product.variantCombinations ?? []
        })) as Product[];
        
        setProducts(mappedProducts);
        setTotalPages(pagination.pages);
        setTotalProducts(pagination.total);
      } catch (error) {
        console.error('Error fetching products:', error);
        setError('Failed to load products. Please try again later.');
        setProducts([]);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [currentPage, selectedSubcategory]);

  const handlePageChange = (newPage: number) => {
    if (newPage < 1 || newPage > totalPages) return;
    setCurrentPage(newPage);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // We don't need this anymore as it's handled by the SubcategoryFilters component

  if (loading) {
    return (
      <>
        <PageTitle 
          title="Luxury Cakes Collection" 
          description="Handcrafted with love and premium ingredients"
          breadcrumbs={[
            { label: "Home", href: "/" },
            { label: "Shop", href: "/shop" },
            { label: "Cakes", href: "/cakes" }
          ]}
        />
        <div className="min-h-screen bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="animate-pulse">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {[...Array(8)].map((_, i) => (
                  <div key={i} className="bg-gray-200 rounded-lg h-64"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }

  if (error) {
    return (
      <>
        <PageTitle 
          title="Luxury Cakes Collection" 
          description="Handcrafted with love and premium ingredients"
          breadcrumbs={[
            { label: "Home", href: "/" },
            { label: "Shop", href: "/shop" },
            { label: "Cakes", href: "/cakes" }
          ]}
        />
        <div className="min-h-screen bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <ErrorMessage message={error} />
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <PageTitle 
        title="Luxury Cakes Collection" 
        breadcrumbs={[
          { label: "Home", href: "/" },
          { label: "Shop", href: "/shop" },
          { label: "Cakes", href: "/cakes" }
        ]}
      />

      <div className="min-h-screen bg-white">
        {/* Category Description for SEO */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="prose max-w-none">
            <p className="text-gray-600 text-lg leading-relaxed">
              Welcome to Pinewraps&apos; exclusive cake collection. Each cake in our collection is a masterpiece, 
              handcrafted with premium ingredients and meticulous attention to detail. Whether you&apos;re celebrating 
              a birthday, wedding, or any special occasion, our luxury cakes are designed to make your moment 
              unforgettable. From classic flavors to innovative creations, discover the perfect cake that matches 
              your taste and style.
            </p>
          </div>
        </div>

        {/* Subcategory Filters */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
          <Suspense fallback={<div className="flex flex-wrap gap-2 items-center animate-pulse">
            <span className="text-sm text-gray-600 mr-2">Loading filters...</span>
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-gray-100 rounded-md h-8 w-20"></div>
            ))}
          </div>}>
            <SubcategoryFilters categoryName="Cakes" label="Filter by type:" />
          </Suspense>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {(!products || products.length === 0) ? (
            <div className="text-center py-8">
              <h3 className="text-lg font-medium">No cakes available</h3>
              <p className="text-gray-600 mt-2">Check back later for new products</p>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
                {products.map((product) => {
                  const price = getProductPrice(product);
                  const primaryImage = product.images.find(img => img.isPrimary) || product.images[0];
                  
                  return (
                    <Link
                      key={product.id}
                      href={`/shop/${product.slug}`}
                      className="group"
                    >
                      <div className="bg-white rounded-lg shadow-sm overflow-hidden transition-transform duration-200 ease-in-out group-hover:shadow-md">
                        <div className="relative aspect-square">
                          <ProductGridImage
                            product={product}
                            src={primaryImage?.url || '/placeholder.jpg'}
                            alt={primaryImage?.alt || product.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="p-4">
                          <h3 className="text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors">
                            {product.name}
                          </h3>
                          <p className="mt-2 text-gray-600">
                            {price.max
                              ? `${formatPrice(price.min)} - ${formatPrice(price.max)}`
                              : formatPrice(price.min)}
                          </p>
                        </div>
                      </div>
                    </Link>
                  );
                })}
              </div>

              {/* Pagination */}
              <div className="flex justify-between items-center mt-8 border-t pt-6">
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="px-6 py-3 bg-gray-100 text-gray-700 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-200 transition-colors duration-200"
                >
                  ← Previous Page
                </button>
                
                <span className="text-sm text-gray-600">
                  Page {currentPage} of {totalPages}
                </span>

                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="px-6 py-3 bg-gray-100 text-gray-700 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-200 transition-colors duration-200"
                >
                  Next Page →
                </button>
              </div>
            </>
          )}
        </div>
      </div>
    </>
  );
}

// Main exported component with Suspense boundary
export default function CakesPage() {
  return (
    <Suspense fallback={<CakesPageFallback />}>
      <CakesPageContent />
    </Suspense>
  );
}