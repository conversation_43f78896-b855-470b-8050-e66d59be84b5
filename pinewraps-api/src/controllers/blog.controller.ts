import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { slugify } from '../utils/string-utils';
import { RedirectService } from '../services/redirect.service';

// Initialize Prisma client
const prisma = new PrismaClient();

/**
 * Blog Controller
 * Handles all blog-related operations
 */
export const BlogController = {
  /**
   * Get all blog posts with pagination
   */
  getAllPosts: async (req: Request, res: Response) => {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 12;
      const status = req.query.status as string;
      const skip = (page - 1) * limit;

      // For public blog endpoint, default to PUBLISHED only
      // For admin endpoint, allow filtering by status
      let whereClause: any = {};
      
      if (status && status !== 'ALL') {
        whereClause.status = status;
      } else if (!status || status === 'PUBLISHED') {
        // Default to published posts for public blog
        whereClause.status = 'PUBLISHED';
      }
      // If status is 'ALL', don't add status filter (admin use case)

      // Get total count for pagination
      const totalCount = await prisma.blogPost.count({
        where: whereClause,
      });

      // Get posts with categories
      const posts = await prisma.blogPost.findMany({
        where: whereClause,
        include: {
          categories: {
            include: {
              category: true,
            },
          },
        },
        orderBy: [
          { publishedAt: 'desc' }, // Order by published date first
          { createdAt: 'desc' }, // Fallback to creation date
        ],
        skip,
        take: limit,
      });

      // Transform the data to simplify the categories
      const formattedPosts = posts.map(post => ({
        ...post,
        categories: post.categories.map(pc => pc.category),
      }));

      return res.status(200).json({
        success: true,
        data: formattedPosts,
        pagination: {
          total: totalCount,
          page,
          limit,
          pages: Math.ceil(totalCount / limit),
        },
      });
    } catch (error) {
      console.error('Error fetching blog posts:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch blog posts',
        error: error.message,
      });
    }
  },

  /**
   * Get a single blog post by ID or slug
   */
  getPost: async (req: Request, res: Response) => {
    try {
      const { identifier } = req.params; // Can be ID or slug

      const post = await prisma.blogPost.findFirst({
        where: {
          OR: [
            { id: identifier },
            { slug: identifier },
          ],
        },
        include: {
          categories: {
            include: {
              category: true,
            },
          },
        },
      });

      if (!post) {
        return res.status(404).json({
          success: false,
          message: 'Blog post not found',
        });
      }

      // Increment view count
      await prisma.blogPost.update({
        where: { id: post.id },
        data: { viewCount: { increment: 1 } },
      });

      // Transform the data to simplify the categories
      const formattedPost = {
        ...post,
        categories: post.categories.map(pc => pc.category),
      };

      return res.status(200).json({
        success: true,
        data: formattedPost,
      });
    } catch (error) {
      console.error('Error fetching blog post:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch blog post',
        error: error.message,
      });
    }
  },

  /**
   * Create a new blog post
   */
  createPost: async (req: Request, res: Response) => {
    try {
      const {
        title,
        content,
        excerpt,
        featuredImage,
        metaTitle,
        metaDescription,
        status,
        categoryIds,
      } = req.body;

      // Generate slug from title
      let slug = slugify(title);
      
      // Check if slug already exists
      const existingPost = await prisma.blogPost.findUnique({
        where: { slug },
      });
      
      // If slug exists, append a unique identifier
      if (existingPost) {
        slug = `${slug}-${Date.now().toString().slice(-6)}`;
      }

      // Set publishedAt date if status is PUBLISHED
      const publishedAt = status === 'PUBLISHED' ? new Date() : null;

      // Create the post
      const post = await prisma.blogPost.create({
        data: {
          title,
          slug,
          content,
          excerpt,
          featuredImage,
          metaTitle: metaTitle || title,
          metaDescription: metaDescription || excerpt,
          status: status || 'DRAFT',
          publishedAt,
          categories: {
            create: categoryIds?.map(categoryId => ({
              category: {
                connect: { id: categoryId },
              },
            })) || [],
          },
        },
        include: {
          categories: {
            include: {
              category: true,
            },
          },
        },
      });

      // Transform the data to simplify the categories
      const formattedPost = {
        ...post,
        categories: post.categories.map(pc => pc.category),
      };

      return res.status(201).json({
        success: true,
        message: 'Blog post created successfully',
        data: formattedPost,
      });
    } catch (error) {
      console.error('Error creating blog post:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to create blog post',
        error: error.message,
      });
    }
  },

  /**
   * Update an existing blog post
   */
  updatePost: async (req: Request, res: Response) => {
    try {
      console.log('Received update request with body:', req.body);
      const { id } = req.params;
      const {
        title,
        content,
        excerpt,
        featuredImage,
        metaTitle,
        metaDescription,
        status,
        categoryIds,
      } = req.body;

      // Check if post exists
      const existingPost = await prisma.blogPost.findUnique({
        where: { id },
        include: {
          categories: true,
        },
      });

      if (!existingPost) {
        return res.status(404).json({
          success: false,
          message: 'Blog post not found',
        });
      }

      // Handle slug update - only update if explicitly provided
      let slug = existingPost.slug; // Keep existing slug by default
      if (req.body.slug !== undefined) {
        // If a slug is explicitly provided (even if empty), use it
        slug = req.body.slug ? slugify(req.body.slug) : slugify(title || existingPost.title);
      }
      
      // Check if new slug already exists (only if it changed)
      if (slug !== existingPost.slug) {
        const slugExists = await prisma.blogPost.findFirst({
          where: {
            slug,
            id: { not: id },
          },
        });
        
        // If slug exists, append a unique identifier
        if (slugExists) {
          slug = `${slug}-${Date.now().toString().slice(-6)}`;
        }
      }

      // Set publishedAt date if status is changing to PUBLISHED
      let publishedAt = existingPost.publishedAt;
      if (status === 'PUBLISHED' && existingPost.status !== 'PUBLISHED') {
        publishedAt = new Date();
      }

      // Validate that all category IDs exist (if provided)
      let validCategoryIds: string[] = [];
      if (categoryIds !== undefined) {
        if (categoryIds.length > 0) {
          const existingCategories = await prisma.blogCategory.findMany({
            where: {
              id: {
                in: categoryIds
              }
            },
            select: { id: true }
          });

          validCategoryIds = existingCategories.map(cat => cat.id);
          if (validCategoryIds.length !== categoryIds.length) {
            return res.status(400).json({
              success: false,
              message: 'One or more category IDs do not exist'
            });
          }
        }
      }

      // Update the post
      const updatedPost = await prisma.$transaction(async (prisma) => {
        // Create redirect if slug changed
        if (slug !== existingPost.slug) {
          await RedirectService.createSlugChangeRedirect(
            existingPost.slug,
            slug,
            'blog',
            id,
            `Blog post slug changed from ${existingPost.slug} to ${slug}`
          );
        }

        // First, delete existing category connections if categoryIds is provided
        if (categoryIds !== undefined) {
          await prisma.blogPostCategory.deleteMany({
            where: { postId: id },
          });
        }

        // Then update the post
        // Construct update data
        const updateData: any = {
          title: title !== undefined ? title : undefined,
          slug,
          content: content !== undefined ? content : undefined,
          excerpt: excerpt !== undefined ? excerpt : undefined,
          featuredImage: featuredImage !== undefined ? featuredImage : undefined,
          metaTitle: metaTitle !== undefined ? metaTitle : undefined,
          metaDescription: metaDescription !== undefined ? metaDescription : undefined,
          status: status !== undefined ? status : undefined,
          publishedAt
        };

        // Only include categories if they were provided
        if (categoryIds !== undefined) {
          updateData.categories = {
            create: categoryIds.map(categoryId => ({
              category: {
                connect: { id: categoryId }
              }
            }))
          };
        }

        console.log('Update data:', updateData);

        return prisma.blogPost.update({
          where: { id },
          data: updateData,
          include: {
            categories: {
              include: {
                category: true,
              },
            },
          },
        });
      });

      // Transform the data to simplify the categories
      const formattedPost = {
        ...updatedPost,
        categories: updatedPost.categories.map(pc => pc.category),
      };

      return res.status(200).json({
        success: true,
        message: 'Blog post updated successfully',
        data: formattedPost,
      });
    } catch (error) {
      console.error('Error updating blog post:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to update blog post',
        error: error.message,
      });
    }
  },

  /**
   * Delete a blog post
   */
  deletePost: async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      // Check if post exists
      const existingPost = await prisma.blogPost.findUnique({
        where: { id },
      });

      if (!existingPost) {
        return res.status(404).json({
          success: false,
          message: 'Blog post not found',
        });
      }

      // Delete the post (this will cascade delete the category connections)
      await prisma.blogPost.delete({
        where: { id },
      });

      return res.status(200).json({
        success: true,
        message: 'Blog post deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting blog post:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to delete blog post',
        error: error.message,
      });
    }
  },

  /**
   * Get all blog categories
   */
  getAllCategories: async (req: Request, res: Response) => {
    try {
      const categories = await prisma.blogCategory.findMany({
        orderBy: {
          name: 'asc',
        },
      });

      return res.status(200).json({
        success: true,
        data: categories,
      });
    } catch (error) {
      console.error('Error fetching blog categories:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch blog categories',
        error: error.message,
      });
    }
  },

  /**
   * Get a single blog category
   */
  getCategory: async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      const category = await prisma.blogCategory.findUnique({
        where: { id },
      });

      if (!category) {
        return res.status(404).json({
          success: false,
          message: 'Blog category not found',
        });
      }

      return res.status(200).json({
        success: true,
        data: category,
      });
    } catch (error) {
      console.error('Error fetching blog category:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch blog category',
        error: error.message,
      });
    }
  },

  /**
   * Create a new blog category
   */
  createCategory: async (req: Request, res: Response) => {
    try {
      const { name, description } = req.body;

      // Generate slug from name
      let slug = slugify(name);
      
      // Check if slug already exists
      const existingCategory = await prisma.blogCategory.findUnique({
        where: { slug },
      });
      
      // If slug exists, append a unique identifier
      if (existingCategory) {
        slug = `${slug}-${Date.now().toString().slice(-6)}`;
      }

      // Create the category
      const category = await prisma.blogCategory.create({
        data: {
          name,
          slug,
          description,
        },
      });

      return res.status(201).json({
        success: true,
        message: 'Blog category created successfully',
        data: category,
      });
    } catch (error) {
      console.error('Error creating blog category:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to create blog category',
        error: error.message,
      });
    }
  },

  /**
   * Update an existing blog category
   */
  updateCategory: async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const { name, description, isActive } = req.body;

      // Check if category exists
      const existingCategory = await prisma.blogCategory.findUnique({
        where: { id },
      });

      if (!existingCategory) {
        return res.status(404).json({
          success: false,
          message: 'Blog category not found',
        });
      }

      // Generate new slug if name changed
      let slug = existingCategory.slug;
      if (name && name !== existingCategory.name) {
        slug = slugify(name);
        
        // Check if new slug already exists
        const slugExists = await prisma.blogCategory.findFirst({
          where: {
            slug,
            id: { not: id },
          },
        });
        
        // If slug exists, append a unique identifier
        if (slugExists) {
          slug = `${slug}-${Date.now().toString().slice(-6)}`;
        }
      }

      // Update the category
      const updatedCategory = await prisma.blogCategory.update({
        where: { id },
        data: {
          name: name !== undefined ? name : undefined,
          slug: slug !== existingCategory.slug ? slug : undefined,
          description: description !== undefined ? description : undefined,
          isActive: isActive !== undefined ? isActive : undefined,
        },
      });

      return res.status(200).json({
        success: true,
        message: 'Blog category updated successfully',
        data: updatedCategory,
      });
    } catch (error) {
      console.error('Error updating blog category:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to update blog category',
        error: error.message,
      });
    }
  },

  /**
   * Delete a blog category
   */
  deleteCategory: async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      // Check if category exists
      const existingCategory = await prisma.blogCategory.findUnique({
        where: { id },
      });

      if (!existingCategory) {
        return res.status(404).json({
          success: false,
          message: 'Blog category not found',
        });
      }

      // Check if category is used in any posts
      const categoryUsage = await prisma.blogPostCategory.findFirst({
        where: { categoryId: id },
      });

      if (categoryUsage) {
        return res.status(400).json({
          success: false,
          message: 'Cannot delete category as it is used in blog posts',
        });
      }

      // Delete the category
      await prisma.blogCategory.delete({
        where: { id },
      });

      return res.status(200).json({
        success: true,
        message: 'Blog category deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting blog category:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to delete blog category',
        error: error.message,
      });
    }
  },

  /**
   * Get posts by category
   */
  getPostsByCategory: async (req: Request, res: Response) => {
    try {
      const { identifier } = req.params; // Can be ID or slug
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 12;
      const skip = (page - 1) * limit;

      // Find the category
      const category = await prisma.blogCategory.findFirst({
        where: {
          OR: [
            { id: identifier },
            { slug: identifier },
          ],
        },
      });

      if (!category) {
        return res.status(404).json({
          success: false,
          message: 'Blog category not found',
        });
      }

      // Get total count for pagination
      const totalCount = await prisma.blogPostCategory.count({
        where: {
          categoryId: category.id,
          post: {
            status: 'PUBLISHED',
          },
        },
      });

      // Get posts in this category
      const postCategories = await prisma.blogPostCategory.findMany({
        where: {
          categoryId: category.id,
          post: {
            status: 'PUBLISHED',
          },
        },
        include: {
          post: {
            include: {
              categories: {
                include: {
                  category: true,
                },
              },
            },
          },
        },
        orderBy: {
          post: {
            publishedAt: 'desc',
          },
        },
        skip,
        take: limit,
      });

      // Transform the data
      const posts = postCategories.map(pc => ({
        ...pc.post,
        categories: pc.post.categories.map(c => c.category),
      }));

      return res.status(200).json({
        success: true,
        data: posts,
        category,
        pagination: {
          total: totalCount,
          page,
          limit,
          pages: Math.ceil(totalCount / limit),
        },
      });
    } catch (error) {
      console.error('Error fetching posts by category:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch posts by category',
        error: error.message,
      });
    }
  },
};
