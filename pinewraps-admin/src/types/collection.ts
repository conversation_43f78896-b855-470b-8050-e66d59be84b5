export enum CollectionStatus {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED'
}

export interface Collection {
  id: string;
  name: string;
  slug: string;
  description?: string;
  content?: string;
  status: CollectionStatus;
  image?: string;
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords: string[];
  products: CollectionProduct[];
  createdAt: string;
  updatedAt: string;
}

export interface CollectionProduct {
  id: string;
  productId: string;
  collectionId: string;
  position: number;
  product: {
    id: string;
    name: string;
    slug: string;
    basePrice: number;
    image?: string;
  };
}

export interface CreateCollectionDTO {
  name: string;
  description?: string;
  content?: string;
  status: CollectionStatus;
  image?: string;
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords: string[];
  products: string[];
}

export interface UpdateCollectionDTO extends Partial<CreateCollectionDTO> {
  id: string;
}
