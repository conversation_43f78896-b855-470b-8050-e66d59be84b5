import { Metada<PERSON> } from 'next';
import { BlogService } from '@/services/blog.service';
import BlogList from './BlogList';
import { createMetadata } from '@/lib/metadata';
import Link from 'next/link';
import { Breadcrumbs } from '@/components/shared/Breadcrumbs';
import JsonLd from '@/components/seo/JsonLd';

export async function generateMetadata(): Promise<Metadata> {
  try {
    const { data: categories } = await BlogService.getAllCategories();

    return createMetadata({
      title: 'Blog - Pinewraps Dubai',
      description: 'Read the latest news, tips, and insights from Pinewraps' + (categories ? ` in categories like ${categories.slice(0, 3).map(c => c.name).join(', ')}` : ''),
      path: '/blog',
    });
  } catch (error) {
    return createMetadata({
      title: 'Blog - Pinewraps Dubai',
      description: 'Read the latest news, tips, and insights from Pinewraps',
      path: '/blog',
    });
  }
}

export const dynamic = 'force-dynamic';

export default async function Page() {
  try {
    const postsResponse = await BlogService.getAllPosts(1, 12);
    const { data: categories } = await BlogService.getAllCategories();
    
    const posts = postsResponse.data;
    const pagination = postsResponse.meta;
    
    return (
      <div className="container mx-auto px-4 py-8">
        {/* Add breadcrumb structured data */}
        <JsonLd 
          type="breadcrumb" 
          data={[
            { label: 'Home', href: '/' },
            { label: 'Blog' }
          ]} 
        />
        
        {/* Breadcrumb Navigation */}
        <nav className="text-sm mb-8">
          <Breadcrumbs 
            items={[
              { label: 'Home', href: '/' },
              { label: 'Blog' }
            ]}
          />
        </nav>

        {/* Main Title */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900">Our Blog</h1>
          <p className="mt-4 text-xl text-gray-600">Latest news, tips, and insights from Pinewraps</p>
        </div>

        {/* Categories Navigation */}
        {categories && categories.length > 0 && (
          <div className="mb-12">
            <div className="flex flex-wrap justify-center gap-3">
              <Link 
                href="/blog"
                className="px-4 py-2 bg-gray-900 text-white rounded-full hover:bg-gray-700 transition"
              >
                All Posts
              </Link>
              {categories.map((category) => (
                <Link 
                  key={category.id}
                  href={`/blog/category/${category.slug}`}
                  className="px-4 py-2 bg-gray-100 text-gray-800 rounded-full hover:bg-gray-200 transition"
                >
                  {category.name}
                </Link>
              ))}
              <Link 
                href="/blog/category"
                className="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-full hover:bg-gray-50 transition"
              >
                View All Categories
              </Link>
            </div>
          </div>
        )}

        {/* About Section */}
        <div className="max-w-4xl mx-auto mb-16 px-4">
          <div className="prose lg:prose-lg text-gray-700">
            <p className="mb-6">
              Welcome to the Pinewraps blog, your go-to resource for all things related to premium cakes, beautiful flowers, and thoughtful gifting in Dubai. Our team of experts shares valuable insights, creative ideas, and the latest trends to help you make every occasion special.
            </p>
            
            <h2 className="text-2xl font-bold text-gray-900 mt-10 mb-4">About Pinewraps</h2>
            <p>
              Pinewraps is an established bakery that specializes in crafting exquisite cakes, stunning flower arrangements, and premium gifting products. With years of experience in the industry, we take pride in creating memorable moments through our artisanal creations. Our commitment to quality, creativity, and customer satisfaction has made us a trusted name in Dubai's gifting scene.
            </p>
          </div>
        </div>

        {/* Blog Posts */}
        {!posts || posts.length === 0 ? (
          <div className="text-center py-10">No blog posts found.</div>
        ) : (
          <BlogList initialPosts={posts} initialPagination={pagination} />
        )}
      </div>
    );
  } catch (error) {
    console.error('Error loading blog page:', error);
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4">Our Blog</h1>
          <p className="text-xl text-gray-600">Latest news, tips, and insights from Pinewraps</p>
        </div>
        <div className="text-center py-10">Failed to load blog posts. Please try again later.</div>
      </div>
    );
  }
}
