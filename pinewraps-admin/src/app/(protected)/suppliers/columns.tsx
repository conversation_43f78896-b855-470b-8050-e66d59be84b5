'use client';

import { ColumnDef } from '@tanstack/react-table';
import { ArrowUpDown, MoreHorizontal, Pencil } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { useRouter } from 'next/navigation';

export type Supplier = {
  id: string;
  name: string;
  code: string;
  email: string | null;
  phone: string | null;
  address: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  _count?: {
    items: number;
    purchaseOrders: number;
  };
};

const ActionsCell = ({ row }: { row: any }) => {
  const router = useRouter();
  const supplier = row.original;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuItem onClick={(e) => {
          e.preventDefault();
          router.push(`/suppliers/${supplier.id}`);
        }}>
          <Pencil className="mr-2 h-4 w-4" />
          Edit
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export const columns: ColumnDef<Supplier>[] = [
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
      >
        Name
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    size: 200,
    cell: ({ row }) => (
      <div className="font-medium">
        {row.getValue('name')}
      </div>
    ),
  },
  {
    accessorKey: 'code',
    header: 'Code',
    size: 100,
    cell: ({ row }) => (
      <div className="font-mono text-sm">
        {row.getValue('code')}
      </div>
    ),
  },
  {
    accessorKey: 'email',
    header: 'Email',
    size: 220,
    cell: ({ row }) => (
      <div className="text-sm">
        {row.getValue('email') || '-'}
      </div>
    ),
  },
  {
    accessorKey: 'phone',
    header: 'Phone',
    size: 140,
    cell: ({ row }) => (
      <div className="text-sm font-mono">
        {row.getValue('phone') || '-'}
      </div>
    ),
  },
  {
    accessorKey: 'address',
    header: 'Address',
    size: 200,
    cell: ({ row }) => (
      <div className="text-sm">
        {row.getValue('address') || '-'}
      </div>
    ),
  },
  {
    accessorKey: '_count.items',
    header: 'Items',
  },
  {
    accessorKey: '_count.purchaseOrders',
    header: 'Purchase Orders',
  },
  {
    accessorKey: 'isActive',
    header: 'Status',
    cell: ({ row }) => (
      <Badge variant={row.original.isActive ? 'secondary' : 'outline'}>
        {row.original.isActive ? 'Active' : 'Inactive'}
      </Badge>
    ),
  },
  {
    id: 'actions',
    cell: ActionsCell,
  },
];
