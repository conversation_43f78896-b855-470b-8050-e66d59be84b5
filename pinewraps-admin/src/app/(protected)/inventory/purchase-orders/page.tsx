'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import { Download, Plus, Upload, Search } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { DataTable } from '@/components/ui/data-table';
import { Heading } from '@/components/ui/heading';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';

import { columns } from './columns';
import api from '@/lib/api';
import { PurchaseOrder } from './columns';

interface FiltersProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
}

const Filters = ({ searchTerm, onSearchChange }: FiltersProps) => (
  <div className="flex items-center gap-4">
    <div className="relative">
      <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
      <Input
        placeholder="Search by PO number or supplier..."
        value={searchTerm}
        onChange={(e) => onSearchChange(e.target.value)}
        className="pl-8 w-[300px]"
      />
    </div>
  </div>
);

export default function PurchaseOrdersPage() {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [importing, setImporting] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const [loading, setLoading] = useState(true);
  const [filteredData, setFilteredData] = useState<PurchaseOrder[]>([]);

  // Get initial values from URL search params
  const [searchTerm, setSearchTerm] = useState(searchParams.get('search') || '');
  const [page, setPage] = useState(parseInt(searchParams.get('page') || '1'));
  const [pageSize, setPageSize] = useState(parseInt(searchParams.get('pageSize') || '10'));
  const [total, setTotal] = useState(0);

  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(searchParams.get('search') || '');

  // Function to update URL with current state
  const updateURL = useCallback((newPage?: number, newSearch?: string, newPageSize?: number) => {
    const params = new URLSearchParams();

    if (newPage && newPage > 1) params.set('page', newPage.toString());
    if (newSearch) params.set('search', newSearch);
    if (newPageSize && newPageSize !== 10) params.set('pageSize', newPageSize.toString());

    const newURL = params.toString() ? `?${params.toString()}` : '';
    router.replace(`/inventory/purchase-orders${newURL}`, { scroll: false });
  }, [router]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300); // 300ms debounce

    return () => clearTimeout(timer);
  }, [searchTerm]);

  useEffect(() => {
    loadPurchaseOrders();
  }, []);
  
  useEffect(() => {
    if (debouncedSearchTerm !== (searchParams.get('search') || '')) {
      setPage(1); // Reset to first page when search changes
      loadPurchaseOrders(1, debouncedSearchTerm);
    }
  }, [debouncedSearchTerm, searchParams]);

  // We don't need to filter locally anymore as the server will handle it

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      setImporting(true);
      const formData = new FormData();
      formData.append('file', file);

      await api.post('/api/purchase-orders/import', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      toast.success('Purchase orders imported successfully');
      loadPurchaseOrders();
    } catch (error: any) {
      console.error('Import error:', error);
      toast.error(error.response?.data?.message || 'Failed to import purchase orders');
    } finally {
      setImporting(false);
    }
  };

  const loadPurchaseOrders = async (newPage?: number, newSearch?: string) => {
    try {
      setLoading(true);
      const currentPage = newPage || page;
      const currentSearch = newSearch !== undefined ? newSearch : debouncedSearchTerm;

      // Add search parameters to the API request
      let url = `/api/purchase-orders?page=${currentPage}&pageSize=${pageSize}`;

      // Add search term if present
      if (currentSearch) {
        url += `&search=${encodeURIComponent(currentSearch)}`;
      }

      const response = await api.get(url);
      setFilteredData(response.data.data.items);
      setPage(response.data.data.page);
      setTotal(response.data.data.total);

      // Update URL with current state
      updateURL(response.data.data.page, currentSearch, pageSize);
    } catch (error) {
      console.error('Error loading purchase orders:', error);
    } finally {
      setLoading(false);
    }
  };

  // We no longer need the local filtering function as the server handles the filtering

  const handleSearchChange = useCallback((value: string) => {
    setSearchTerm(value);
  }, []);

  return (
    <div className="flex-1 space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <Heading
            title={`Purchase Orders (${filteredData.length})`}
            description="Manage purchase orders"
          />
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={async () => {
              try {
                const response = await api.get('/api/purchase-orders/template', {
                  responseType: 'blob'
                });
                
                const url = window.URL.createObjectURL(new Blob([response.data]));
                const link = document.createElement('a');
                link.href = url;
                link.setAttribute('download', 'purchase-order-template.csv');
                document.body.appendChild(link);
                link.click();
                link.remove();
                window.URL.revokeObjectURL(url);
              } catch (error) {
                console.error('Download error:', error);
                toast.error('Failed to download template');
              }
            }}
          >
            <Download className="mr-2 h-4 w-4" /> Template
          </Button>
          <input
            type="file"
            ref={fileInputRef}
            className="hidden"
            accept=".csv"
            onChange={handleFileUpload}
            onClick={(e) => (e.currentTarget.value = '')}
          />
          <Button
            variant="outline"
            size="sm"
            onClick={() => fileInputRef.current?.click()}
            disabled={importing}
          >
            <Upload className="mr-2 h-4 w-4" /> Import
          </Button>
          <Button 
            onClick={() => router.push('/inventory/purchase-orders/new')}
            size="sm"
          >
            <Plus className="mr-2 h-4 w-4" /> New Order
          </Button>
        </div>
      </div>
      <Separator />
      
      <DataTable
        columns={columns}
        data={filteredData}
        loading={loading}
        pagination={{
          page,
          pageSize,
          total,
          onPageChange: (newPage) => {
            setPage(newPage);
            loadPurchaseOrders(newPage);
          }
        }}
        meta={{
          onPageSizeChange: (newPageSize: number) => {
            setPageSize(newPageSize);
            setPage(1); // Reset to first page when changing page size
            loadPurchaseOrders(1);
          }
        }}
        filterComponent={
          <Filters
            searchTerm={searchTerm}
            onSearchChange={handleSearchChange}
          />
        }
      />
    </div>
  );
}
