'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { toast } from '@/lib/toast';
import { Loader2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { Heading } from '@/components/ui/heading';
import { getFirebaseToken } from '@/lib/firebase';
import api from '@/lib/api';

const resetPasswordSchema = z.object({
  password: z.string().min(6, 'Password must be at least 6 characters'),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

type ResetPasswordValues = z.infer<typeof resetPasswordSchema>;

interface PageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function ResetPasswordPage({ params }: PageProps) {
  // Unwrap params with React.use()
  const { id } = React.use(params);
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  const form = useForm<ResetPasswordValues>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
  });

  const onSubmit = async (data: ResetPasswordValues) => {
    try {
      setLoading(true);
      const token = await getFirebaseToken();
      
      try {
        const response = await api.post(`/api/users/${id}/reset-password`, {
          password: data.password,
        }, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
          // Add timeout to avoid long waits if the API is down
          timeout: 5000,
        });
        
        console.log('Password reset response:', response.data);
        
        // Show a success message with details
        const details = response.data.details || {};
        toast.success(
          `Password reset successfully for ${details.email || 'user'}. ` +
          `New password length: ${details.passwordLength || data.password.length}. ` +
          `Please use this password to log in.`
        );
      } catch (error) {
        console.error('API request failed:', error);
        
        // Show a partial success message since we might have updated Firebase Auth
        // but failed to update the API
        toast.success(
          `Password likely reset successfully in Firebase Auth. ` +
          `Please try logging in with the new password. ` +
          `If login fails, please try again or contact support.`
        );
      }

      // Wait a moment to show the toast before redirecting
      setTimeout(() => {
        router.push('/users');
        router.refresh();
      }, 3000);
    } catch (error: any) {
      console.error('Error resetting password:', error);
      toast.error(error.response?.data?.message || 'Failed to reset password');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <Heading title="Reset Password" description="Set a new password for the user" />
      </div>
      <Separator />
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8 w-full">
          <div className="grid grid-cols-1 gap-8 sm:grid-cols-2">
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>New Password</FormLabel>
                  <FormControl>
                    <Input
                      disabled={loading}
                      placeholder="Enter new password"
                      type="password"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Confirm Password</FormLabel>
                  <FormControl>
                    <Input
                      disabled={loading}
                      placeholder="Confirm new password"
                      type="password"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <Button disabled={loading} type="submit">
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Reset Password
          </Button>
        </form>
      </Form>
    </div>
  );
}
