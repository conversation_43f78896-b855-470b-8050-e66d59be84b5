'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Progress } from '@/components/ui/progress';
import { 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  TrendingDown,
  TrendingUp,
  Award
} from 'lucide-react';
import { QualityMetrics } from '@/services/staff-reports.service';

interface QualityControlPanelProps {
  data?: QualityMetrics;
  isLoading: boolean;
}

export default function QualityControlPanel({ data, isLoading }: QualityControlPanelProps) {
  // Add debugging for quality metrics
  React.useEffect(() => {
    if (data) {
      console.log('Quality metrics data:', data);
      console.log('Orders inspected:', data.totalOrdersProcessed);
      console.log('Orders sent back:', data.ordersSentBack);
      console.log('Overall return rate:', data.returnRate);
      console.log('Returns by department:', data.returnsByDepartment);
      console.log('Department order counts:', data.departmentOrderCounts);
    }
  }, [data]);
  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="grid gap-4 md:grid-cols-3">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16 mb-2" />
                <Skeleton className="h-3 w-32" />
              </CardContent>
            </Card>
          ))}
        </div>
        
        <div className="grid gap-4 md:grid-cols-2">
          <Card>
            <CardHeader>
              <Skeleton className="h-5 w-32" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-32 w-full" />
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <Skeleton className="h-5 w-24" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-32 w-full" />
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Quality Control Metrics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            No quality control data available.
          </div>
        </CardContent>
      </Card>
    );
  }

  const qualityScore = data.totalOrdersProcessed > 0 
    ? ((data.totalOrdersProcessed - data.ordersSentBack) / data.totalOrdersProcessed) * 100 
    : 100;

  const getQualityRating = (score: number) => {
    if (score >= 95) return { label: 'Excellent', color: 'text-green-600', icon: Award };
    if (score >= 90) return { label: 'Good', color: 'text-blue-600', icon: CheckCircle };
    if (score >= 80) return { label: 'Average', color: 'text-yellow-600', icon: AlertTriangle };
    return { label: 'Poor', color: 'text-red-600', icon: XCircle };
  };

  const rating = getQualityRating(qualityScore);
  const RatingIcon = rating.icon;

  return (
    <div className="space-y-4">
      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalOrdersProcessed.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Orders processed in period
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Orders Sent Back</CardTitle>
            <XCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{data.ordersSentBack}</div>
            <p className="text-xs text-muted-foreground">
              Quality issues identified
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Overall Quality</CardTitle>
            <RatingIcon className={`h-4 w-4 ${rating.color}`} />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${rating.color}`}>
              {qualityScore.toFixed(1)}%
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className={rating.color}>
                {rating.label}
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        {/* Return Rate by Department */}
        <Card>
          <CardHeader>
            <CardTitle>Return Rate by Department</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(data.returnsByDepartment)
                // Only show kitchen and design departments (final check is the one that sends orders back)
                .filter(([department]) => department === 'kitchen' || department === 'design')
                .map(([department, returns]) => {
                  // Normalize department name for display
                  const displayName = department.charAt(0).toUpperCase() + department.slice(1);

                  // Calculate accurate return rate based on actual orders processed by this department
                  const departmentOrders = data.departmentOrderCounts?.[department as keyof typeof data.departmentOrderCounts] || 0;
                  const returnRate = departmentOrders > 0 ? (returns / departmentOrders) * 100 : 0;
                  
                  return (
                    <div key={department} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="font-medium">{displayName}</span>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-muted-foreground">
                            {returns} returns / {departmentOrders} orders
                          </span>
                          <Badge
                            variant={returnRate > 5 ? "destructive" : returnRate > 2 ? "secondary" : "default"}
                          >
                            {returnRate.toFixed(1)}%
                          </Badge>
                        </div>
                      </div>
                      <Progress 
                        value={100 - returnRate} 
                        className={returnRate > 5 ? "bg-red-200" : returnRate > 2 ? "bg-yellow-200" : "bg-green-200"}
                      />
                    </div>
                  );
                })
              }
            </div>
          </CardContent>
        </Card>

        {/* Common Issues */}
        <Card>
          <CardHeader>
            <CardTitle>Common Quality Issues</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.commonIssues.length > 0 ? (
                data.commonIssues.slice(0, 8).map((issue, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="flex h-6 w-6 items-center justify-center rounded-full bg-muted text-xs font-medium">
                        {index + 1}
                      </div>
                      <span className="capitalize">{issue.issue}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline">{issue.department}</Badge>
                      <span className="text-sm font-medium">{issue.count}</span>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-4 text-muted-foreground">
                  No common issues identified in the selected period.
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quality Insights */}
      <Card>
        <CardHeader>
          <CardTitle>Quality Insights</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <h4 className="font-medium">Performance Summary</h4>
              <div className="text-sm text-muted-foreground space-y-1">
                <p>
                  • {data.totalOrdersProcessed} total orders processed
                </p>
                <p>
                  • {data.ordersSentBack} orders required rework ({data.returnRate.toFixed(1)}% return rate)
                </p>
                <p>
                  • Overall quality score: {qualityScore.toFixed(1)}% ({rating.label})
                </p>
              </div>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">Recommendations</h4>
              <div className="text-sm text-muted-foreground space-y-1">
                {qualityScore >= 95 ? (
                  <p>• Excellent quality standards maintained</p>
                ) : qualityScore >= 90 ? (
                  <p>• Good quality with room for minor improvements</p>
                ) : (
                  <>
                    <p>• Focus on reducing return rates</p>
                    <p>• Review common issues and provide targeted training</p>
                    <p>• Implement additional quality checkpoints</p>
                  </>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
