'use client';

import { Heading } from '@/components/ui/heading';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAuth } from '@/providers/auth-provider';
import { useState, useEffect } from 'react';
import { 
  updatePassword,
  updateProfile,
  EmailAuthProvider,
  reauthenticateWithCredential,
  getAuth
} from 'firebase/auth';
import { Loader2 } from 'lucide-react';
import toast from 'react-hot-toast';
import { Label } from '@/components/ui/label';
import { useRouter } from 'next/navigation';
import { api } from '@/lib/api/api';

interface UserData {
  id: string;
  firstName: string;
  lastName: string;
  role: string;
  permissions: Record<string, any>;
  email: string;
  isActive: boolean;
}

interface UserType {
  role: string;
  permissions: string[];
  label: string;
}

const USER_TYPES: UserType[] = [
  { role: 'SUPER_ADMIN', permissions: ['all'], label: 'Super Admin' },
  { role: 'ADMIN', permissions: ['manage_users', 'manage_orders', 'manage_products', 'manage_inventory'], label: 'Admin' },
  { role: 'POS_USER', permissions: ['manage_orders', 'view_products', 'view_inventory'], label: 'POS User' },
  { role: 'DRIVER', permissions: ['manage_deliveries', 'view_orders'], label: 'Driver' }
];

export default function SettingsPage() {
  const auth = getAuth();
  const { user } = useAuth();
  const router = useRouter();
  
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const [userData, setUserData] = useState<UserData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [users, setUsers] = useState<UserData[]>([]);

  useEffect(() => {
    const fetchInitialData = async () => {
      if (!auth.currentUser) return;
      
      try {
        setIsLoading(true);
        
        // Fetch current user data
        const userResponse = await api.get(`/users/firebase/${auth.currentUser.uid}`);
        const currentUserData = userResponse.data.data;
        setUserData(currentUserData);
        
        // Update form with user data
        setFormData(prev => ({
          ...prev,
          firstName: currentUserData.firstName || '',
          lastName: currentUserData.lastName || ''
        }));

        // If user is an admin or super admin, fetch all users
        if (currentUserData.role === 'SUPER_ADMIN' || currentUserData.role === 'ADMIN') {
          const usersResponse = await api.get('/users');
          setUsers(usersResponse.data.data);
        }
      } catch (error: any) {
        console.error('Error fetching data:', error);
        toast.error(error.message || 'Failed to load data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchInitialData();
  }, [auth.currentUser]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleNameUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!auth.currentUser || !userData) return;

    try {
      setIsLoading(true);
      
      // Update in Firebase
      const displayName = `${formData.firstName} ${formData.lastName}`.trim();
      await updateProfile(auth.currentUser, { displayName });
      
      // Update in backend
      const response = await api.put(`/users/${userData.id}`, {
        firstName: formData.firstName,
        lastName: formData.lastName,
        role: userData.role,
        isActive: true,
        permissions: userData.permissions
      });

      if (!response.data.success) {
        throw new Error(response.data.message || 'Failed to update profile');
      }

      await auth.currentUser.getIdToken(true);
      await auth.currentUser.reload();
      
      toast.success('Profile updated successfully');
    } catch (error: any) {
      console.error('Profile update error:', error);
      toast.error(error.message || 'Failed to update profile');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePasswordUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!auth.currentUser || !auth.currentUser.email) return;

    if (formData.newPassword !== formData.confirmPassword) {
      toast.error('New passwords do not match');
      return;
    }

    if (!formData.currentPassword) {
      toast.error('Please enter your current password');
      return;
    }

    try {
      setIsLoading(true);
      
      // Re-authenticate user
      const credential = EmailAuthProvider.credential(
        auth.currentUser.email,
        formData.currentPassword
      );
      await reauthenticateWithCredential(auth.currentUser, credential);
      
      // Update password
      await updatePassword(auth.currentUser, formData.newPassword);
      
      toast.success('Password updated successfully');
      
      // Clear password fields
      setFormData(prev => ({
        ...prev,
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      }));
    } catch (error: any) {
      console.error('Password update error:', error);
      if (error.code === 'auth/wrong-password') {
        toast.error('Current password is incorrect');
      } else {
        toast.error(error.message || 'Failed to update password');
      }
    } finally {
      setIsLoading(false);
    }
  };

  if (!user) return null;

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between">
        <Heading
          title="Settings"
          description="Manage your account settings"
        />
      </div>
      <Separator />
      
      <div className="grid gap-4">
        {/* Profile Information */}
        <Card>
          <CardHeader>
            <CardTitle>Profile Information</CardTitle>
            <CardDescription>
              Update your name and personal details
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleNameUpdate} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName">First Name</Label>
                  <Input
                    id="firstName"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleInputChange}
                    placeholder="First name"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastName">Last Name</Label>
                  <Input
                    id="lastName"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleInputChange}
                    placeholder="Last name"
                  />
                </div>
              </div>
              
              <div className="flex justify-end">
                <Button type="submit" disabled={isLoading}>
                  {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Save Changes
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

        {/* Password Update */}
        <Card>
          <CardHeader>
            <CardTitle>Change Password</CardTitle>
            <CardDescription>
              Update your password to keep your account secure
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handlePasswordUpdate} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="currentPassword">Current Password</Label>
                <Input
                  id="currentPassword"
                  name="currentPassword"
                  type="password"
                  value={formData.currentPassword}
                  onChange={handleInputChange}
                  placeholder="Enter current password"
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="newPassword">New Password</Label>
                  <Input
                    id="newPassword"
                    name="newPassword"
                    type="password"
                    value={formData.newPassword}
                    onChange={handleInputChange}
                    placeholder="Enter new password"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Confirm Password</Label>
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type="password"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    placeholder="Confirm new password"
                  />
                </div>
              </div>
              
              <div className="flex justify-end">
                <Button 
                  type="submit" 
                  disabled={isLoading || !formData.currentPassword || !formData.newPassword || !formData.confirmPassword}
                >
                  {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Update Password
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

        {/* Read-only Information */}
        <Card>
          <CardHeader>
            <CardTitle>Account Information</CardTitle>
            <CardDescription>
              Your account details and status
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Email</Label>
                  <p className="text-sm text-muted-foreground mt-1">{user.email}</p>
                </div>
                <div>
                  <Label>Role</Label>
                  <p className="text-sm text-muted-foreground mt-1 capitalize">{user.role?.toLowerCase()}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
