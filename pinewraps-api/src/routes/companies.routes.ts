import express from 'express';
import { companiesController } from '../controllers/companies.controller';
import { requireAdminPanelAccess, requireAuth } from '../middleware/auth';

const router = express.Router();

// First authenticate the user, then check for admin panel access
router.use(requireAuth);
router.use(requireAdminPanelAccess);

router.post('/', companiesController.createCompany);
router.get('/', companiesController.getCompanies);
router.get('/search', companiesController.searchCompanies);
router.get('/:id', companiesController.getCompanyById);
router.patch('/:id', companiesController.updateCompany);
router.delete('/:id', companiesController.deleteCompany);

export default router;
