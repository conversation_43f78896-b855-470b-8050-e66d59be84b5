'use client';

import { useEffect, useState } from 'react';

interface JsonLdProps {
  type: 'product' | 'organization' | 'website' | 'breadcrumb' | 'blogPosting';
  data: any;
}

export default function JsonLd({ type, data }: JsonLdProps) {
  const [mounted, setMounted] = useState(false);
  
  // Only render on client-side to avoid hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);
  
  // Return null during server-side rendering or initial client render
  if (!mounted) {
    return null;
  }
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://pinewraps.com';
  
  let schema;
  
  switch (type) {
    case 'product':
      schema = {
        '@context': 'https://schema.org',
        '@type': 'Product',
        name: data.name,
        description: data.description?.replace(/<[^>]*>/g, '') || '',
        image: data.images?.[0]?.url || `${baseUrl}/images/placeholder.jpg`,
        sku: data.sku || data.id,
        offers: {
          '@type': 'Offer',
          price: data.price,
          priceCurrency: 'AED',
          availability: data.status === 'ACTIVE' ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock',
          url: `${baseUrl}/shop/${data.slug}`,
        },
      };
      break;
    case 'organization':
      schema = {
        '@context': 'https://schema.org',
        '@type': 'Organization',
        name: 'Pinewraps Dubai',
        url: baseUrl,
        logo: `${baseUrl}/logo.png`,
        description: 'Premium cakes, fresh flowers, and perfect gift combinations for special occasions in Dubai.',
        address: {
          '@type': 'PostalAddress',
          addressLocality: 'Dubai',
          addressRegion: 'Dubai',
          addressCountry: 'AE',
        },
        contactPoint: {
          '@type': 'ContactPoint',
          telephone: '+971544044864',
          contactType: 'customer service',
          email: '<EMAIL>',
          areaServed: 'AE',
          availableLanguage: ['English', 'Arabic'],
        },
        sameAs: [
          'https://www.instagram.com/pine.wraps/',
          'https://www.facebook.com/Pinewraps-236418870100022/'
        ],
        openingHoursSpecification: {
          '@type': 'OpeningHoursSpecification',
          dayOfWeek: [
            'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'
          ],
          opens: '09:00',
          closes: '21:00'
        }
      };
      break;
    case 'website':
      schema = {
        '@context': 'https://schema.org',
        '@type': 'WebSite',
        name: 'Pinewraps Dubai',
        url: baseUrl,
        description: 'Discover exquisite cakes and beautiful flower arrangements for your special occasions. Premium same-day delivery across Dubai.',
        potentialAction: {
          '@type': 'SearchAction',
          target: `${baseUrl}/search?q={search_term_string}`,
          'query-input': 'required name=search_term_string'
        }
      };
      break;
    case 'breadcrumb':
      schema = {
        '@context': 'https://schema.org',
        '@type': 'BreadcrumbList',
        itemListElement: data.map((item: any, index: number) => ({
          '@type': 'ListItem',
          position: index + 1,
          name: item.label,
          item: item.href ? `${baseUrl}${item.href.startsWith('/') ? item.href : `/${item.href}`}` : undefined,
        })),
      };
      break;
    case 'blogPosting':
      schema = {
        '@context': 'https://schema.org',
        '@type': 'BlogPosting',
        headline: data.title,
        image: data.featuredImage ? [data.featuredImage] : [],
        datePublished: data.publishedAt,
        dateModified: data.updatedAt || data.publishedAt,
        author: {
          '@type': 'Person',
          name: data.author || 'Pinewraps Team',
        },
        publisher: {
          '@type': 'Organization',
          name: 'Pinewraps Dubai',
          logo: {
            '@type': 'ImageObject',
            url: `${baseUrl}/logo.png`,
          },
        },
        description: data.excerpt || data.metaDescription || '',
        mainEntityOfPage: {
          '@type': 'WebPage',
          '@id': `${baseUrl}/blog/${data.slug}`,
        },
      };
      break;
  }
  
  return (
    <script
      key={`jsonld-${type}-${JSON.stringify(data)}`}
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
    />
  );
}
