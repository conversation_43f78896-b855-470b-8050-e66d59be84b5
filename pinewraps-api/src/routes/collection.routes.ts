import { Router } from 'express';
import multer from 'multer';
import { CollectionController } from '../controllers/collection.controller';
import { requireAuth } from '../middleware/auth';
import { uploadToFirebase, deleteFromFirebase } from '../utils/upload';
import { ApiError } from '../utils/api-error';

const router = Router();
const controller = new CollectionController();

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
});

// Public routes
router.get('/public', controller.getAll.bind(controller));
router.get('/public/:slug', controller.getBySlug.bind(controller));

// Protected routes (admin only)
router.use(requireAuth);

// Collection CRUD routes
router.post('/', controller.create.bind(controller));
router.get('/', controller.getAll.bind(controller));
router.get('/id/:id', controller.getById.bind(controller));
router.get('/:slug', controller.getBySlug.bind(controller));
router.put('/:id', controller.update.bind(controller));
router.delete('/:id', controller.delete.bind(controller));
router.put('/:id/positions', controller.updateProductPositions.bind(controller));

// Collection image upload routes
router.post('/upload', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      throw new ApiError('No file provided', 400);
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(req.file.mimetype)) {
      throw new ApiError('Invalid file type. Only JPEG, PNG and WebP are allowed.', 400);
    }

    // Generate path based on file type and context
    const timestamp = Date.now();
    const fileName = req.file.originalname;
    // Remove existing extension to avoid duplicates
    const nameWithoutExt = fileName.replace(/\.[^/.]+$/, '');
    const fileExt = fileName.split('.').pop();
    const customPath = `collections/${timestamp}-${nameWithoutExt.replace(/[^a-zA-Z0-9-]/g, '_')}.${fileExt}`;

    const result = await uploadToFirebase(req.file, customPath);
    res.json(result);
  } catch (error) {
    if (error instanceof ApiError) {
      res.status(error.statusCode).json({ error: error.message });
    } else {
      console.error('Upload error:', error);
      res.status(500).json({ error: 'Failed to upload file' });
    }
  }
});

// Delete collection image
router.delete('/upload', async (req, res) => {
  try {
    const { url } = req.body;
    if (!url) {
      throw new ApiError('No file URL provided', 400);
    }

    await deleteFromFirebase(url);
    res.json({ message: 'File deleted successfully' });
  } catch (error) {
    if (error instanceof ApiError) {
      res.status(error.statusCode).json({ error: error.message });
    } else {
      console.error('Delete error:', error);
      res.status(500).json({ error: 'Failed to delete file' });
    }
  }
});

export default router;
