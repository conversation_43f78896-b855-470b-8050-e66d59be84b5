import { Metadata } from 'next';
import Link from 'next/link';
import BlogPostContent from './BlogPostContent';
import { BlogService } from '@/services/blog.service';
import { createBlogPostMetadata } from '@/lib/metadata';

export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  // Ensure params is properly awaited
  const slug = params?.slug || '';
  
  try {
    const { data: post } = await BlogService.getPostBySlug(slug);

    return createBlogPostMetadata({
      title: post.title,
      excerpt: post.excerpt,
      metaTitle: post.metaTitle,
      metaDescription: post.metaDescription,
      slug: slug,
      featuredImage: post.featuredImage,
    });
  } catch (error) {
    return createBlogPostMetadata({
      title: 'Blog Post',
      slug: slug,
    });
  }
}

export default async function BlogPostPage({ params }: { params: { slug: string } }) {
  // Ensure params is properly awaited
  const slug = params?.slug || '';
  
  try {
    const response = await BlogService.getPostBySlug(slug);
    return <BlogPostContent post={response.data} />;
  } catch (error) {
    return (
      <div className="min-h-screen bg-white flex flex-col items-center justify-center p-4">
        <h1 className="text-2xl font-bold text-gray-800 mb-4">Post Not Found</h1>
        <p className="text-gray-600 mb-6">The requested blog post could not be found or is not published.</p>
        <Link 
          href="/blog" 
          className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          Back to Blog
        </Link>
      </div>
    );
  }
}
