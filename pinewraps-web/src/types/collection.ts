import { Product } from './product';

export type CollectionStatus = 'DRAFT' | 'PUBLISHED';

export interface Collection {
  id: string;
  name: string;
  slug: string;
  description?: string | null;
  content?: string | null;
  status: CollectionStatus;
  image?: string | null;
  seoTitle?: string | null;
  seoDescription?: string | null;
  seoKeywords: string[];
  createdAt: string;
  updatedAt: string;
  products: CollectionProduct[];
}

export interface CollectionProduct {
  id: string;
  productId: string;
  collectionId: string;
  position: number;
  product: Product;
}

export interface CreateCollectionDto {
  name: string;
  description?: string;
  content?: string;
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
  image?: string;
  status?: CollectionStatus;
  products?: string[]; // Array of product IDs
}

export interface UpdateCollectionDto extends Partial<CreateCollectionDto> {
  id: string;
}

export interface UpdateProductPositionsDto {
  products: {
    productId: string;
    position: number;
  }[];
}
