'use client';

import { useState, useEffect, useCallback } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { Heading } from '@/components/ui/heading';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { Loader2, Plus, Trash, Download, X, Check, CheckCircle, XCircle } from 'lucide-react';
import { useRouter } from 'next/navigation';
import toast from 'react-hot-toast';
import api from '@/lib/api';
import { getFirebaseToken } from '@/lib/firebase';
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

import { useInvoiceValidation } from '@/hooks/use-invoice-validation';

const purchaseOrderFormSchema = z.object({
  supplierId: z.string().min(1, 'Supplier is required'),
  paymentTerms: z.enum(['CREDIT', 'PAID_IN_ADVANCE', 'NET_15', 'NET_30', 'NET_45', 'NET_60', 'COD']),
  invoiceNumber: z.string().nullable().optional(),
  invoiceDate: z.date().nullable().optional(),
  invoicePdfUrl: z.string().nullable().optional(),
  paymentReference: z.string().nullable().optional(),
  bankPaymentReference: z.string().nullable().optional(),
  bankPaymentReferenceDate: z.union([z.date(), z.string()]).nullable().optional(),
  notes: z.string().nullable().optional(),
  additionalCharge: z.union([z.number(), z.string()]).transform(val => 
    typeof val === 'string' ? parseFloat(val) || 0 : val || 0
  ).default(0),
  tax: z.union([z.number(), z.string()]).transform(val => 
    typeof val === 'string' ? parseFloat(val) || 0 : val || 0
  ).default(0),
  status: z.enum(['DRAFT', 'PENDING', 'APPROVED', 'COMPLETED', 'CANCELLED']).optional(),
  items: z.array(
    z.object({
      id: z.string().optional(),
      itemId: z.string().min(1, 'Item is required'),
      quantity: z.union([z.number(), z.string()]).transform(val =>
        typeof val === 'string' ? parseFloat(val) || 0 : val || 0
      ),
      totalPrice: z.union([z.number(), z.string()]).transform(val => 
        typeof val === 'string' ? parseFloat(val) || 0 : val || 0
      ),
      unitPrice: z.union([z.number(), z.string()]).transform(val => 
        typeof val === 'string' ? parseFloat(val) || 0 : val || 0
      ),
      notes: z.string().nullable().optional(),
    })
  ).min(1, 'At least one item is required'),
});

type PurchaseOrderFormValues = z.infer<typeof purchaseOrderFormSchema>;

interface PurchaseOrderFormProps {
  initialData?: any;
  returnUrl?: string;
}

interface Supplier {
  id: string;
  name: string;
  code: string;
  email?: string;
}

interface InventoryItem {
  id: string;
  name: string;
  sku: string;
  description: string;
  currentStock: number;
  unitMetricId: string;
  unitMetric?: {
    id: string;
    name: string;
    displayName: string;
  };
  conversionRate?: number;
}

export function PurchaseOrderForm({ initialData, returnUrl = '/inventory/purchase-orders' }: PurchaseOrderFormProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [suppliersLoading, setSuppliersLoading] = useState(false);
  const [itemsLoading, setItemsLoading] = useState(false);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [items, setItems] = useState<InventoryItem[]>([]);
  const [openSupplierCombobox, setOpenSupplierCombobox] = useState(false);
  const [supplierSearch, setSupplierSearch] = useState('');
  const [openItemCombobox, setOpenItemCombobox] = useState<number | null>(null);
  const [itemSearch, setItemSearch] = useState('');
  const id = initialData?.id;
  const orderNumber = initialData?.orderNumber;
  const [pdfPreviewUrl, setPdfPreviewUrl] = useState<string | null>(null);

  // Invoice validation hook
  const { validationState, validateInvoiceNumber, cleanup } = useInvoiceValidation({
    excludeId: initialData?.id,
    debounceMs: 500,
  });

  // Status can be changed by anyone
  const isStatusLocked = false;

  const defaultValues = initialData ? {
    ...initialData,
    invoiceDate: initialData.invoiceDate ? new Date(initialData.invoiceDate) : null,
    invoicePdfUrl: initialData.invoicePdfUrl || initialData.invoice_pdf_url || null,
    paymentReference: initialData.paymentReference || null,
    bankPaymentReference: initialData.bankPaymentReference || null,
    bankPaymentReferenceDate: initialData.bankPaymentReferenceDate ? new Date(initialData.bankPaymentReferenceDate) : null,
    invoiceNumber: initialData.invoiceNumber || null,
    paymentTerms: initialData.paymentTerms || 'NET_30',
    notes: initialData.notes || null,
    additionalCharge: Number(initialData.additionalCharge) || 0,
    tax: Number(initialData.tax) || 0,
    status: initialData.status || 'DRAFT',
    supplierId: initialData.supplier?.id || '',
    items: initialData.items?.map((item: any) => ({
      id: item.id || undefined,
      itemId: item.itemId || item.item?.id || '',
      quantity: Number(item.quantity) || 0, // Allow decimal quantities
      totalPrice: Number(item.total) || 0,
      unitPrice: Number(item.unitPrice) || 0,
      notes: item.notes || null,
    })) || [],
  } : {
    supplierId: '',
    paymentTerms: 'NET_30',
    invoiceNumber: null,
    invoiceDate: null,
    invoicePdfUrl: null,
    paymentReference: null,
    bankPaymentReference: null,
    bankPaymentReferenceDate: null,
    notes: null,
    additionalCharge: 0,
    tax: 0,
    status: 'PENDING',
    items: [],
  };

  const form = useForm<PurchaseOrderFormValues>({
    resolver: zodResolver(purchaseOrderFormSchema),
    defaultValues,
  });

  useEffect(() => {
    console.log('Initial Data:', initialData);
    console.log('Invoice PDF URL from initialData:', initialData?.invoicePdfUrl);

    if (initialData) {
      const pdfUrl = initialData.invoicePdfUrl || initialData.invoice_pdf_url;
      console.log('PDF URL to be set:', pdfUrl);

      if (pdfUrl) {
        console.log('Setting PDF URL:', pdfUrl);
        setPdfPreviewUrl(pdfUrl);
      }

      // Reset form with all initial data when it becomes available
      const formData = {
        supplierId: initialData.supplier?.id || '',
        paymentTerms: initialData.paymentTerms || 'NET_30',
        invoiceNumber: initialData.invoiceNumber || null,
        invoiceDate: initialData.invoiceDate ? new Date(initialData.invoiceDate) : null,
        invoicePdfUrl: pdfUrl || null,
        paymentReference: initialData.paymentReference || null,
        bankPaymentReference: initialData.bankPaymentReference || null,
        bankPaymentReferenceDate: initialData.bankPaymentReferenceDate ? new Date(initialData.bankPaymentReferenceDate) : null,
        notes: initialData.notes || null,
        additionalCharge: Number(initialData.additionalCharge) || 0,
        tax: Number(initialData.tax) || 0,
        status: initialData.status || 'DRAFT',
        items: initialData.items?.map((item: any) => ({
          id: item.id || undefined,
          itemId: item.itemId || item.item?.id || '',
          quantity: Number(item.quantity) || 0, // Allow decimal quantities
          totalPrice: Number(item.total) || 0,
          unitPrice: Number(item.unitPrice) || 0,
          notes: item.notes || null,
        })) || [],
      };

      console.log('Resetting form with data:', formData);
      console.log('Items being set:', formData.items);

      // Reset the entire form with new data
      form.reset(formData);

      // Force update each field individually to ensure they display correctly
      setTimeout(() => {
        formData.items.forEach((item: any, index: number) => {
          console.log(`Setting item ${index} - quantity: ${item.quantity}, unitPrice: ${item.unitPrice}, totalPrice: ${item.totalPrice}`);
          form.setValue(`items.${index}.quantity`, item.quantity);
          form.setValue(`items.${index}.unitPrice`, item.unitPrice);
          form.setValue(`items.${index}.totalPrice`, item.totalPrice);
          form.setValue(`items.${index}.itemId`, item.itemId);
          if (item.notes) {
            form.setValue(`items.${index}.notes`, item.notes);
          }
        });
      }, 100);
    }
  }, [initialData, form]);

  // Debounced search functions
  const debouncedSupplierSearch = useCallback(
    debounce((searchTerm: string) => {
      loadSuppliers(searchTerm);
    }, 300),
    []
  );

  const debouncedItemSearch = useCallback(
    debounce((searchTerm: string) => {
      loadItems(searchTerm);
    }, 300),
    []
  );

  useEffect(() => {
    loadSuppliers();
    loadItems();
  }, []);

  // Load suppliers and items when editing to ensure they're available for form display
  useEffect(() => {
    if (initialData && initialData.supplier) {
      // Ensure the current supplier is in the suppliers list
      const currentSupplier = suppliers.find(s => s.id === initialData.supplier.id);
      if (!currentSupplier && suppliers.length > 0) {
        // Add the current supplier to the list if it's not already there
        setSuppliers(prev => [...prev, {
          id: initialData.supplier.id,
          name: initialData.supplier.name,
          code: initialData.supplier.code,
          email: initialData.supplier.email
        }]);
      }
    }

    // Ensure items from existing purchase order are available
    if (initialData && initialData.items && initialData.items.length > 0) {
      initialData.items.forEach((orderItem: any) => {
        if (orderItem.item && orderItem.itemId) {
          const existingItem = items.find(item => item.id === orderItem.itemId);
          if (!existingItem) {
            // Add the item to the list if it's not already there
            setItems(prev => [...prev, {
              id: orderItem.itemId,
              name: orderItem.item.name || 'Unknown Item',
              description: orderItem.item.description || '',
              sku: orderItem.item.sku || '',
              currentStock: orderItem.item.currentStock || 0,
              unitMetric: orderItem.item.unitMetric,
              isActive: true,
              minQuantity: 0,
              unitMetricId: orderItem.item.unitMetricId || '',
              categoryId: orderItem.item.categoryId,
              category: orderItem.item.category,
              conversionRate: orderItem.item.conversionRate
            }]);
          }
        }
      });
    }
  }, [initialData, suppliers, items]);

  // Watch form values to debug quantity display issues
  const watchedItems = form.watch('items');
  useEffect(() => {
    if (initialData && watchedItems && watchedItems.length > 0) {
      console.log('Current form items values:', watchedItems);
      watchedItems.forEach((item, index) => {
        console.log(`Form item ${index}:`, {
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          totalPrice: item.totalPrice,
          itemId: item.itemId
        });
      });
    }
  }, [watchedItems, initialData]);

  // Reset search when popovers close
  useEffect(() => {
    if (!openSupplierCombobox) {
      setSupplierSearch('');
    }
  }, [openSupplierCombobox]);

  useEffect(() => {
    if (openItemCombobox === null) {
      setItemSearch('');
    }
  }, [openItemCombobox]);

  // Cleanup invoice validation on unmount
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, [cleanup]);

  // Simple debounce function
  function debounce(func: Function, wait: number) {
    let timeout: NodeJS.Timeout;
    return function executedFunction(...args: any[]) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  const loadSuppliers = async (searchTerm = '') => {
    try {
      setSuppliersLoading(true);
      const token = await getFirebaseToken();

      // Try with the correct endpoint first
      try {
        const response = await api.get('/api/suppliers', {
          params: {
            isActive: true,
            limit: 100,
            ...(searchTerm && { search: searchTerm }),
          },
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        console.log('Supplier response:', response.data);

        if (response.data && response.data.data) {
          const supplierData = response.data.data.items || response.data.data;
          if (Array.isArray(supplierData)) {
            setSuppliers(supplierData);
          } else {
            console.error('Invalid supplier data format:', supplierData);
            toast.error('Invalid supplier data format received');
          }
        } else {
          console.error('Invalid response format:', response.data);
          toast.error('Invalid response format from server');
        }
      } catch (apiError: any) {
        console.log('API error, trying alternative endpoint:', apiError.message);

        // Try alternative endpoint
        try {
          const altResponse = await api.get('/api/inventory/suppliers', {
            params: {
              isActive: true,
              limit: 100,
              ...(searchTerm && { search: searchTerm }),
            },
            headers: {
              Authorization: `Bearer ${token}`,
            },
          });

          if (altResponse.data && altResponse.data.data) {
            const supplierData = altResponse.data.data.items || altResponse.data.data;
            if (Array.isArray(supplierData)) {
              setSuppliers(supplierData);
            } else {
              console.error('Invalid supplier data format from alt endpoint:', supplierData);
              toast.error('Invalid supplier data format received');
            }
          }
        } catch (altError) {
          console.error('All supplier API endpoints failed:', altError);
          toast.error('Failed to load suppliers');
        }
      }
    } catch (error: any) {
      console.error('Error loading suppliers:', error);
      toast.error(error.response?.data?.message || 'Failed to load suppliers');
    } finally {
      setSuppliersLoading(false);
    }
  };

  const loadItems = async (searchTerm = '', retryCount = 0) => {
    try {
      setItemsLoading(true);
      console.log('Starting to load items with search:', searchTerm, 'retry:', retryCount);

      const token = await getFirebaseToken();
      if (!token) {
        throw new Error('Authentication token not available');
      }

      const response = await api.get('/api/inventory-items', {
        params: {
          isActive: true,
          limit: 100,
          ...(searchTerm && { search: searchTerm }),
        },
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      console.log('Raw API response:', response.data);

      // Handle different possible response formats
      let inventoryItems: any[] = [];

      if (response.data?.success && response.data?.data?.items) {
        inventoryItems = response.data.data.items;
      } else if (response.data?.data?.items) {
        inventoryItems = response.data.data.items;
      } else if (response.data?.items) {
        inventoryItems = response.data.items;
      } else if (Array.isArray(response.data?.data)) {
        inventoryItems = response.data.data;
      } else if (Array.isArray(response.data)) {
        inventoryItems = response.data;
      }

      if (inventoryItems.length > 0 || searchTerm) {
        console.log('Setting inventory items:', inventoryItems);
        setItems(inventoryItems);
      } else if (retryCount < 2) {
        // Retry loading if no items found and no search term (initial load)
        console.log('No items found, retrying...');
        setTimeout(() => loadItems(searchTerm, retryCount + 1), 1000);
        return;
      } else {
        console.warn('No inventory items found after retries');
        setItems([]);
        if (!searchTerm) {
          toast.error('No inventory items found. Please check if items exist and are active.');
        }
      }
    } catch (error: any) {
      console.error('Error loading items:', error);

      // Retry on network errors
      if (retryCount < 2 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
        console.log('Network error, retrying...');
        setTimeout(() => loadItems(searchTerm, retryCount + 1), 2000);
        return;
      }

      setItems([]);
      toast.error(error.response?.data?.message || 'Failed to load inventory items');
    } finally {
      setItemsLoading(false);
    }
  };

  const onSubmit = async (data: PurchaseOrderFormValues) => {
    try {
      setLoading(true);
      toast.loading('Processing purchase order...');
      console.log('Form submission started with data:', data);

      // Additional validation before submission
      if (!data.items || data.items.length === 0) {
        toast.dismiss();
        toast.error('Please add at least one item to the purchase order');
        setLoading(false);
        return;
      }

      // Validate each item has required fields
      for (let i = 0; i < data.items.length; i++) {
        const item = data.items[i];
        if (!item.itemId) {
          toast.dismiss();
          toast.error(`Item ${i + 1}: Please select an item`);
          setLoading(false);
          return;
        }
        if (!item.quantity || item.quantity <= 0) {
          toast.dismiss();
          toast.error(`Item ${i + 1}: Please enter a valid quantity`);
          setLoading(false);
          return;
        }
        if (item.unitPrice === undefined || item.unitPrice < 0) {
          toast.dismiss();
          toast.error(`Item ${i + 1}: Please enter a valid unit price`);
          setLoading(false);
          return;
        }
      }

      // Check invoice number validation before submission
      if (!validationState.isUnique) {
        toast.dismiss();
        toast.error('Please resolve the duplicate invoice number before saving.');
        setLoading(false);
        return;
      }

      // If validation is still in progress, wait for it to complete
      if (validationState.isChecking) {
        toast.dismiss();
        toast.error('Please wait for invoice number validation to complete.');
        setLoading(false);
        return;
      }

      const token = await getFirebaseToken();
      if (!token) {
        throw new Error('Failed to get authentication token');
      }
      
      // Ensure we have the correct PDF URL (from form data or state)
      const pdfUrl = data.invoicePdfUrl || pdfPreviewUrl;
      console.log('PDF URL from form:', data.invoicePdfUrl);
      console.log('PDF URL from state:', pdfPreviewUrl);
      console.log('Using PDF URL:', pdfUrl);
      
      // Create a separate object for API submission with properly formatted dates
      // This avoids TypeScript errors with the form values
      
      // Format dates for API submission (as strings)
      let formattedInvoiceDate: string | null = null;
      let formattedBankDate: string | null = null;
      
      // Handle invoice date
      if (data.invoiceDate) {
        try {
          const invoiceDate = data.invoiceDate instanceof Date ? 
            data.invoiceDate : new Date(data.invoiceDate as any);
          formattedInvoiceDate = format(invoiceDate, 'yyyy-MM-dd');
          console.log('Formatted invoice date:', formattedInvoiceDate);
        } catch (error) {
          console.error('Error formatting invoice date:', error);
        }
      }
      
      // Handle bank payment reference date
      if (data.bankPaymentReferenceDate) {
        try {
          const bankDate = data.bankPaymentReferenceDate;
          if (bankDate instanceof Date) {
            formattedBankDate = format(bankDate, 'yyyy-MM-dd');
          } else if (typeof bankDate === 'string') {
            // If it's already a string, parse it to ensure it's valid
            const parsedDate = new Date(bankDate);
            formattedBankDate = format(parsedDate, 'yyyy-MM-dd');
          }
          console.log('Formatted bank date:', formattedBankDate);
        } catch (error) {
          console.error('Error formatting bank payment reference date:', error);
        }
      }
      
      // Prepare the final payload with properly formatted data
      const payload = {
        ...data,
        // Use the formatted date strings for API submission
        invoiceDate: formattedInvoiceDate,
        bankPaymentReferenceDate: formattedBankDate,
        invoicePdfUrl: pdfUrl,
        items: data.items.map((item, index) => {
          const processedItem = {
            ...item,
            quantity: typeof item.quantity === 'string' ? parseFloat(item.quantity) : item.quantity,
            unitPrice: typeof item.unitPrice === 'string' ? parseFloat(item.unitPrice) : item.unitPrice,
            totalPrice: typeof item.totalPrice === 'string' ? parseFloat(item.totalPrice) : item.totalPrice,
          };
          console.log(`Processing item ${index} for submission:`, {
            original: item,
            processed: processedItem
          });
          return processedItem;
        }),
        tax: typeof data.tax === 'string' ? parseFloat(data.tax) : data.tax,
        additionalCharge: typeof data.additionalCharge === 'string' ? parseFloat(data.additionalCharge) : data.additionalCharge,
      };
      
      console.log('Final payload:', payload);

      console.log('Submitting payload:', payload);

      let response: any;
      try {
        if (initialData) {
          console.log(`Updating purchase order ${initialData.id}`);
          response = await api.put(`/api/purchase-orders/${initialData.id}`, payload, {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          });
          console.log('Update response:', response.data);
          toast.dismiss(); // Dismiss the loading toast
          toast.success('Purchase order updated successfully');
        } else {
          console.log('Creating new purchase order');
          response = await api.post('/api/purchase-orders', payload, {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          });
          console.log('Create response:', response.data);
          toast.dismiss(); // Dismiss the loading toast
          toast.success('Purchase order created successfully');
        }

        // Use the new navigation handler
        const orderId = response.data.data.id;
        console.log(`Redirecting to purchase order ${orderId}`);
        handleSuccessNavigation(orderId, !!initialData);
      } catch (apiError: any) {
        console.error('API error:', apiError);
        toast.dismiss(); // Dismiss the loading toast

        // Enhanced error handling with specific messages
        let errorMessage = initialData ? 'Failed to update purchase order' : 'Failed to create purchase order';

        if (apiError.response?.data?.message) {
          errorMessage = apiError.response.data.message;
        } else if (apiError.response?.data?.errors) {
          const errors = apiError.response.data.errors;
          const errorMessages = Object.values(errors).filter(Boolean);
          if (errorMessages.length > 0) {
            errorMessage = errorMessages.join(', ');
          }
        }

        toast.error(errorMessage);
      }
      router.refresh();
    } catch (error: any) {
      console.error('Error saving purchase order:', error);
      toast.error(error.response?.data?.message || 'Failed to save purchase order');
    } finally {
      setLoading(false);
    }
  };

  const handleItemChange = (itemId: string, index: number) => {
    const item = items.find(i => i.id === itemId);
    if (item) {
      const currentItems = form.getValues('items');
      // Update the item at the specified index
      if (currentItems && currentItems[index]) {
        // Keep existing values but update the itemId
        currentItems[index] = {
          ...currentItems[index],
          itemId: itemId
          // Don't set unitMetric as it's not in the type definition
        };
        form.setValue('items', currentItems);
      }
      console.log(`Selected item: ${item.name} (${item.id})`);
    }
  };

  const handleDownloadPdf = async () => {
    try {
      const token = await getFirebaseToken();
      // Use token in query parameter instead of header for blob download
      const response = await api.get(`/api/purchase-orders/${id}/pdf?token=${token}`, {
        responseType: 'blob',
      });
      
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `PO-${orderNumber}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading PDF:', error);
      toast.error('Failed to download PDF');
    }
  };

  const handleCancel = () => {
    router.back();
  };

  const handleSuccessNavigation = (orderId: string, isUpdate: boolean) => {
    if (isUpdate) {
      // For updates, navigate to the return URL (preserving pagination state)
      router.push(returnUrl);
    } else {
      // For new orders, go to the detail view
      router.push(`/inventory/purchase-orders/${orderId}`);
    }
  };

  return (
    <div className="flex-1 space-y-4">
      <div className="flex items-center justify-between">
        <Heading
          title={initialData ? 'Edit Purchase Order' : 'Create Purchase Order'}
          description={initialData ? 'Update purchase order details' : 'Create a new purchase order'}
        />
      </div>
      <Separator />

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          {/* Form Fields Grid - Full width layout */}
          <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
            <FormField
              control={form.control}
              name="supplierId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Supplier</FormLabel>
                  <Popover
                    open={openSupplierCombobox}
                    onOpenChange={setOpenSupplierCombobox}
                  >
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          role="combobox"
                          aria-expanded={openSupplierCombobox}
                          className={cn(
                            "w-full justify-start h-10",
                            !field.value && "text-muted-foreground"
                          )}
                          disabled={loading}
                        >
                          {field.value
                            ? suppliers.find((supplier) => supplier.id === field.value)?.name
                            : "Select supplier..."}
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent
                      className="w-full min-w-[300px] p-0"
                      align="start"
                      side="bottom"
                      sideOffset={4}
                    >
                      <Command shouldFilter={false}>
                        <div className="flex items-center border-b px-3">
                          <CommandInput
                            placeholder="Search suppliers..."
                            className="h-9 border-0 focus:ring-0"
                            value={supplierSearch}
                            onValueChange={(value) => {
                              setSupplierSearch(value);
                              // Use debounced search for better performance
                              if (value.length >= 2) {
                                debouncedSupplierSearch(value);
                              } else if (value.length === 0) {
                                loadSuppliers();
                              }
                            }}
                          />
                          {supplierSearch && (
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0"
                              onClick={() => {
                                setSupplierSearch('');
                                loadSuppliers();
                              }}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          )}
                        </div>
                        <CommandEmpty>
                          {suppliersLoading ? 'Searching suppliers...' :
                           supplierSearch.length >= 2 ? 'No suppliers found.' : 'Type to search suppliers...'}
                        </CommandEmpty>
                        <CommandGroup className="max-h-60 overflow-y-auto">
                          {!suppliersLoading && suppliers.length > 0 && supplierSearch && (
                            <div className="px-2 py-1 text-xs text-muted-foreground border-b">
                              {suppliers.length} supplier{suppliers.length !== 1 ? 's' : ''} found
                            </div>
                          )}
                          {suppliersLoading ? (
                            <div className="flex items-center justify-center py-4">
                              <Loader2 className="h-4 w-4 animate-spin" />
                              <span className="ml-2 text-sm text-muted-foreground">Loading suppliers...</span>
                            </div>
                          ) : suppliers.map((supplier) => (
                              <CommandItem
                                key={supplier.id}
                                value={supplier.id}
                                onSelect={(currentValue) => {
                                  console.log('Selected supplier ID:', currentValue);
                                  field.onChange(currentValue === field.value ? "" : currentValue);
                                  setOpenSupplierCombobox(false);
                                  setSupplierSearch('');
                                }}
                                className="cursor-pointer"
                              >
                                <Check
                                  className={cn(
                                    "mr-2 h-4 w-4",
                                    field.value === supplier.id ? "opacity-100" : "opacity-0"
                                  )}
                                />
                                <div className="flex flex-col flex-1">
                                  <span className="font-medium">{supplier.name}</span>
                                  <span className="text-xs text-muted-foreground">
                                    Code: {supplier.code}
                                    {supplier.email && ` • ${supplier.email}`}
                                  </span>
                                </div>
                              </CommandItem>
                            ))}
                        </CommandGroup>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="invoiceNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Invoice Number</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        placeholder="Enter invoice number"
                        className={cn(
                          "w-full pr-10",
                          !validationState.isUnique && "border-red-500 focus-visible:ring-red-500"
                        )}
                        {...field}
                        value={field.value ?? ''}
                        onChange={(e) => {
                          const value = e.target.value || null;
                          field.onChange(value);
                          validateInvoiceNumber(value || '');
                        }}
                      />
                      {validationState.isChecking && (
                        <Loader2 className="absolute right-3 top-3 h-4 w-4 animate-spin text-muted-foreground" />
                      )}
                      {!validationState.isChecking && field.value && validationState.isUnique && (
                        <CheckCircle className="absolute right-3 top-3 h-4 w-4 text-green-500" />
                      )}
                      {!validationState.isChecking && !validationState.isUnique && (
                        <XCircle className="absolute right-3 top-3 h-4 w-4 text-red-500" />
                      )}
                    </div>
                  </FormControl>
                  {!validationState.isUnique && validationState.existingOrder && (
                    <div className="text-sm text-red-600 mt-1">
                      Invoice number already used in{' '}
                      <Button
                        variant="link"
                        className="p-0 h-auto text-red-600 underline text-sm"
                        onClick={() => router.push(`/inventory/purchase-orders/${validationState.existingOrder?.id}`)}
                        type="button"
                      >
                        {validationState.existingOrder.orderNumber}
                      </Button>
                      {' '}({validationState.existingOrder.supplierName})
                    </div>
                  )}
                  {validationState.error && (
                    <div className="text-sm text-red-600 mt-1">
                      {validationState.error}
                    </div>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="invoiceDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Invoice Date</FormLabel>
                  <FormControl>
                    <div className="w-full [&_.react-datepicker-wrapper]:w-full [&_.react-datepicker-wrapper]:block">
                      <DatePicker
                        selected={field.value}
                        onChange={(date: Date | null) => field.onChange(date)}
                        dateFormat="MMMM d, yyyy"
                        isClearable
                        showMonthDropdown
                        showYearDropdown
                        dropdownMode="select"
                        className="!w-full !block h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        placeholderText="Select invoice date"
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="paymentReference"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Payment Reference</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter payment reference"
                      className="w-full"
                      {...field}
                      value={field.value || ''}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="bankPaymentReference"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Bank Payment Ref</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter bank payment reference"
                      className="w-full"
                      {...field}
                      value={field.value || ''}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="bankPaymentReferenceDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Bank Payment Ref Date</FormLabel>
                  <FormControl>
                    <div className="w-full [&_.react-datepicker-wrapper]:w-full [&_.react-datepicker-wrapper]:block">
                      <DatePicker
                        selected={field.value instanceof Date ? field.value : null}
                        onChange={(date: Date | null) => field.onChange(date)}
                        dateFormat="MMMM d, yyyy"
                        isClearable
                        showMonthDropdown
                        showYearDropdown
                        dropdownMode="select"
                        className="!w-full !block h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        placeholderText="Select bank payment reference date"
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="paymentTerms"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Payment Terms</FormLabel>
                  <FormControl>
                    <Select
                      disabled={loading}
                      onValueChange={field.onChange}
                      value={field.value}
                      defaultValue={field.value}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select payment terms" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="CREDIT">Credit</SelectItem>
                        <SelectItem value="PAID_IN_ADVANCE">Paid in Advance</SelectItem>
                        <SelectItem value="NET_15">Net 15</SelectItem>
                        <SelectItem value="NET_30">Net 30</SelectItem>
                        <SelectItem value="NET_45">Net 45</SelectItem>
                        <SelectItem value="NET_60">Net 60</SelectItem>
                        <SelectItem value="COD">Cash on Delivery</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Status</FormLabel>
                  <FormControl>
                    <Select
                      disabled={isStatusLocked}
                      onValueChange={field.onChange}
                      value={field.value}
                      defaultValue={field.value}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue defaultValue={field.value} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="DRAFT">Draft</SelectItem>
                        <SelectItem value="PENDING">Pending</SelectItem>
                        <SelectItem value="APPROVED">Approved</SelectItem>
                        <SelectItem value="COMPLETED">Completed</SelectItem>
                        <SelectItem value="CANCELLED">Cancelled</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                  {isStatusLocked && (
                    <p className="text-sm text-muted-foreground mt-2">
                      Status is currently locked
                    </p>
                  )}
                </FormItem>
              )}
            />
          </div>

          <div>
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Items</h3>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => {
                  const currentItems = form.getValues('items');
                  form.setValue('items', [
                    ...currentItems,
                    { itemId: '', quantity: 1.0, totalPrice: 0, unitPrice: 0 },
                  ]);
                }}
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Item
              </Button>
            </div>
            <Separator className="my-4" />
            
            {form.watch('items')?.map((_, index) => (
              <div key={index} className="mb-4 grid grid-cols-1 gap-4 rounded-lg border p-4 sm:grid-cols-2 lg:grid-cols-6">
                <FormField
                  control={form.control}
                  name={`items.${index}.itemId`}
                  render={({ field }) => (
                    <FormItem className="sm:col-span-2 lg:col-span-2">
                      <FormLabel>Item</FormLabel>
                      <Popover
                        open={openItemCombobox === index}
                        onOpenChange={(open) => setOpenItemCombobox(open ? index : null)}
                      >
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              role="combobox"
                              aria-expanded={openItemCombobox === index}
                              className={cn(
                                "w-full justify-start h-10",
                                !field.value && "text-muted-foreground"
                              )}
                              disabled={loading}
                            >
                              {field.value
                                ? items.find((item) => item.id === field.value)?.name
                                : "Select item..."}
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent
                          className="w-full min-w-[350px] p-0"
                          align="start"
                          side="bottom"
                          sideOffset={4}
                        >
                          <Command shouldFilter={false}>
                            <div className="flex items-center border-b px-3">
                              <CommandInput
                                placeholder="Search items..."
                                className="h-9 border-0 focus:ring-0"
                                value={itemSearch}
                                onValueChange={(value) => {
                                  setItemSearch(value);
                                  // Use debounced search for better performance
                                  if (value.length >= 2) {
                                    debouncedItemSearch(value);
                                  } else if (value.length === 0) {
                                    loadItems();
                                  }
                                }}
                              />
                              {itemSearch && (
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  className="h-6 w-6 p-0"
                                  onClick={() => {
                                    setItemSearch('');
                                    loadItems();
                                  }}
                                >
                                  <X className="h-3 w-3" />
                                </Button>
                              )}
                            </div>
                            <CommandEmpty>
                              {itemsLoading ? 'Searching items...' :
                               itemSearch.length >= 2 ? 'No items found.' : 'Type to search items...'}
                            </CommandEmpty>
                            <CommandGroup className="max-h-60 overflow-y-auto">
                              {!itemsLoading && items.length > 0 && itemSearch && (
                                <div className="px-2 py-1 text-xs text-muted-foreground border-b">
                                  {items.length} item{items.length !== 1 ? 's' : ''} found
                                </div>
                              )}
                              {itemsLoading ? (
                                <div className="flex items-center justify-center py-4">
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                  <span className="ml-2 text-sm text-muted-foreground">Loading items...</span>
                                </div>
                              ) : items.map((item) => (
                                  <CommandItem
                                    key={item.id}
                                    value={item.id}
                                    onSelect={(currentValue) => {
                                      field.onChange(currentValue === field.value ? "" : currentValue);
                                      handleItemChange(currentValue, index);
                                      setOpenItemCombobox(null);
                                      setItemSearch('');
                                    }}
                                    className="cursor-pointer"
                                  >
                                    <Check
                                      className={cn(
                                        "mr-2 h-4 w-4",
                                        field.value === item.id ? "opacity-100" : "opacity-0"
                                      )}
                                    />
                                    <div className="flex flex-col flex-1">
                                      <span className="font-medium">{item.name}</span>
                                      <span className="text-xs text-muted-foreground">
                                        SKU: {item.sku || 'No SKU'} • Stock: {item.currentStock} {item.unitMetric?.displayName || 'units'}
                                      </span>
                                    </div>
                                  </CommandItem>
                                ))}
                            </CommandGroup>
                          </Command>
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name={`items.${index}.quantity`}
                  render={({ field }) => (
                    <FormItem className="lg:col-span-1">
                      <FormLabel>Quantity</FormLabel>
                      <div className="flex items-center gap-2">
                        <FormControl>
                          <Input
                            type="text"
                            inputMode="decimal"
                            className="w-full"
                            value={(() => {
                              const val = field.value;
                              console.log(`Quantity field value for item ${index}:`, val, typeof val);
                              return val !== undefined && val !== null && val !== 0 && String(val) !== '' ? String(val) : '';
                            })()}
                            onChange={(e) => {
                              // Allow numbers and decimal points for quantity
                              const value = e.target.value.replace(/[^0-9.]/g, '');

                              // Handle empty input
                              if (!value) {
                                field.onChange('');
                                return;
                              }

                              // Validate decimal format (allow numbers with optional decimal point)
                              if (/^\d*\.?\d*$/.test(value)) {
                                field.onChange(value);

                                // Update total price when quantity changes
                                const newQuantity = parseFloat(value) || 0;
                                const unitPriceValue = form.getValues(`items.${index}.unitPrice`);
                                const unitPrice = typeof unitPriceValue === 'string' ? parseFloat(unitPriceValue) || 0 : unitPriceValue || 0;
                                const newTotalPrice = newQuantity * unitPrice;
                                // Convert to string for display but will be transformed to number by schema
                                form.setValue(`items.${index}.totalPrice`, newTotalPrice.toFixed(2) as any);
                              }
                            }}
                            onBlur={(e) => {
                              // Format the value on blur (ensure it's a valid decimal)
                              if (e.target.value) {
                                const formattedValue = parseFloat(e.target.value);
                                if (!isNaN(formattedValue)) {
                                  field.onChange(formattedValue);
                                }
                              }
                            }}
                          />
                        </FormControl>
                        <span className="text-sm text-gray-500 hidden sm:block whitespace-nowrap">
                          {items.find(item => item.id === form.getValues(`items.${index}.itemId`))?.unitMetric?.displayName || 'units'}
                        </span>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name={`items.${index}.unitPrice`}
                  render={({ field }) => (
                    <FormItem className="lg:col-span-1">
                      <FormLabel>Unit Price</FormLabel>
                      <div className="flex items-center gap-2">
                        <FormControl>
                          <Input
                            type="text"
                            inputMode="decimal"
                            className="w-full"
                            value={field.value !== undefined && field.value !== null ? String(field.value) : ''}
                            onChange={(e) => {
                              // Allow only numbers and decimal points
                              const value = e.target.value.replace(/[^0-9.]/g, '');
                              
                              // Handle empty input
                              if (!value) {
                                field.onChange('');
                                return;
                              }
                              
                              // Validate decimal format
                              if (/^\d*\.?\d*$/.test(value)) {
                                field.onChange(value);
                                
                                // Update total price when unit price changes
                                const newUnitPrice = parseFloat(value) || 0;
                                const quantityValue = form.getValues(`items.${index}.quantity`);
                                const quantity = typeof quantityValue === 'string' ? parseFloat(quantityValue) || 0 : quantityValue || 0;
                                const newTotalPrice = quantity * newUnitPrice;
                                // Convert to string for display but will be transformed to number by schema
                                form.setValue(`items.${index}.totalPrice`, newTotalPrice.toFixed(2) as any);
                              }
                            }}
                            onBlur={(e) => {
                              // Format the value on blur
                              if (e.target.value) {
                                const formattedValue = parseFloat(e.target.value);
                                if (!isNaN(formattedValue)) {
                                  field.onChange(formattedValue.toString());
                                }
                              }
                            }}
                          />
                        </FormControl>
                        <span className="text-sm text-gray-500 hidden sm:block whitespace-nowrap">
                          AED
                        </span>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name={`items.${index}.totalPrice`}
                  render={({ field }) => (
                    <FormItem className="lg:col-span-1">
                      <FormLabel>Total Price</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          inputMode="decimal"
                          className="w-full"
                          value={field.value?.toString() || ''}
                          onChange={(e) => {
                            // Allow only numbers and decimal points
                            const value = e.target.value.replace(/[^0-9.]/g, '');
                            
                            // Handle empty input
                            if (!value) {
                              field.onChange('');
                              return;
                            }
                            
                            // Validate decimal format
                            if (/^\d*\.?\d*$/.test(value)) {
                              field.onChange(value);
                              
                              // Calculate unit price when total price changes
                              const totalPrice = parseFloat(value) || 0;
                              const quantityValue = form.getValues(`items.${index}.quantity`);
                              const quantity = typeof quantityValue === 'string' ? parseFloat(quantityValue) || 0 : quantityValue || 0;
                              if (quantity > 0) {
                                const unitPrice = totalPrice / quantity;
                                // Convert to string for display but will be transformed to number by schema
                                form.setValue(`items.${index}.unitPrice`, unitPrice.toFixed(2) as any);
                              }
                            }
                          }}
                          onBlur={(e) => {
                            // Format the value on blur
                            if (e.target.value) {
                              const formattedValue = parseFloat(e.target.value);
                              if (!isNaN(formattedValue)) {
                                field.onChange(formattedValue.toString());
                              }
                            }
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex items-end lg:col-span-1">
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    className="w-full h-10"
                    onClick={() => {
                      const currentItems = form.getValues('items');
                      form.setValue('items', currentItems.filter((_, i) => i !== index));
                    }}
                  >
                    <Trash className="h-4 w-4 sm:mr-2" />
                    <span className="hidden sm:inline">Remove</span>
                  </Button>
                </div>
              </div>
            ))}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* PDF Invoice Upload */}
            <div className="space-y-2">
              <FormLabel>Invoice PDF</FormLabel>
              <div className="flex flex-col space-y-2">
                {(pdfPreviewUrl || form.getValues('invoicePdfUrl')) && (
                  <div className="flex items-center gap-2 p-2 border rounded bg-gray-50">
                    <a 
                      href={pdfPreviewUrl || form.getValues('invoicePdfUrl')} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1"
                    >
                      <Download className="h-4 w-4" />
                      View/Download Invoice PDF
                    </a>
                    <button
                      type="button"
                      onClick={() => {
                        setPdfPreviewUrl(null);
                        form.setValue('invoicePdfUrl', null);
                      }}
                      className="ml-auto text-gray-500 hover:text-gray-700"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                )}
                <Input
                  type="file"
                  accept=".pdf"
                  onChange={async (e) => {
                    const file = e.target.files?.[0];
                    if (!file) return;
                    
                    try {
                      // Show loading state
                      toast.loading('Uploading invoice PDF...');
                      
                      // Create FormData for file upload
                      const formData = new FormData();
                      formData.append('file', file);
                      
                      // If we have an existing order ID (for updates)
                      if (initialData?.id) {
                        formData.append('purchaseOrderId', initialData.id);
                      }
                      
                      // Upload the PDF
                      const token = await getFirebaseToken();
                      const uploadResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/purchase-orders/upload-invoice`, {
                        method: 'POST',
                        headers: {
                          'Authorization': `Bearer ${token}`
                        },
                        body: formData
                      });
                      
                      if (!uploadResponse.ok) {
                        throw new Error('Failed to upload invoice PDF');
                      }
                      
                      const uploadResult = await uploadResponse.json();
                      
                      // Update the form with the PDF URL
                      form.setValue('invoicePdfUrl', uploadResult.fileUrl);
                      setPdfPreviewUrl(uploadResult.fileUrl);
                      
                      toast.dismiss();
                      toast.success('Invoice PDF uploaded successfully');
                    } catch (error) {
                      console.error('Error uploading PDF:', error);
                      toast.dismiss();
                      toast.error('Failed to upload invoice PDF');
                    }
                  }}
                  className="w-full"
                />
                <p className="text-xs text-gray-500">Upload supplier's invoice (PDF only, max 5MB)</p>
              </div>
            </div>
            
            {/* Notes Field */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Add any additional notes..."
                      className="resize-none h-full min-h-[120px]"
                      {...field}
                      value={field.value || ''}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Tax and Additional Charges */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="tax"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tax (AED)</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      inputMode="decimal"
                      placeholder="0.00"
                      value={field.value?.toString() || ''}
                      onChange={(e) => {
                        // Allow only numbers and decimal points
                        const value = e.target.value.replace(/[^0-9.]/g, '');

                        // Handle empty input
                        if (!value) {
                          field.onChange('');
                          return;
                        }

                        // Validate decimal format (allow numbers with optional decimal point)
                        if (/^\d*\.?\d*$/.test(value)) {
                          field.onChange(value);
                        }
                      }}
                      onBlur={(e) => {
                        // Format the value on blur
                        if (e.target.value) {
                          const formattedValue = parseFloat(e.target.value);
                          if (!isNaN(formattedValue)) {
                            field.onChange(formattedValue.toString());
                          }
                        }
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="additionalCharge"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Additional Charge (AED)</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      inputMode="decimal"
                      placeholder="0.00"
                      value={field.value?.toString() || ''}
                      onChange={(e) => {
                        // Allow only numbers and decimal points
                        const value = e.target.value.replace(/[^0-9.]/g, '');

                        // Handle empty input
                        if (!value) {
                          field.onChange('');
                          return;
                        }

                        // Validate decimal format (allow numbers with optional decimal point)
                        if (/^\d*\.?\d*$/.test(value)) {
                          field.onChange(value);
                        }
                      }}
                      onBlur={(e) => {
                        // Format the value on blur
                        if (e.target.value) {
                          const formattedValue = parseFloat(e.target.value);
                          if (!isNaN(formattedValue)) {
                            field.onChange(formattedValue.toString());
                          }
                        }
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Order Summary */}
          <div className="mt-6 border rounded-md p-4">
            <h3 className="text-lg font-medium mb-4">Order Summary</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Subtotal:</span>
                <span>
                  AED {form.watch('items').reduce((total, item) => {
                    const totalPrice = Number(item.totalPrice) || 0;
                    return total + totalPrice;
                  }, 0).toFixed(2)}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Tax:</span>
                <span>AED {Number(form.watch('tax') || 0).toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span>Additional Charge:</span>
                <span>AED {Number(form.watch('additionalCharge') || 0).toFixed(2)}</span>
              </div>
              <div className="flex justify-between font-bold pt-2 border-t">
                <span>Total:</span>
                <span>
                  AED {(
                    form.watch('items').reduce((total, item) => {
                      const totalPrice = Number(item.totalPrice) || 0;
                      return total + totalPrice;
                    }, 0) +
                    Number(form.watch('tax') || 0) +
                    Number(form.watch('additionalCharge') || 0)
                  ).toFixed(2)}
                </span>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row justify-end gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={loading}
              className="w-full sm:w-auto"
            >
              Cancel
            </Button>
            {id && (
              <Button
                type="button"
                variant="outline"
                onClick={handleDownloadPdf}
                className="w-full sm:w-auto"
              >
                <Download className="mr-2 h-4 w-4" />
                Download PDF
              </Button>
            )}
            <Button type="submit" disabled={loading} className="w-full sm:w-auto">
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {initialData ? 'Update' : 'Create'} Purchase Order
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
