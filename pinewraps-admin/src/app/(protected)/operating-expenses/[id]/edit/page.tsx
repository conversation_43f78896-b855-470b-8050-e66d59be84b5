'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import ExpenseForm from '@/components/operating-expenses/expense-form';
import api from '@/lib/api';
import { getFirebaseToken } from '@/lib/firebase';

interface Company {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
}

interface ExpenseCategory {
  id: string;
  name: string;
  description?: string;
}

interface OperatingExpense {
  id: string;
  invoiceNumber: string;
  companyName: string;
  amount: number;
  currency: string;
  invoiceDate: string;
  dueDate?: string;
  categoryId: string;
  companyId: string;
  description?: string;
  bankReference?: string;
  attachments: string[];
  status: string;
  category: ExpenseCategory;
  company: Company;
}

export default function EditExpensePage() {
  const params = useParams();
  const router = useRouter();
  const [expense, setExpense] = useState<OperatingExpense | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (params.id) {
      fetchExpense(params.id as string);
    }
  }, [params.id]);

  const fetchExpense = async (id: string) => {
    try {
      const token = await getFirebaseToken();
      const response = await api.get(`/api/operating-expenses/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      
      setExpense(response.data.data);
    } catch (error) {
      console.error('Error fetching expense:', error);
      alert('Failed to load expense details');
      router.push('/operating-expenses');
    } finally {
      setLoading(false);
    }
  };

  const handleSuccess = (updatedExpense: OperatingExpense) => {
    router.push(`/operating-expenses/${updatedExpense.id}`);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (!expense) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Expense not found</p>
      </div>
    );
  }

  return (
    <ExpenseForm 
      expense={expense} 
      onSuccess={handleSuccess}
    />
  );
}
