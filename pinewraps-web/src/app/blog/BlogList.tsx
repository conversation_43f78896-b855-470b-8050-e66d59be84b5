'use client';

import type { BlogPost } from '@/types/blog';
import Link from 'next/link';
import Image from 'next/image';
import { format } from 'date-fns';
import { useState } from 'react';
import { BlogService } from '@/services/blog.service';

interface BlogListProps {
  initialPosts: BlogPost[];
  initialPage?: number;
  initialPagination?: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}

export default function BlogList({ initialPosts, initialPage = 1, initialPagination }: BlogListProps) {
  const [posts, setPosts] = useState<BlogPost[]>(initialPosts);
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [loading, setLoading] = useState(false);
  
  // Initialize hasMore based on pagination metadata if available, otherwise fallback to post count
  const [hasMore, setHasMore] = useState(() => {
    if (initialPagination) {
      return initialPagination.page < initialPagination.pages;
    }
    // Fallback: assume more posts if we got exactly 12 posts
    return initialPosts.length === 12;
  });
  if (!posts?.length) {
    return (
      <div className="text-center mt-12">
        <p className="text-lg text-gray-500">No blog posts found.</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-7xl mx-auto py-8 px-4 sm:py-12 sm:px-6 lg:px-8">
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {posts.map((post: BlogPost) => (
            <Link
              key={post.id}
              href={`/blog/${post.slug}`}
              className="flex flex-col rounded-lg shadow-lg overflow-hidden transition duration-300 hover:shadow-xl"
            >
              <div className="flex-shrink-0">
                <div className="h-48 w-full relative">
                  <Image
                    src={post.featuredImage || '/images/placeholder.png'}
                    alt={post.title || 'Blog post featured image'}
                    fill
                    className="object-cover"
                    unoptimized
                    sizes="(max-width: 1024px) 100vw, 1024px"
                  />
                </div>
              </div>
              <div className="flex-1 bg-white p-6 flex flex-col justify-between">
                <div className="flex-1">
                  <div className="flex space-x-2">
                    {post.categories?.map((category) => (
                      <span
                        key={category.id}
                        className="inline-flex items-center px-3 py-0.5 rounded-full text-sm font-medium bg-gray-100 text-gray-800"
                      >
                        {category.name}
                      </span>
                    ))}
                  </div>
                  <div className="block mt-2">
                    <p className="text-xl font-semibold text-gray-900">{post.title}</p>
                    <p className="mt-3 text-base text-gray-500 line-clamp-3">{post.excerpt}</p>
                  </div>
                </div>
                <div className="mt-6">
                  <div className="text-sm text-gray-500">
                    {format(new Date(post.publishedAt), 'MMMM d, yyyy')}
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>

        {hasMore && (
          <div className="mt-8 text-center">
            <button
              onClick={async () => {
                try {
                  setLoading(true);
                  const nextPage = currentPage + 1;
                  const response = await BlogService.getAllPosts(nextPage, 12);
                  
                  if (response.data && response.data.length > 0) {
                    const newPosts = [...posts, ...response.data];
                    setPosts(newPosts);
                    setCurrentPage(nextPage);
                    
                    // Check if there are more pages based on pagination info
                    if (response.meta) {
                      // Use pagination metadata for accurate determination
                      setHasMore(nextPage < response.meta.pages);
                    } else {
                      // Fallback: assume no more if less than 12 posts returned
                      setHasMore(response.data.length === 12);
                    }
                  } else {
                    // No more posts available
                    setHasMore(false);
                  }
                } catch (error) {
                  console.error('Error loading more posts:', error);
                } finally {
                  setLoading(false);
                }
              }}
              disabled={loading}
              className={`inline-flex items-center px-4 py-2 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              {loading ? 'Loading...' : 'Load More'}
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
