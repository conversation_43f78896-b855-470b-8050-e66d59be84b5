'use client';

import ProductForm from "@/components/products/ProductForm";
import { useEffect, useState } from "react";
import { Category } from "@/types/category";
import { categoriesApi } from "@/lib/api";
import { toast } from "@/components/ui/use-toast";
import { Loader2 } from "lucide-react";

const NewProductPage = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        const response = await categoriesApi.getAll();
        if (response.data?.success) {
          setCategories(response.data.data);
        } else {
          toast({
            title: "Error",
            description: "Failed to fetch categories",
            variant: "destructive"
          });
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
        toast({
          title: "Error",
          description: "Failed to load categories",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  if (loading) {
    return (
      <div className="flex h-full items-center justify-center">
        <Loader2 className="h-6 w-6 animate-spin" />
      </div>
    );
  }

  return (
    <div className="flex-col">
      <div className="flex-1 space-y-4 p-8 pt-6">
        <ProductForm categories={categories} />
      </div>
    </div>
  );
}

export default NewProductPage;
