'use client';

import { useState, useCallback, useEffect } from 'react';
import { useRouter, useSearchParams, usePathname } from 'next/navigation';
import { Product } from '@/types/product';
import { Button } from '@/components/ui/button';
import { Plus, Package, CheckCircle2, Clock, AlertCircle, Download, Upload, Loader2 } from 'lucide-react';
import { DataTable } from '@/components/ui/data-table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { createColumns } from '@/components/products/columns';
import { Heading } from '@/components/ui/heading';
import { Separator } from '@/components/ui/separator';
import { AnalyticsCard } from '@/components/ui/analytics-card';
import DeleteDialog from '@/components/shared/DeleteDialog';
import { toast } from '@/lib/toast';
import { cn } from '@/lib/utils';
import { useAuth } from '@/hooks/use-auth';
import { 
  useProducts, 
  useProductAnalytics, 
  useProductCategories, 
  useDeleteProduct,
  useBulkDeleteProducts,
  useExportProducts,
  useImportProducts,
  type ProductFilters
} from '@/hooks/use-products';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

export default function ProductsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  // Toast is imported from @/lib/toast
  const { isSuperAdmin } = useAuth();
  
  // Filters state
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');
  
  // Dialog states
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [bulkDeleteDialogOpen, setBulkDeleteDialogOpen] = useState(false);
  const [selectedRows, setSelectedRows] = useState<Product[]>([]);
  const [importDialogOpen, setImportDialogOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 500); // 500ms delay

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Reset page when filters change
  useEffect(() => {
    setPage(1);
  }, [statusFilter, categoryFilter, debouncedSearchQuery]);

  // Build filters object
  const filters: ProductFilters = {
    page,
    limit: pageSize,
    status: statusFilter,
    categoryId: categoryFilter,
    search: debouncedSearchQuery,
  };

  // React Query hooks
  const { data: productsData, isLoading: productsLoading, error: productsError } = useProducts(filters);
  const { data: analytics, isLoading: analyticsLoading } = useProductAnalytics();
  const { data: categories, isLoading: categoriesLoading } = useProductCategories();
  
  // Debug logs
  console.log('Products Data:', productsData);
  console.log('Analytics Data:', analytics);
  console.log('Categories Data:', categories);
  console.log('Products Error:', productsError);
  
  // Mutation hooks
  const deleteProductMutation = useDeleteProduct();
  const bulkDeleteMutation = useBulkDeleteProducts();
  const exportMutation = useExportProducts();
  const importMutation = useImportProducts();

  // Extract data from API response
  const products = productsData?.products || [];
  const pagination = productsData?.pagination || {};
  const totalPages = pagination?.pages || 1;
  const totalItems = pagination?.total || 0;
  
  // Debug logs for pagination
  console.log('Pagination data:', {
    currentPage: page,
    totalPages,
    totalItems,
    pageSize,
    paginationFromAPI: pagination
  });
  const categoriesList = Array.isArray(categories) ? categories : []; // Ensure it's always an array

  // Loading states
  const isLoading = productsLoading || analyticsLoading || categoriesLoading;

  const getStatusBadgeClass = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'draft':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'inactive':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const handleDelete = async (product: Product) => {
    const loadingToast = toast.crud.deleting('Product');
    try {
      await deleteProductMutation.mutateAsync(product.id);
      toast.dismiss(loadingToast);
      toast.crud.deleted('Product');
    } catch (error: any) {
      console.error('Error deleting product:', error);
      toast.dismiss(loadingToast);
      toast.crud.deleteError('Product', error?.message);
    } finally {
      setDeleteDialogOpen(false);
      setSelectedProduct(null);
    }
  };

  const handleExport = async () => {
    try {
      const response = await exportMutation.mutateAsync(filters);
      
      // Create a Blob from the response data
      const blob = new Blob([response.data], { type: 'text/csv' });
      
      // Create a temporary URL for the blob
      const url = window.URL.createObjectURL(blob);
      
      // Create a temporary anchor element and trigger the download
      const a = document.createElement('a');
      a.href = url;
      a.download = 'products.csv';
      document.body.appendChild(a);
      a.click();
      
      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast.success('Products exported successfully');
    } catch (error) {
      console.error('Error exporting products:', error);
      toast.error('Failed to export products');
    }
  };

  const handleImport = async () => {
    if (!selectedFile) {
      toast.error('Please select a file to import');
      return;
    }

    try {
      await importMutation.mutateAsync(selectedFile);
      toast.success('Products imported successfully');
    } catch (error) {
      console.error('Error importing products:', error);
      toast.error('Failed to import products');
    } finally {
      setImportDialogOpen(false);
      setSelectedFile(null);
    }
  };

  const columns = createColumns({
    onEdit: (productId: string) => router.push(`/products/${productId}/edit`),
    onDelete: (product: Product) => {
      setSelectedProduct(product);
      setDeleteDialogOpen(true);
    },
    getStatusBadgeClass,
  });

  const analyticsCards = [
    {
      title: 'Total Products',
      value: analytics?.totalProducts || 0,
      icon: Package,
    },
    {
      title: 'Active Products',
      value: analytics?.activeProducts || 0,
      icon: CheckCircle2,
    },
    {
      title: 'Draft Products',
      value: analytics?.draftProducts || 0,
      icon: Clock,
    },
    {
      title: 'Categories',
      value: analytics?.totalCategories || 0,
      icon: AlertCircle,
    },
  ];

  return (
    <div className="flex-1 space-y-4">
      <div className="flex items-center justify-between">
        <Heading title="Products" description="Manage your products" />
        <div className="flex items-center gap-4">
          <Button onClick={handleExport}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button onClick={() => setImportDialogOpen(true)}>
            <Upload className="mr-2 h-4 w-4" />
            Import
          </Button>
          <Button onClick={() => router.push('/products/new')}>
            <Plus className="mr-2 h-4 w-4" />
            Add New
          </Button>
        </div>
      </div>
      <Separator />
      
      <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
        {analyticsCards.map((card) => (
          <AnalyticsCard
            key={card.title}
            title={card.title}
            value={card.value}
            icon={card.icon}          />
        ))}
      </div>

      <DataTable
        columns={columns}
        data={products}
        loading={isLoading}
        searchKey="name"
        searchPlaceholder="Search by name, description, or SKU..."
        filterComponent={
          <div className="flex items-center space-x-2 ml-2">
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
              </SelectContent>
            </Select>
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-[160px]">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categoriesList.map(category => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        }
        pagination={{
          page: page,
          pageSize: pageSize,
          total: totalItems,
          onPageChange: (newPage) => {
            console.log('Page changed to:', newPage);
            setPage(newPage);
          }
        }}
        meta={{
          onPageSizeChange: (newPageSize: number) => {
            console.log('Page size changed to:', newPageSize);
            setPageSize(newPageSize);
            setPage(1); // Reset to first page when changing page size
            
            // Update URL with new page size
            const params = new URLSearchParams(searchParams.toString());
            params.set('pageSize', newPageSize.toString());
            params.set('page', '1');
            router.push(`${pathname}?${params.toString()}`);
            
            // Log the updated filters that will be used for the next data fetch
            console.log('Updated filters after page size change:', {
              ...filters,
              page: 1,
              limit: newPageSize
            });
          }
        }}
      />

      <DeleteDialog
        open={deleteDialogOpen}
        onOpenChange={(open) => {
          setDeleteDialogOpen(open);
          if (!open) setSelectedProduct(null);
        }}
        onConfirm={() => selectedProduct && handleDelete(selectedProduct)}
        title="Delete Product"
        description="Are you sure you want to delete this product? This action cannot be undone."
      />

      <AlertDialog open={importDialogOpen} onOpenChange={setImportDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Import Products</AlertDialogTitle>
              <AlertDialogDescription>
                Upload a CSV file to import products. Download the template first if you haven't already.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <div className="grid gap-4 py-4">
              <div className="flex items-center gap-4">
                <Input
                  type="file"
                  accept=".csv"
                  onChange={(e) => setSelectedFile(e.target.files?.[0] || null)}
                />
              </div>
            </div>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={handleImport}>
                <Upload className="mr-2 h-4 w-4" />
                Import
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
    </div>
  );
}
