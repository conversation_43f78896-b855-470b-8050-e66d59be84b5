import { Collection, CreateCollectionDTO, UpdateCollectionDTO } from '@/types/collection';
import { PaginatedResponse } from '@/types/pagination';
import api from '@/lib/api';

export class CollectionService {
  static async getCollections(page: number = 1, limit: number = 10) {
    const response = await api.get<PaginatedResponse<Collection>>(`/api/collections?page=${page}&limit=${limit}`);
    return response.data;
  }

  static async getCollection(id: string) {
    const response = await api.get<Collection>(`/api/collections/id/${id}`);
    return response.data;
  }

  static async createCollection(data: CreateCollectionDTO) {
    const response = await api.post<Collection>('/api/collections', data);
    return response.data;
  }

  static async updateCollection(id: string, data: UpdateCollectionDTO) {
    const response = await api.put<Collection>(`/api/collections/${id}`, data);
    return response.data;
  }

  static async deleteCollection(id: string) {
    await api.delete(`/api/collections/${id}`);
  }

  static async updateProductPositions(id: string, products: { productId: string; position: number }[]) {
    const response = await api.put<Collection>(`/api/collections/${id}/positions`, { products });
    return response.data;
  }
}
