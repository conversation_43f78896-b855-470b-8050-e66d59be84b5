"use client";

import { ColumnDef } from "@tanstack/react-table";
import { DataTableColumnHeader } from "@/components/ui/data-table-header";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { MoreHorizontal, Eye, ShoppingBag, Trash } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Customer } from "@/services/customer.service";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { customerService } from "@/services/customer.service";
import { toast } from "react-hot-toast";

export const columns: ColumnDef<Customer>[] = [
  {
    accessorKey: "name",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Name" />
    ),
    enableSorting: true,
    size: 250,
    cell: ({ row }) => {
      const firstName = row.original.firstName || '';
      const lastName = row.original.lastName || '';
      const fullName = firstName || lastName ? `${firstName} ${lastName}`.trim() : '-';
      const router = useRouter();

      return (
        <Button
          variant="link"
          className="p-0 h-auto font-normal text-left justify-start"
          onClick={() => router.push(`/customers/${row.original.id}`)}
        >
          {fullName}
        </Button>
      );
    },
  },
  {
    accessorKey: "email",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Email" />
    ),
    enableSorting: true,
    size: 280,
    cell: ({ row }) => (
      <div className="text-sm">
        {row.original.email || '-'}
      </div>
    ),
  },
  {
    accessorKey: "phone",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Phone" />
    ),
    enableSorting: true,
    size: 150,
    cell: ({ row }) => (
      <div className="text-sm font-mono">
        {row.original.phone || "-"}
      </div>
    ),
  },
  {
    accessorKey: "reward.points",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Points" />
    ),
    enableSorting: true,
    size: 80,
    cell: ({ row }) => (
      <div className="text-center font-medium">
        {row.original.reward?.points || 0}
      </div>
    ),
  },
  {
    accessorKey: "reward.tier",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Tier" />
    ),
    enableSorting: true,
    size: 100,
    cell: ({ row }) => (
      <Badge variant={getTierVariant(row.original.reward?.tier)} className="text-xs">
        {row.original.reward?.tier || "GREEN"}
      </Badge>
    ),
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    enableSorting: true,
    size: 140,
    cell: ({ row }) => {
      const isVip = row.original.isVip;
      const isNew = new Date(row.original.createdAt).getTime() > Date.now() - 30 * 24 * 60 * 60 * 1000;

      return (
        <div className="flex flex-wrap gap-1">
          {isVip && <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-300 text-xs">VIP</Badge>}
          {isNew && <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300 text-xs">New</Badge>}
        </div>
      );
    }
  },
  {
    accessorKey: "createdAt",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Joined" />
    ),
    size: 120,
    cell: ({ row }) => {
      return (
        <div className="text-muted-foreground text-sm">
          {new Date(row.original.createdAt).toLocaleDateString()}
        </div>
      );
    }
  },
  {
    id: "actions",
    header: "Actions",
    size: 80,
    cell: ({ row }) => {
      const router = useRouter();
      const customer = row.original;
      const [isDeleteAlertOpen, setIsDeleteAlertOpen] = useState(false);
      const [isDeleting, setIsDeleting] = useState(false);

      const handleDelete = async () => {
        try {
          setIsDeleting(true);
          await customerService.deleteCustomer(customer.id);
          // The toast is already handled in the service
          // Refresh the page to update the list
          router.refresh();
        } catch (error) {
          // Error is already handled in the service
          console.error("Error deleting customer:", error);
        } finally {
          setIsDeleting(false);
          setIsDeleteAlertOpen(false);
        }
      };

      return (
        <>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">Actions</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => router.push(`/customers/${customer.id}`)}>
                <Eye className="mr-2 h-4 w-4" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => router.push(`/customers/${customer.id}/orders`)}>
                <ShoppingBag className="mr-2 h-4 w-4" />
                View Orders
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={() => setIsDeleteAlertOpen(true)}
                className="text-destructive focus:text-destructive"
              >
                <Trash className="mr-2 h-4 w-4" />
                Delete Customer
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <AlertDialog open={isDeleteAlertOpen} onOpenChange={setIsDeleteAlertOpen}>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Are you sure you want to delete this customer?</AlertDialogTitle>
                <AlertDialogDescription>
                  This action cannot be undone. This will permanently delete the customer
                  {customer.firstName || customer.lastName ? ` "${(customer.firstName || '')} ${(customer.lastName || '')}"`.trim() : ''} 
                  and all associated data.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
                <AlertDialogAction 
                  onClick={(e) => {
                    e.preventDefault();
                    handleDelete();
                  }}
                  disabled={isDeleting}
                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                >
                  {isDeleting ? "Deleting..." : "Delete"}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </>
      );
    },
  },
];

function getTierVariant(tier?: string) {
  switch (tier) {
    case 'PLATINUM':
      return 'default';
    case 'GOLD':
      return 'destructive';
    case 'SILVER':
      return 'secondary';
    default:
      return 'outline';
  }
}
