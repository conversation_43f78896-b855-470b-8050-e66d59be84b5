-- Add ready stock fields to ProductVariant table
ALTER TABLE "ProductVariant" ADD COLUMN "isReadyStock" BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE "ProductVariant" ADD COLUMN "readyStockQty" INTEGER NOT NULL DEFAULT 0;
ALTER TABLE "ProductVariant" ADD COLUMN "readyStockAlert" INTEGER NOT NULL DEFAULT 5;

-- Create index for ready stock queries
CREATE INDEX "ProductVariant_isReadyStock_idx" ON "ProductVariant"("isReadyStock");
