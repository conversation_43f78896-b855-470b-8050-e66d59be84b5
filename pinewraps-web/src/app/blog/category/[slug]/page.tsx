import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { BlogService } from '@/services/blog.service';
import BlogList from '../../BlogList';
import { createMetadata } from '@/lib/metadata';
import Link from 'next/link';
import { Breadcrumbs } from '@/components/shared/Breadcrumbs';
import JsonLd from '@/components/seo/JsonLd';

interface PageProps {
  params: {
    slug: string;
  };
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  try {
    const { slug } = params;
    const { data: categories } = await BlogService.getAllCategories();
    const category = categories.find(cat => cat.slug === slug);
    
    if (!category) {
      return createMetadata({
        title: 'Category Not Found - Pinewraps Dubai',
        description: 'The requested blog category could not be found.',
        path: `/blog/category/${slug}`,
      });
    }

    return createMetadata({
      title: `${category.name} - Blog Category - Pinewraps Dubai`,
      description: `Explore our blog posts in the ${category.name} category. Find tips, insights, and inspiration from Pinewraps Dubai.`,
      path: `/blog/category/${slug}`,
    });
  } catch (error) {
    return createMetadata({
      title: 'Blog Category - Pinewraps Dubai',
      description: 'Explore blog posts by category from Pinewraps Dubai.',
      path: `/blog/category/${params.slug}`,
    });
  }
}

export const dynamic = 'force-dynamic';

export default async function CategoryPage({ params }: PageProps) {
  try {
    const { slug } = params;
    const { data: categories } = await BlogService.getAllCategories();
    const category = categories.find(cat => cat.slug === slug);
    
    if (!category) {
      notFound();
    }

    const { data: posts } = await BlogService.getPostsByCategory(slug, 1, 10);
    
    return (
      <div className="container mx-auto px-4 py-8">
        {/* Add breadcrumb structured data */}
        <JsonLd 
          type="breadcrumb" 
          data={[
            { label: 'Home', href: '/' },
            { label: 'Blog', href: '/blog' },
            { label: `Category: ${category.name}` }
          ]} 
        />
        
        {/* Breadcrumb Navigation */}
        <nav className="text-sm mb-8">
          <Breadcrumbs 
            items={[
              { label: 'Home', href: '/' },
              { label: 'Blog', href: '/blog' },
              { label: `Category: ${category.name}` }
            ]}
          />
        </nav>

        {/* Category Title */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900">{category.name}</h1>
          <p className="mt-4 text-xl text-gray-600">
            Browse all articles in the {category.name} category
          </p>
        </div>

        {/* Blog Posts */}
        {!posts || posts.length === 0 ? (
          <div className="text-center py-10">
            <p className="text-lg text-gray-500">No posts found in this category.</p>
            <Link href="/blog" className="mt-4 inline-block text-blue-600 hover:underline">
              Return to all blog posts
            </Link>
          </div>
        ) : (
          <BlogList initialPosts={posts} />
        )}
      </div>
    );
  } catch (error) {
    console.error('Error loading category page:', error);
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4">Category Not Found</h1>
          <p className="text-xl text-gray-600">We couldn't find the requested category.</p>
        </div>
        <div className="text-center py-10">
          <Link href="/blog" className="inline-block text-blue-600 hover:underline">
            Return to all blog posts
          </Link>
        </div>
      </div>
    );
  }
}
