import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "@/components/ui/use-toast";
import { companyService, Company } from "@/services/company.service";

// Company interface is now imported from the service

export const useCompanies = () => {
  const queryClient = useQueryClient();

  const { data: companies, isLoading, error } = useQuery<Company[]>({
    queryKey: ["companies"],
    queryFn: () => companyService.getCompanies(),
  });

  const createCompany = useMutation({
    mutationFn: (data: Omit<Company, "id">) => companyService.createCompany(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["companies"] });
      toast({ title: "Company created successfully" });
    },
    onError: () => {
      toast({ title: "Failed to create company", variant: "destructive" });
    },
  });

  const updateCompany = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<Company> }) => companyService.updateCompany(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["companies"] });
      toast({ title: "Company updated successfully" });
    },
    onError: () => {
      toast({ title: "Failed to update company", variant: "destructive" });
    },
  });

  const deleteCompany = useMutation({
    mutationFn: (id: string) => companyService.deleteCompany(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["companies"] });
      toast({ title: "Company deleted successfully" });
    },
    onError: () => {
      toast({ title: "Failed to delete company", variant: "destructive" });
    },
  });

  return {
    companies,
    isLoading,
    error,
    createCompany: createCompany.mutateAsync,
    updateCompany: updateCompany.mutateAsync,
    deleteCompany: deleteCompany.mutateAsync,
  };
};
