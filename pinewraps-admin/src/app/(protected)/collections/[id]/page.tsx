'use client';

import { useEffect, useState } from 'react';
import { use } from 'react';
import { useRouter } from 'next/navigation';
import { Collection } from '@/types/collection';
import { CollectionService } from '@/services/collection.service';
import { CollectionForm } from '@/components/collections/collection-form';
import { toast } from 'react-hot-toast';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

interface PageProps {
  params: Promise<{ id: string }>
}

export default function EditCollectionPage({ params }: PageProps) {
  const { id } = use(params);
  const [collection, setCollection] = useState<Collection | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    loadCollection();
  }, [id]);

  const loadCollection = async () => {
    try {
      const data = await CollectionService.getCollection(id);
      setCollection(data);
    } catch (error) {
      console.error('Error loading collection:', error);
      toast.error('Failed to load collection');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (data: any) => {
    try {
      await CollectionService.updateCollection(id, data);
      toast.success('Collection updated successfully');
      router.push('/collections');
    } catch (error) {
      console.error('Error updating collection:', error);
      toast.error('Failed to update collection');
    }
  };

  return (
    <div className="p-6">
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="ghost"
          onClick={() => router.push('/collections')}
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <h1 className="text-2xl font-bold">Edit Collection</h1>
      </div>

      {!loading && collection && (
        <CollectionForm
          initialData={collection}
          onSubmit={handleSubmit}
          loading={loading}
        />
      )}
    </div>
  );
}
