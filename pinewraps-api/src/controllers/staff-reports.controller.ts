import { Request, Response } from 'express';
import { StaffAnalyticsService } from '../services/staff-analytics.service';
import { ApiError } from '../lib/error';
import { z } from 'zod';
import { prisma } from '../lib/prisma';

// Validation schemas
const StaffReportsQuerySchema = z.object({
  staffId: z.string().optional(),
  department: z.enum(['kitchen', 'design', 'finalCheck', 'cashier']).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  timeRange: z.enum(['7d', '14d', '30d', '3m', 'all', 'custom']).optional().default('30d'),
});

const DepartmentMetricsQuerySchema = z.object({
  department: z.enum(['kitchen', 'design', 'finalCheck', 'cashier']).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  timeRange: z.enum(['7d', '14d', '30d', '3m', 'all', 'custom']).optional().default('30d'),
});

export class StaffReportsController {
  /**
   * Debug endpoint to check staff assignments in orders
   */
  static async debugStaffAssignments(req: Request, res: Response) {
    try {
      const staffId = req.params.staffId;

      if (!staffId) {
        return res.status(400).json({
          success: false,
          error: { message: 'Staff ID is required' }
        });
      }

      // Get staff info
      const staff = await prisma.user.findUnique({
        where: { id: staffId },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          isKitchenStaff: true,
          isDesignStaff: true,
          isFinalCheckStaff: true,
          isCashierStaff: true,
        }
      });

      if (!staff) {
        return res.status(404).json({
          success: false,
          error: { message: 'Staff not found' }
        });
      }

      // Get orders where this staff member is assigned
      const ordersAsCreator = await prisma.pOSOrder.count({ where: { createdById: staffId } });
      const ordersAsKitchen = await prisma.pOSOrder.count({ where: { kitchenById: staffId } });
      const ordersAsDesign = await prisma.pOSOrder.count({ where: { designById: staffId } });
      const ordersAsFinalCheck = await prisma.pOSOrder.count({ where: { finalCheckById: staffId } });

      // Get sample orders
      const sampleOrders = await prisma.pOSOrder.findMany({
        where: {
          OR: [
            { createdById: staffId },
            { kitchenById: staffId },
            { designById: staffId },
            { finalCheckById: staffId },
          ]
        },
        select: {
          id: true,
          orderNumber: true,
          status: true,
          createdById: true,
          kitchenById: true,
          designById: true,
          finalCheckById: true,
          requiresKitchen: true,
          requiresDesign: true,
          requiresFinalCheck: true,
        },
        take: 10
      });

      res.json({
        success: true,
        data: {
          staff: {
            id: staff.id,
            name: `${staff.firstName} ${staff.lastName}`,
            roles: {
              kitchen: staff.isKitchenStaff,
              design: staff.isDesignStaff,
              finalCheck: staff.isFinalCheckStaff,
              cashier: staff.isCashierStaff,
            }
          },
          orderCounts: {
            asCreator: ordersAsCreator,
            asKitchen: ordersAsKitchen,
            asDesign: ordersAsDesign,
            asFinalCheck: ordersAsFinalCheck,
            total: ordersAsCreator + ordersAsKitchen + ordersAsDesign + ordersAsFinalCheck
          },
          sampleOrders
        }
      });
    } catch (error: any) {
      console.error('Error in debug staff assignments:', error);
      res.status(500).json({
        success: false,
        error: { message: error.message || 'Failed to debug staff assignments' }
      });
    }
  }

  /**
   * Debug endpoint to check users in database
   */
  static async debugUsers(req: Request, res: Response) {
    try {
      const allUsers = await prisma.user.findMany({
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          role: true,
          isActive: true,
          isDeleted: true,
          isKitchenStaff: true,
          isDesignStaff: true,
          isFinalCheckStaff: true,
          isCashierStaff: true,
        },
        take: 50 // Increase limit to see more users
      });

      console.log('DEBUG: All users found:', allUsers.length);

      const staffUsers = allUsers.filter(user =>
        user.isKitchenStaff || user.isDesignStaff || user.isFinalCheckStaff || user.isCashierStaff
      );

      // Also check POS orders
      const totalOrders = await prisma.pOSOrder.count();
      const recentOrders = await prisma.pOSOrder.findMany({
        select: {
          id: true,
          orderNumber: true,
          status: true,
          createdById: true,
          kitchenById: true,
          designById: true,
          finalCheckById: true,
          requiresKitchen: true,
          requiresDesign: true,
          requiresFinalCheck: true,
          createdAt: true,
        },
        orderBy: { createdAt: 'desc' },
        take: 10
      });

      console.log('DEBUG: POS Orders found:', totalOrders);
      console.log('DEBUG: Recent orders:', recentOrders);

      res.json({
        success: true,
        data: {
          totalUsers: allUsers.length,
          staffUsers: staffUsers.length,
          totalOrders,
          allUsers: allUsers.map(u => ({
            id: u.id,
            name: `${u.firstName} ${u.lastName}`,
            role: u.role,
            isActive: u.isActive,
            isDeleted: u.isDeleted,
            kitchen: u.isKitchenStaff,
            design: u.isDesignStaff,
            finalCheck: u.isFinalCheckStaff,
            cashier: u.isCashierStaff
          })),
          staffOnly: staffUsers.map(u => ({
            id: u.id,
            name: `${u.firstName} ${u.lastName}`,
            role: u.role,
            kitchen: u.isKitchenStaff,
            design: u.isDesignStaff,
            finalCheck: u.isFinalCheckStaff,
            cashier: u.isCashierStaff
          })),
          recentOrders: recentOrders.map(o => ({
            id: o.id,
            orderNumber: o.orderNumber,
            status: o.status,
            createdById: o.createdById,
            kitchenById: o.kitchenById,
            designById: o.designById,
            finalCheckById: o.finalCheckById,
            requires: {
              kitchen: o.requiresKitchen,
              design: o.requiresDesign,
              finalCheck: o.requiresFinalCheck
            },
            createdAt: o.createdAt
          }))
        }
      });
    } catch (error: any) {
      console.error('Error in debug users:', error);
      res.status(500).json({
        success: false,
        error: {
          message: error.message || 'Failed to debug users',
          code: 500,
        },
      });
    }
  }

  /**
   * Get detailed individual staff analytics
   */
  static async getIndividualStaffDetails(req: Request, res: Response) {
    try {
      const { staffId } = req.params;
      const query = StaffReportsQuerySchema.parse(req.query);

      // Calculate date range
      const { startDate, endDate } = calculateDateRange(query.timeRange, query.startDate, query.endDate);

      const staffDetails = await StaffAnalyticsService.getIndividualStaffDetails(staffId, startDate, endDate);

      res.json({
        success: true,
        data: staffDetails,
      });
    } catch (error: any) {
      console.error('Error getting individual staff details:', error);
      if (error instanceof ApiError) {
        return res.status(error.statusCode).json({
          success: false,
          error: {
            message: error.message,
            code: error.statusCode,
          },
        });
      }
      return res.status(500).json({
        success: false,
        error: {
          message: error.message || 'Failed to get staff details',
          code: 500,
        },
      });
    }
  }

  /**
   * Get individual staff performance metrics
   */
  static async getStaffPerformance(req: Request, res: Response) {
    try {
      const query = StaffReportsQuerySchema.parse(req.query);
      
      // Calculate date range
      const { startDate, endDate } = calculateDateRange(query.timeRange, query.startDate, query.endDate);

      // Use optimized method for better performance and accuracy
      console.log('Using optimized staff performance calculation for individual endpoint');

      // Check if we should show all roles (when no department filter is applied and showAllRoles is requested)
      const showAllRoles = !query.department && req.query.showAllRoles === 'true';
      console.log(`Show all roles: ${showAllRoles}, Department filter: ${query.department}`);

      const staffPerformance = await StaffAnalyticsService.getOptimizedStaffPerformance(
        query.department,
        startDate,
        endDate,
        showAllRoles
      );

      // Filter by staffId if specified
      const filteredStaffPerformance = query.staffId
        ? staffPerformance.filter(staff => staff.staffId === query.staffId)
        : staffPerformance;

      return res.json({
        success: true,
        data: filteredStaffPerformance,
        meta: {
          timeRange: query.timeRange,
          startDate: startDate?.toISOString(),
          endDate: endDate?.toISOString(),
          department: query.department,
          staffId: query.staffId,
        },
      });
    } catch (error) {
      console.error('Error getting staff performance:', error);
      
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid query parameters',
            details: error.errors,
          },
        });
      }

      return res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to get staff performance metrics',
        },
      });
    }
  }

  /**
   * Get department performance metrics
   */
  static async getDepartmentMetrics(req: Request, res: Response) {
    try {
      const query = DepartmentMetricsQuerySchema.parse(req.query);
      
      // Calculate date range
      const { startDate, endDate } = calculateDateRange(query.timeRange, query.startDate, query.endDate);

      const departmentMetrics = await StaffAnalyticsService.getDepartmentMetrics(
        startDate,
        endDate
      );

      return res.json({
        success: true,
        data: departmentMetrics,
        meta: {
          timeRange: query.timeRange,
          startDate: startDate?.toISOString(),
          endDate: endDate?.toISOString(),
        },
      });
    } catch (error) {
      console.error('Error getting department metrics:', error);
      
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid query parameters',
            details: error.errors,
          },
        });
      }

      return res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to get department metrics',
        },
      });
    }
  }

  /**
   * Get quality control metrics
   */
  static async getQualityMetrics(req: Request, res: Response) {
    try {
      const query = DepartmentMetricsQuerySchema.parse(req.query);
      
      // Calculate date range
      const { startDate, endDate } = calculateDateRange(query.timeRange, query.startDate, query.endDate);

      const qualityMetrics = await StaffAnalyticsService.getQualityMetrics(
        startDate,
        endDate
      );

      return res.json({
        success: true,
        data: qualityMetrics,
        meta: {
          timeRange: query.timeRange,
          startDate: startDate?.toISOString(),
          endDate: endDate?.toISOString(),
        },
      });
    } catch (error) {
      console.error('Error getting quality metrics:', error);
      
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid query parameters',
            details: error.errors,
          },
        });
      }

      return res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to get quality metrics',
        },
      });
    }
  }

  /**
   * Get comprehensive staff dashboard data
   */
  static async getStaffDashboard(req: Request, res: Response) {
    try {
      console.log('Staff dashboard request received:', req.query);
      const query = DepartmentMetricsQuerySchema.parse(req.query);

      // Calculate date range
      const { startDate, endDate } = calculateDateRange(query.timeRange, query.startDate, query.endDate);
      console.log('Date range calculated:', { startDate, endDate });

      // Use optimized method for better performance
      console.log('Using optimized staff performance calculation');
      const [staffPerformance, departmentMetrics, qualityMetrics] = await Promise.all([
        StaffAnalyticsService.getOptimizedStaffPerformance(query.department, startDate, endDate),
        StaffAnalyticsService.getDepartmentMetrics(startDate, endDate),
        StaffAnalyticsService.getQualityMetrics(startDate, endDate),
      ]);

      // Calculate summary statistics
      const totalStaff = staffPerformance.length;
      const totalOrdersProcessed = staffPerformance.reduce((sum, staff) => sum + staff.ordersCompleted, 0);
      const averageProductivity = staffPerformance.length > 0
        ? staffPerformance.reduce((sum, staff) => sum + staff.productivityScore, 0) / staffPerformance.length
        : 0;
      const averageQualityScore = staffPerformance.length > 0
        ? staffPerformance.reduce((sum, staff) => sum + staff.qualityScore, 0) / staffPerformance.length
        : 0;

      // Get top performers
      const topPerformers = staffPerformance
        .sort((a, b) => b.productivityScore - a.productivityScore)
        .slice(0, 5);

      return res.json({
        success: true,
        data: {
          summary: {
            totalStaff,
            totalOrdersProcessed,
            averageProductivity: Math.round(averageProductivity * 100) / 100,
            averageQualityScore: Math.round(averageQualityScore * 100) / 100,
          },
          topPerformers,
          departmentMetrics,
          qualityMetrics,
          staffPerformance,
        },
        meta: {
          timeRange: query.timeRange,
          startDate: startDate?.toISOString(),
          endDate: endDate?.toISOString(),
        },
      });
    } catch (error) {
      console.error('Error getting staff dashboard:', error);
      
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid query parameters',
            details: error.errors,
          },
        });
      }

      return res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to get staff dashboard data',
        },
      });
    }
  }

  /**
   * Get productivity trends over time
   */
  static async getProductivityTrends(req: Request, res: Response) {
    try {
      const query = StaffReportsQuerySchema.parse(req.query);
      
      // For trends, we'll get data for multiple periods
      const periods = generatePeriods(query.timeRange || '30d');
      const trends = [];

      for (const period of periods) {
        const staffPerformance = await StaffAnalyticsService.getStaffPerformance(
          query.staffId,
          query.department,
          period.startDate,
          period.endDate
        );

        const averageProductivity = staffPerformance.length > 0
          ? staffPerformance.reduce((sum, staff) => sum + staff.productivityScore, 0) / staffPerformance.length
          : 0;

        const totalOrders = staffPerformance.reduce((sum, staff) => sum + staff.ordersCompleted, 0);

        trends.push({
          period: period.label,
          date: period.startDate.toISOString().split('T')[0],
          averageProductivity: Math.round(averageProductivity * 100) / 100,
          totalOrders,
          staffCount: staffPerformance.length,
        });
      }

      return res.json({
        success: true,
        data: trends,
        meta: {
          timeRange: query.timeRange,
          department: query.department,
          staffId: query.staffId,
        },
      });
    } catch (error) {
      console.error('Error getting productivity trends:', error);
      
      return res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to get productivity trends',
        },
      });
    }
  }

  /**
   * Debug final check data specifically
   */
  static async debugFinalCheckData(req: Request, res: Response) {
    try {
      // Check final check staff
      const finalCheckStaff = await prisma.user.findMany({
        where: {
          isFinalCheckStaff: true,
          isDeleted: false
        },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          isFinalCheckStaff: true,
          isActive: true
        }
      });

      // Check orders with final check assignments
      const ordersWithFinalCheck = await prisma.pOSOrder.findMany({
        where: {
          finalCheckById: { not: null }
        },
        select: {
          id: true,
          orderNumber: true,
          finalCheckById: true,
          finalCheckStartTime: true,
          finalCheckEndTime: true,
          requiresFinalCheck: true,
          status: true,
          createdAt: true
        },
        take: 10,
        orderBy: { createdAt: 'desc' }
      });

      // Check orders that require final check
      const ordersRequiringFinalCheck = await prisma.pOSOrder.count({
        where: { requiresFinalCheck: true }
      });
      
      // Check orders with specific final check statuses
      const finalCheckStatuses = ['FINAL_CHECK_QUEUE', 'FINAL_CHECK_PROCESSING', 'COMPLETED'];
      const ordersByStatus = {};
      
      // Get counts for each status
      for (const status of finalCheckStatuses) {
        const count = await prisma.pOSOrder.count({
          where: { status: status as any }
        });
        ordersByStatus[status] = count;
      }
      
      // Get recent orders in each status
      const recentOrdersByStatus = {};
      for (const status of finalCheckStatuses) {
        const orders = await prisma.pOSOrder.findMany({
          where: { status: status as any },
          select: {
            id: true,
            orderNumber: true,
            finalCheckById: true,
            finalCheckStartTime: true,
            finalCheckEndTime: true,
            requiresFinalCheck: true,
            status: true,
            createdAt: true
          },
          take: 5,
          orderBy: { createdAt: 'desc' }
        });
        recentOrdersByStatus[status] = orders;
      }
      
      // Check orders assigned to each final check staff member
      const staffOrderCounts = [];
      for (const staff of finalCheckStaff) {
        const count = await prisma.pOSOrder.count({
          where: { finalCheckById: staff.id }
        });
        staffOrderCounts.push({
          staffId: staff.id,
          staffName: `${staff.firstName} ${staff.lastName}`,
          orderCount: count
        });
      }

      res.json({
        success: true,
        data: {
          finalCheckStaff: finalCheckStaff,
          finalCheckStaffCount: finalCheckStaff.length,
          ordersWithFinalCheckAssignment: ordersWithFinalCheck,
          ordersWithFinalCheckCount: ordersWithFinalCheck.length,
          ordersRequiringFinalCheckCount: ordersRequiringFinalCheck,
          ordersByStatus: ordersByStatus,
          recentOrdersByStatus: recentOrdersByStatus,
          staffOrderCounts: staffOrderCounts
        }
      });
    } catch (error) {
      console.error('Error in debug final check data:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to debug final check data'
      });
    }
  }
}

/**
 * Calculate date range based on time range parameter
 */
function calculateDateRange(timeRange: string, customStartDate?: string, customEndDate?: string) {
  const now = new Date();
  let startDate = new Date();
  let endDate = now;

  if (timeRange === 'custom' && customStartDate) {
    startDate = new Date(customStartDate);
    endDate = customEndDate ? new Date(customEndDate) : now;
  } else {
    switch (timeRange) {
      case '7d':
        startDate.setDate(now.getDate() - 7);
        break;
      case '14d':
        startDate.setDate(now.getDate() - 14);
        break;
      case '30d':
        startDate.setDate(now.getDate() - 30);
        break;
      case '3m':
        startDate.setMonth(now.getMonth() - 3);
        break;
      case 'all':
        startDate = new Date(0);
        break;
    }
  }

  return { startDate, endDate };
}

/**
 * Generate periods for trend analysis
 */
function generatePeriods(timeRange: string) {
  const now = new Date();
  const periods = [];
  
  let periodCount = 7;
  let periodLength = 1; // days
  
  switch (timeRange) {
    case '7d':
      periodCount = 7;
      periodLength = 1;
      break;
    case '14d':
      periodCount = 14;
      periodLength = 1;
      break;
    case '30d':
      periodCount = 30;
      periodLength = 1;
      break;
    case '3m':
      periodCount = 12;
      periodLength = 7; // weeks
      break;
  }

  for (let i = periodCount - 1; i >= 0; i--) {
    const endDate = new Date(now);
    endDate.setDate(now.getDate() - (i * periodLength));
    
    const startDate = new Date(endDate);
    startDate.setDate(endDate.getDate() - periodLength + 1);
    
    periods.push({
      startDate,
      endDate,
      label: endDate.toISOString().split('T')[0],
    });
  }

  return periods;
}
