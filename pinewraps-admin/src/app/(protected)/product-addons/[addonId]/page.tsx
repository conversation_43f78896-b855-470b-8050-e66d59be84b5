'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Heading } from '@/components/ui/heading';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import { 
  Form, 
  FormControl, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Trash, Plus, ArrowLeft, Save, Loader2 } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';

// Form schema for product addon
const formSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  options: z.array(z.object({
    id: z.string().optional(),
    name: z.string().min(1, 'Option name is required'),
    price: z.coerce.number().min(0, 'Price must be a positive number'),
    allowsCustomText: z.boolean().default(false),
    customTextLabel: z.string().optional(),
    maxTextLength: z.coerce.number().optional(),
  })).min(1, 'At least one option is required'),
});

type FormValues = z.infer<typeof formSchema>;

export default function EditProductAddonPage({ params }: { params: { addonId: string } }) {
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(true);

  // Initialize form with default values
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      description: '',
      options: [
        {
          name: '',
          price: 0,
          allowsCustomText: false,
          customTextLabel: '',
          maxTextLength: undefined,
        }
      ],
    },
  });

  // Fetch addon data
  useEffect(() => {
    const fetchAddon = async () => {
      try {
        const response = await fetch(`/api/product-addons/${params.addonId}`);
        if (!response.ok) {
          throw new Error('Failed to fetch product addon');
        }
        
        const data = await response.json();
        form.reset({
          name: data.name,
          description: data.description || '',
          options: data.options.map((option: any) => ({
            id: option.id,
            name: option.name,
            price: option.price,
            allowsCustomText: option.allowsCustomText,
            customTextLabel: option.customTextLabel || '',
            maxTextLength: option.maxTextLength,
          })),
        });
      } catch (error) {
        console.error('Error fetching product addon:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch product addon',
          variant: 'destructive',
        });
      } finally {
        setFetchLoading(false);
      }
    };

    fetchAddon();
  }, [params.addonId, form, toast]);

  // Handle form submission
  const onSubmit = async (data: FormValues) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/product-addons/${params.addonId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Failed to update product addon');
      }

      toast({
        title: 'Success',
        description: 'Product addon updated successfully',
      });
      router.push('/product-addons');
    } catch (error) {
      console.error('Error updating product addon:', error);
      toast({
        title: 'Error',
        description: 'Failed to update product addon',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Add new option
  const addOption = () => {
    const currentOptions = form.getValues('options');
    form.setValue('options', [
      ...currentOptions,
      {
        name: '',
        price: 0,
        allowsCustomText: false,
        customTextLabel: '',
        maxTextLength: undefined,
      }
    ]);
  };

  // Remove option
  const removeOption = (index: number) => {
    const currentOptions = form.getValues('options');
    if (currentOptions.length === 1) {
      toast({
        title: 'Error',
        description: 'At least one option is required',
        variant: 'destructive',
      });
      return;
    }
    form.setValue('options', currentOptions.filter((_, i) => i !== index));
  };

  if (fetchLoading) {
    return (
      <div className="flex-1 space-y-4 p-8 pt-6 flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <Heading title="Edit Product Addon" description="Modify your product addon" />
        <Button variant="outline" onClick={() => router.push('/product-addons')}>
          <ArrowLeft className="mr-2 h-4 w-4" /> Back
        </Button>
      </div>
      <Separator />

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <div className="grid grid-cols-1 gap-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter addon name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Enter addon description" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Options</h3>
                <Button type="button" variant="outline" onClick={addOption}>
                  <Plus className="mr-2 h-4 w-4" /> Add Option
                </Button>
              </div>

              {form.watch('options').map((_, index) => (
                <Card key={index} className="overflow-hidden">
                  <CardContent className="p-4">
                    <div className="flex justify-between items-center mb-4">
                      <h4 className="font-medium">Option {index + 1}</h4>
                      <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        onClick={() => removeOption(index)}
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name={`options.${index}.name`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Name</FormLabel>
                            <FormControl>
                              <Input placeholder="Option name" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`options.${index}.price`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Price</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                step="0.01"
                                placeholder="0.00"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="mt-4">
                      <FormField
                        control={form.control}
                        name={`options.${index}.allowsCustomText`}
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                            <div className="space-y-0.5">
                              <FormLabel>Allow Custom Text</FormLabel>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </div>

                    {form.watch(`options.${index}.allowsCustomText`) && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <FormField
                          control={form.control}
                          name={`options.${index}.customTextLabel`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Custom Text Label</FormLabel>
                              <FormControl>
                                <Input placeholder="Enter text label" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name={`options.${index}.maxTextLength`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Max Text Length</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  placeholder="50"
                                  {...field}
                                  value={field.value || ''}
                                  onChange={(e) => {
                                    const value = e.target.value === '' ? undefined : parseInt(e.target.value);
                                    field.onChange(value);
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          <Button type="submit" disabled={loading} className="ml-auto">
            {loading && <Save className="mr-2 h-4 w-4 animate-spin" />}
            {!loading && <Save className="mr-2 h-4 w-4" />}
            Update Addon
          </Button>
        </form>
      </Form>
    </div>
  );
}
