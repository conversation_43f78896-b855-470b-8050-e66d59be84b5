'use client';

import { ColumnDef } from '@tanstack/react-table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { MoreHorizontal, Pencil, Trash2, ArrowUpDown, Archive, Package } from 'lucide-react';

interface UnitMetric {
  id: string;
  name: string;
  displayName: string;
  isActive: boolean;
  isDefault: boolean;
}

export interface InventoryItem {
  id: string;
  name: string;
  description?: string;
  sku?: string;
  minQuantity: number;
  currentStock: number;
  unitMetric?: UnitMetric;
  unitMetricId: string;
  conversionRate?: number;
  isActive: boolean;
  categoryId?: string;
  category?: {
    id: string;
    name: string;
  };
  adjustments?: any[];
  stockHistory?: any[];
  purchaseOrderItems?: any[];
}

interface ColumnsProps {
  onEdit: (item: InventoryItem) => void;
  onDelete: (item: InventoryItem) => void;
}

export const columns = ({ onEdit, onDelete }: ColumnsProps): ColumnDef<InventoryItem>[] => [
  {
    accessorKey: 'name',
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          className="h-auto p-0 font-semibold"
        >
          Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => (
      <div className="font-medium">{row.getValue('name')}</div>
    ),
  },
  {
    accessorKey: 'sku',
    header: 'SKU',
    cell: ({ row }) => {
      const sku = row.getValue('sku') as string;
      return <div className="font-mono text-sm">{sku || '-'}</div>;
    },
  },
  {
    accessorKey: 'category.name',
    header: 'Category',
    cell: ({ row }) => {
      const category = row.original.category;
      return <div>{category?.name || '-'}</div>;
    },
  },
  {
    accessorKey: 'currentStock',
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          className="h-auto p-0 font-semibold"
        >
          <Package className="mr-2 h-4 w-4" />
          Stock
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const currentStock = row.getValue('currentStock') as number;
      const minQuantity = row.original.minQuantity;
      const isLowStock = currentStock <= minQuantity;

      return (
        <Badge
          variant={isLowStock ? 'destructive' : 'default'}
          className="font-mono"
        >
          {currentStock}
        </Badge>
      );
    },
  },
  {
    accessorKey: 'unitMetric.displayName',
    header: 'Unit',
    cell: ({ row }) => {
      const unitMetric = row.original.unitMetric;
      const conversionRate = row.original.conversionRate;

      return (
        <div className="text-muted-foreground text-sm">
          {unitMetric ? unitMetric.displayName : ''}
          {conversionRate && ` (1 = ${conversionRate})`}
        </div>
      );
    },
  },
  {
    accessorKey: 'minQuantity',
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          className="h-auto p-0 font-semibold"
        >
          Min Qty
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const minQuantity = row.getValue('minQuantity') as number;
      return <div className="font-mono">{minQuantity}</div>;
    },
  },
  {
    accessorKey: 'isActive',
    header: 'Status',
    cell: ({ row }) => {
      const isActive = row.getValue('isActive') as boolean;
      return (
        <Badge variant={isActive ? 'default' : 'secondary'}>
          {isActive ? 'Active' : 'Inactive'}
        </Badge>
      );
    },
    filterFn: (row, id, value) => {
      if (value === 'all') return true;
      const isActive = row.getValue(id) as boolean;
      return value === 'active' ? isActive : !isActive;
    },
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => {
      const item = row.original;
      const canDelete = !item.adjustments?.length &&
                       !item.stockHistory?.length &&
                       !item.purchaseOrderItems?.length;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => onEdit(item)}>
              <Pencil className="mr-2 h-4 w-4" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                  {canDelete ? (
                    <>
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete
                    </>
                  ) : (
                    <>
                      <Archive className="mr-2 h-4 w-4" />
                      Archive
                    </>
                  )}
                </DropdownMenuItem>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>
                    {canDelete ? 'Delete Item' : 'Archive Item'}
                  </AlertDialogTitle>
                  <AlertDialogDescription>
                    {canDelete ? (
                      <>
                        Are you sure you want to delete "{item.name}"? This action cannot be undone.
                      </>
                    ) : (
                      <>
                        This item has associated records and cannot be deleted.
                        Would you like to archive "{item.name}" instead?
                        Archived items will be hidden from the active inventory but their history will be preserved.
                      </>
                    )}
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={() => onDelete(item)}
                    className={canDelete ? "bg-destructive text-destructive-foreground hover:bg-destructive/90" : ""}
                  >
                    {canDelete ? 'Delete' : 'Archive'}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
